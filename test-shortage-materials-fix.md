# 物料欠料表格数据刷新问题修复

## 问题描述
在 `src/views/shortage-materials/index.vue` 中，当调用 `requestMatterSelectData()` 函数时，表格数据没有正确刷新。

## 问题原因分析
1. **异步处理问题**：`handleSaveOrderConfiguration` 函数中的数据刷新逻辑没有正确处理异步操作
2. **参数传递问题**：刷新数据时没有传递正确的筛选参数
3. **数据重组问题**：只调用了 `requestMatterSelectData()` 但没有重新组装订单数据

## 修复方案

### 1. 修改 `handleSaveOrderConfiguration` 函数
- 将函数改为 `async` 函数
- 添加 `try-catch` 错误处理
- 正确传递物料筛选参数
- 添加数据重组逻辑
- 添加加载状态指示

### 2. 添加加载状态
- 新增 `isRefreshing` 响应式变量
- 在表格上添加 `:loading="isRefreshing"` 属性
- 在数据刷新过程中显示加载状态

### 3. 优化其他相关函数
- 修改 `handleConfirmMaterialsTransferChange` 函数，确保完整的数据刷新流程
- 修改 `handleConfirmSalesOrderTransferChange` 函数，添加订单筛选数据刷新

## 修改内容

### 主要修改点：

1. **handleSaveOrderConfiguration 函数**：
```javascript
const handleSaveOrderConfiguration = async (data, type) => {
  // ... 参数构建逻辑 ...
  
  try {
    isRefreshing.value = true;
    const res = await updOrAddOrderConfigurationAPI(params);
    if (res.code === 200) {
      // 使用当前的物料筛选条件重新获取数据
      await requestMatterSelectData(materialsDialogVisibleData.value);
      // 重新组装订单数据
      await requestMatterSelectSaleOrder();
      console.log("数据刷新完成");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
  } finally {
    isRefreshing.value = false;
  }
};
```

2. **表格加载状态**：
```vue
<vxe-table
  :loading="isRefreshing"
  // ... 其他属性 ...
>
```

3. **物料筛选确认函数**：
```javascript
const handleConfirmMaterialsTransferChange = async () => {
  await requestUpdateMaterialScreeningAPI();
  const materialId = materialsDialogVisibleData.value;
  await requestMatterSelectData(materialId);
  // 重新组装订单数据
  await requestMatterSelectSaleOrder();
  materialsDialogVisible.value = false;
};
```

## 预期效果
1. 保存欠料时间或备注后，表格数据会立即刷新显示最新数据
2. 刷新过程中显示加载状态，提升用户体验
3. 确保数据的完整性和一致性
4. 错误处理更加完善

## 测试建议
1. 测试保存欠料时间后表格是否刷新
2. 测试保存备注后表格是否刷新
3. 测试物料筛选后的数据刷新
4. 测试销售订单筛选后的数据刷新
5. 验证加载状态是否正常显示
