|-- jinze-zhisheng-admin
|-- .browserslistrc # 配置目标浏览器的环境
|-- .dockerignore # 排除不需要上传到 docker 服务端的文件或目录
|-- .editorconfig # 编辑器读取文件格式及样式定义配置 https://editorconfig.org/
|-- .env # 全局环境变量配置（当 .env 文件与 .env.development、.env.production、.env.staging 这三个文件之一存在相同的配置 key 时，.env 优先级更低）
|-- .env.development # 开发环境变量配置
|-- .env.production # 生产环境变量配置
|-- .env.staging # 预发布环境变量配置
|-- .eslintcache #校验缓存文件
|-- .gitignore # git 提交忽略文件
|-- .lintstagedrc # lint-staged 配置文件
|-- .markdownlint.json # markdown 格式检查配置
|-- .npmrc # npm 配置文件
|-- .nvmrc # 用于指定在使用 Node Version Manager（NVM）时要使用的特定 Node.js 版本
|-- .prettierrc.js # prettier 插件配置
|-- .stylelintignore # stylelint 语法检查忽略文件
|-- commitlint.config.js # git 提交前检查配置 校验是否符合提交规范
|-- directoryList.md # 目录结构树
|-- hj.md # 目录结构树
|-- Dockerfile # Docker 镜像构建文件
|-- eslint.config.js # eslint 语法检查配置文件
|-- index.html # html 主入口
|-- instantpage.js # 页面全局加速插件
|-- package.json # 依赖包管理以及命令配置
|-- package.txt # 备份依赖包版本
|-- pnpm-lock.yaml # 依赖包版本锁定文件
|-- postcss.config.js # postcss 插件配置
|-- stylelint.config.js # stylelint 语法检查配置文件
|-- tailwind.config.js # tailwindcss 插件配置
|-- tsconfig.json # typescript 配置
|-- vite.config.ts # vite 配置文件
|-- .husky # 代码提交前校验配置文件
| |-- commit-msg
| |-- common.sh
| |-- pre-commit
| |-- \_
| |-- .gitignore
| |-- applypatch-msg
| |-- commit-msg
| |-- h
| |-- husky.sh
| |-- post-applypatch
| |-- post-checkout
| |-- post-commit
| |-- post-merge
| |-- post-rewrite
| |-- pre-applypatch
| |-- pre-auto-gc
| |-- pre-commit
| |-- pre-push
| |-- pre-rebase
| |-- prepare-commit-msg
|-- .vscode # VSC 工具推荐配置文件
| |-- extensions.json # 一键安装平台推荐的 vscode 插件
| |-- settings.json # 设置扩展程序或 vscode 编辑器的一些属性
| |-- vue3.0.code-snippets # vue3.0 代码片段
| |-- vue3.2.code-snippets # vue3.2 代码片段
| |-- vue3.3.code-snippets # vue3.3 代码片段
|-- build # 构建工具
| |-- cdn.ts # 打包时采用 cdn 模式
| |-- compress.ts # 打包时启用 gzip 压缩或 brotli 压缩
| |-- info.ts # 输出打包信息（大小、用时）
| |-- optimize.ts # vite 依赖预构建配置项
| |-- plugins.ts # vite 相关插件存放处
| |-- utils.ts # 构建工具常用方法抽离
|-- locales # 国际化文件存放处
| |-- en.yaml # 英文配置
| |-- zh-CN.yaml # 中文配置
|-- mock # 模拟接口数据
| |-- asyncRoutes.ts # 模拟后台返回动态路由
| |-- login.ts # 模拟登录接口
| |-- refreshToken.ts # 模拟用户无感刷新token
| |-- system.ts # 模拟后台返回系统配置
|-- public # 静态资源
| |-- favicon.ico
| |-- jocG7HDNgf.txt
| |-- logo png.svg
| |-- logo.png
| |-- logo.svg
| |-- onlead1n.png
| |-- platform-config.json # 全局配置文件（打包后修改也可生效）
| |-- robots.txt # 搜索引擎爬虫配置
| |-- js # js 文件存放处
|-- src
| |-- App.vue
| |-- main.ts
| |-- api # 接口请求统一管理 用于对接接口的目录
| | |-- carousel.ts
| | |-- forecast.ts
| | |-- home.ts
| | |-- mock.ts
| | |-- routes.ts
| | |-- system.ts
| | |-- testing.ts
| | |-- user.ts
| |-- assets # 字体、图片等静态资源
| | |-- user.jpg
| | |-- iconfont
| | | |-- iconfont.css
| | | |-- iconfont.js
| | | |-- iconfont.json
| | | |-- iconfont.ttf
| | | |-- iconfont.woff
| | | |-- iconfont.woff2
| | |-- login
| | | |-- avatar.svg
| | | |-- bg.png
| | | |-- illustration.svg
| | |-- status
| | | |-- 403.svg
| | | |-- 404.svg
| | | |-- 500.svg
| | |-- svg
| | | |-- back_top.svg
| | | |-- dark.svg
| | | |-- day.svg
| | | |-- enter_outlined.svg
| | | |-- exit_screen.svg
| | | |-- full_screen.svg
| | | |-- globalization.svg
| | | |-- keyboard_esc.svg
| | | |-- system.svg
| | |-- table-bar
| | |-- collapse.svg
| | |-- drag.svg
| | |-- expand.svg
| | |-- refresh.svg
| | |-- settings.svg
| |-- components # 自定义通用组件
| | |-- ReAnimateSelector # 选择器组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- animate.ts
| | | |-- index.vue
| | |-- ReAuth # 按钮级别权限组件（根据路由meta中的auths字段进行判断）
| | | |-- index.ts
| | | |-- src
| | | |-- auth.tsx
| | |-- ReCol # 封装 element-plus 的 el-col 组件
| | | |-- index.ts
| | |-- ReCountTo # 数字动画组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- normal
| | | | |-- index.tsx
| | | | |-- props.ts
| | | |-- rebound
| | | |-- index.tsx
| | | |-- props.ts
| | | |-- rebound.css
| | |-- ReCropper # 图片裁剪组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- circled.css
| | | |-- index.tsx
| | | |-- svg
| | | |-- arrow-down.svg
| | | |-- arrow-h.svg
| | | |-- arrow-left.svg
| | | |-- arrow-right.svg
| | | |-- arrow-up.svg
| | | |-- arrow-v.svg
| | | |-- change.svg
| | | |-- download.svg
| | | |-- index.ts
| | | |-- reload.svg
| | | |-- rotate-left.svg
| | | |-- rotate-right.svg
| | | |-- search-minus.svg
| | | |-- search-plus.svg
| | | |-- upload.svg
| | |-- ReCropperPreview # 图片裁剪预览组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- index.vue
| | |-- ReDialog # 基于 element-plus 中 el-dialog 组件开发的函数式弹框封装
| | | |-- index.ts
| | | |-- index.vue
| | | |-- type.ts
| | |-- ReFilterInput # 筛选框组件封装
| | | |-- input.vue # 普通筛选封装 表层
| | | |-- qualityInput.vue # 高级筛选封装 设置层
| | |-- ReIcon # 图标组件封装
| | | |-- data.ts
| | | |-- index.ts
| | | |-- src
| | | |-- hooks.ts
| | | |-- iconfont.ts
| | | |-- iconifyIconOffline.ts
| | | |-- iconifyIconOnline.ts
| | | |-- offlineIcon.ts
| | | |-- Select.vue
| | | |-- types.ts
| | |-- ReImageVerify # 图片验证码组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- hooks.ts
| | | |-- index.vue
| | |-- ReProgress 进度条组件封装
| | | |-- index.vue
| | |-- RePureTableBar # 表格组件封装(使用第三方pure-admin)——不推荐使用该组件
| | | |-- index.ts
| | | |-- src
| | | |-- bar.tsx
| | |-- ReSegmented # 分段控制器组件
| | | |-- index.ts
| | | |-- src
| | | |-- index.css
| | | |-- index.tsx
| | | |-- type.ts
| | |-- ReTable # 全局列表格组件封装
| | | |-- ButtonGroup.vue # 表格按钮组
| | | |-- ColumnSettingDialog.vue #高级排序
| | | |-- Table.vue # 表格组件
| | |-- ReText # 支持 Tooltip 提示的文本省略组件封装
| | | |-- index.ts
| | | |-- src
| | | |-- index.vue
| | |-- ReTranslateComponent # 全国翻译组件封装，目前还有bug，而且需要收费，考虑换别的方法实现动态翻译
| | | |-- cdn.vue
| | | |-- index.vue
| | |-- ReVueFlow # 流程图组件封装
| | |-- beifen.vue
| | |-- controls.css
| | |-- minimap.css
| | |-- style.css
| | |-- theme-default.css
| | |-- 运算流程图(有大bug).vue
| | |-- full-life-cycle # 全生命周期流程图
| | |-- send-laborers-flow # 派工流程图
| | |-- controls.css # 控制器样式
| | |-- index.vue # 全生命周期流程图封装
| | |-- minmap.css # 缩略图样式
| | |-- node-resizer.css # 节点调整大小样式
| | |-- style.css # 流程图样式
| | |-- theme-default.css # 主题样式
| | |-- src #个性化节点定义
| | |-- common-node.vue # 通用节点封装
| | |-- sales-order.vue # 测试组件
| | |-- send-laborers.vue # 派工节点自定义封装
| | |-- warehouse-preparation.vue # 仓库备料节点封装
| | |-- workshop-production.vue #车间生产节点封装
| |-- config # 获取平台动态全局配置
| | |-- index.ts
| |-- directives # 自定义指令
| | |-- index.ts
| | |-- auth # 按钮级别权限指令（根据路由meta中的auths字段进行判断）
| | | |-- index.ts
| | |-- copy # 文本复制指令（默认双击复制）
| | | |-- index.ts
| | |-- longpress # 长按指令
| | | |-- index.ts
| | |-- optimize # 防抖、节流指令
| | | |-- index.ts
| | |-- ripple # 防抖、节流指令
| | |-- index.scss
| | |-- index.ts
| |-- layout # 主要页面布局 （包括侧边栏、面包屑、头部、内容区等封装）这些文件可无需了解。
| | |-- frame.vue
| | |-- index.vue
| | |-- redirect.vue
| | |-- types.ts
| | |-- components
| | | |-- lay-content
| | | | |-- index.vue
| | | |-- lay-footer
| | | | |-- index.vue
| | | |-- lay-frame
| | | | |-- index.vue
| | | |-- lay-navbar
| | | | |-- index.vue
| | | |-- lay-notice
| | | | |-- data.ts
| | | | |-- index.vue
| | | | |-- components
| | | | |-- NoticeItem.vue
| | | | |-- NoticeList.vue
| | | |-- lay-panel
| | | | |-- index.vue
| | | |-- lay-search
| | | | |-- index.vue
| | | | |-- types.ts
| | | | |-- components
| | | | |-- SearchFooter.vue
| | | | |-- SearchHistory.vue
| | | | |-- SearchHistoryItem.vue
| | | | |-- SearchModal.vue
| | | | |-- SearchResult.vue
| | | |-- lay-setting
| | | | |-- index.vue
| | | |-- lay-sidebar
| | | | |-- NavHorizontal.vue
| | | | |-- NavMix.vue
| | | | |-- NavVertical.vue
| | | | |-- components
| | | | |-- SidebarBreadCrumb.vue
| | | | |-- SidebarCenterCollapse.vue
| | | | |-- SidebarExtraIcon.vue
| | | | |-- SidebarFullScreen.vue
| | | | |-- SidebarItem.vue
| | | | |-- SidebarLeftCollapse.vue
| | | | |-- SidebarLinkItem.vue
| | | | |-- SidebarLogo.vue
| | | | |-- SidebarTopCollapse.vue
| | | |-- lay-tag
| | | |-- index.scss
| | | |-- index.vue
| | | |-- components
| | | |-- TagChrome.vue
| | |-- hooks
| | | |-- useBoolean.ts
| | | |-- useDataThemeChange.ts
| | | |-- useLayout.ts
| | | |-- useMultiFrame.ts
| | | |-- useNav.ts
| | | |-- useTag.ts
| | | |-- useTranslationLang.ts
| | |-- theme
| | |-- index.ts
| |-- plugins # 处理一些库或插件，导出更方便的 api
| | |-- echarts.ts
| | |-- elementPlus.ts
| | |-- i18n.ts
| |-- router # 路由配置
| | |-- enums.ts
| | |-- enums.txt
| | |-- index.ts
| | |-- index.txt
| | |-- utils.ts
| | |-- utils.txt
| | |-- modules
| | |-- error.ts
| | |-- home.ts
| | |-- remaining.ts
| | |-- test.ts
| |-- store # pinia 状态管理
| | |-- index.ts
| | |-- types.ts
| | |-- utils.ts
| | |-- modules
| | |-- app.ts
| | |-- epTheme.ts
| | |-- multiTags.ts
| | |-- permission.ts
| | |-- settings.ts
| | |-- user.ts
| |-- style # 全局样式
| | |-- dark.scss
| | |-- element-plus.scss
| | |-- index.scss
| | |-- login.css
| | |-- reset.scss
| | |-- sidebar.scss
| | |-- tailwind.css
| | |-- transition.scss
| |-- utils # 全局工具方法
| | |-- auth.ts # 权限相关(例如登录后token的一个处理)
| | |-- exportXlsx.js # 导出xlsx封装
| | |-- fangouDebugg.js # 禁用debugg调试工具
| | |-- globalPolyfills.ts
| | |-- imageConversion.ts # 图片全局压缩处理
| | |-- message.ts # 消息通知封装
| | |-- mitt.ts
| | |-- preventDefault.ts
| | |-- print.ts
| | |-- propTypes.ts
| | |-- responsive.ts
| | |-- responWidth.ts
| | |-- sso.ts
| | |-- tree.ts
| | |-- windowClose.js # 监听到F12检查或者debugg直接关闭浏览器窗口（未启用）
| | |-- http # http 请求封装
| | | |-- index.ts
| | | |-- types.d.ts
| | |-- localforage # 本地缓存封装
| | | |-- index.ts
| | | |-- types.d.ts
| | |-- progress # 进度条封装 一般用公共组件的即可
| | |-- index.ts
| |-- views # 存放编写业务代码页面
| |-- account-settings # 账户设置(右上角的账户设置)
| | |-- index.vue
| | |-- components
| | |-- AccountManagement.vue
| | |-- Preferences.vue
| | |-- Profile.vue
| | |-- SecurityLog.vue
| |-- analyze-statistics # 统计分析(未做)
| | |-- procurement-warehouse
| | | |-- index.vue
| | |-- quality-control
| | |-- index.vue
| |-- change-audit # 变更审核 (未做)
| | |-- changes-bill-of-material-review
| | | |-- index.vue
| | |-- outsourcing-order-changereview
| | | |-- index.vue
| | |-- purchase-order-change-review
| | |-- index.vue
| |-- common-functions # 常用功能(未做)
| | |-- index.vue
| |-- error
| | |-- 403.vue
| | |-- 404.vue
| | |-- 500.vue
| |-- full-lifecycle # 销售订单全生命周期
| | |-- index.vue # 展示点击后的对应节点的列表
| | |-- src
| | |-- completion-inspection # 完工检验节点列表
| | | |-- index.vue
| | |-- mrp # MRP运算节点列表
| | | |-- index.vue
| | |-- production-in-stock # 生产入库节点列表
| | | |-- index.vue
| | |-- production-order # 生产订单节点列表
| | | |-- index.vue
| | |-- production-order-review # 审核生产订单及用料清单列表
| | | |-- index.vue
| | |-- program-orders # 计划订单节点列表
| | | |-- index.vue
| | |-- sales-orders # 销售订单节点列表
| | | |-- index.vue
| | |-- sell-out # 销售出库节点列表
| | | |-- index.vue
| | |-- send-laborers # 派工节点列表
| | | |-- index.vue
| | |-- warehouse-preparation # 仓库备料节点列表
| | | |-- index.vue
| | |-- workshop-production #车间生产节点列表
| | |-- index.vue
| |-- home # 首页
| | |-- data.ts
| | |-- index.vue
| | |-- utils.ts
| | |-- components
| | |-- charts
| | | |-- ChartBar.vue
| | | |-- ChartLine.vue
| | | |-- ChartRound.vue
| | | |-- index.ts
| | |-- table
| | |-- columns.tsx
| | |-- empty.svg
| | |-- index.vue
| |-- login # 登录页
| | |-- index.vue
| | |-- utils
| | |-- motion.ts
| | |-- rule.ts
| | |-- static.ts
| |-- sales-outbound-monitoring # 销售出库监控
| | |-- index.vue
| |-- system # 系统管理
| | |-- hooks.ts
| | |-- dept # 部门管理
| | | |-- form.vue
| | | |-- index.vue
| | | |-- utils
| | | |-- hook.tsx
| | | |-- rule.ts
| | | |-- types.ts
| | |-- menu # 菜单管理
| | | |-- form.vue
| | | |-- index.vue
| | | |-- README.md
| | | |-- utils
| | | |-- enums.ts
| | | |-- hook.tsx
| | | |-- rule.ts
| | | |-- types.ts
| | |-- role # 角色管理
| | | |-- form.vue
| | | |-- index.vue
| | | |-- components
| | | | |-- PermissionMenu.vue
| | | |-- utils
| | | |-- hook.tsx
| | | |-- rule.ts
| | | |-- types.ts
| | |-- user # 用户管理
| | |-- index.vue
| | |-- tree.vue
| | |-- form
| | | |-- index.vue
| | | |-- role.vue
| | |-- svg
| | | |-- expand.svg
| | | |-- unexpand.svg
| | |-- utils
| | |-- hook.tsx
| | |-- reset.css
| | |-- rule.ts
| | |-- types.ts
| |-- test # 测试文件
| |-- ell-table封装备份.vue
| |-- index.vue
| |-- 备份.txt
| |-- 备份2.txt
| |-- 封装好的.txt
|-- types # 全局 TS 类型配置
|-- directives.d.ts # 全局自定义指令类型声明
|-- global-components.d.ts # 自定义全局组件获得 Volar 提示（自定义的全局组件需要在这里声明下才能获得 Volar 类型提示哦）
|-- global.d.ts # 全局类型声明，无需引入直接在 `.vue` 、`.ts` 、`.tsx` 文件使用即可获得类型提示
|-- index.d.ts # 此文件跟同级目录的 global.d.ts 文件一样也是全局类型声明，只不过这里存放一些零散的全局类型，无需引入直接在 .vue 、.ts 、.tsx 文件使用即可获得类型提示
|-- router.d.ts # 全局路由类型声明
|-- shims-tsx.d.ts # 该文件是为了给 .tsx 文件提供类型支持，在编写时能正确识别语法
|-- shims-vue.d.ts # .vue、.scss 文件不是常规的文件类型，typescript 无法识别，所以我们需要通过下图的代码告诉 typescript 这些文件的类型，防止类型报错
