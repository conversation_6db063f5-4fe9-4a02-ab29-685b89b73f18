// @ts-check

/** @type {import("@commitlint/types").UserConfig} */
export default {
  // 忽略规则：忽略包含 "init" 的提交消息
  ignores: [commit => commit.includes("init")],

  // 扩展：使用 "@commitlint/config-conventional" 作为基础配置
  extends: ["@commitlint/config-conventional"],

  rules: {
    // body-leading-blank: 提交消息主体前必须有一个空行，级别为错误（2），始终（always）要求
    "body-leading-blank": [2, "always"],

    // footer-leading-blank: 提交消息页脚前必须有一个空行，级别为警告（1），始终（always）要求
    "footer-leading-blank": [1, "always"],

    // header-max-length: 提交消息头部（type+scope+subject）的最大长度为 108 个字符，级别为错误（2），始终（always）要求
    "header-max-length": [2, "always", 108],

    // subject-empty: 提交消息主题不能为空，级别为错误（2），始终（always）要求
    "subject-empty": [2, "never"],

    // type-empty: 提交消息类型不能为空，级别为错误（2），始终（always）要求
    "type-empty": [2, "never"],

    // type-enum: 限制提交消息的类型为以下类型之一，级别为错误（2），始终（always）要求
    "type-enum": [
      2,
      "always",
      [
        "feat", // 新功能
        "fix", // 修复 Bug
        "perf", // 性能优化
        "style", // 代码格式（不影响功能）
        "docs", // 文档更新
        "test", // 增加测试
        "refactor", // 代码重构（非新增功能或修复 Bug）
        "build", // 构建过程或辅助工具变动
        "ci", // 持续集成相关变更
        "chore", // 其他更新（不修改 src 或测试文件）
        "revert", // 回退提交
        "wip", // 工作进行中
        "workflow", // 工作流程变动
        "types", // 类型定义更新
        "release" // 版本发布
      ]
    ]
  }
};
