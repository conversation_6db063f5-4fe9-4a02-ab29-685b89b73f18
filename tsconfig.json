{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "experimentalDecorators": true,
    "strictFunctionTypes": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "baseUrl": ".",
    /* 告诉编译器项目中存在js */
    "allowJs": true,
    "resolveJsonModule": true,
    "lib": [
      "ESNext",
      "DOM",
      "ES2015"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@build/*": [
        "build/*"
      ]
    },
    "types": [
      "node",
      "vite/client",
      "element-plus/global",
      "@pureadmin/table/volar",
      "@pureadmin/descriptions/volar"
    ]
  },
  "include": [
    "mock/*.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/*.d.ts",
    "vite.config.ts"
  ],
  "exclude": [
    "dist",
    "**/*.js",
    "node_modules"
  ]
}