{"name": "jinze-<PERSON><PERSON><PERSON>g", "version": "5.7.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "build:prod": "rimraf dist && vite build --mode production", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "2.3.1", "@intlify/unplugin-vue-i18n": "^5.2.0", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.1.2", "@pureadmin/utils": "^2.4.7", "@vitejs/plugin-vue": "^5.1.4", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.41.5", "@vue-flow/minimap": "^1.5.0", "@vueuse/core": "^10.11.0", "@vueuse/motion": "^2.2.3", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.5", "axios": "^1.7.2", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "echarts": "^5.5.0", "element-plus": "^2.7.5", "image-conversion": "^2.1.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "marked": "^15.0.7", "mddir": "^1.1.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.7", "pinyin-pro": "^3.22.0", "plus-pro-components": "^0.1.17", "qs": "^6.12.1", "recordrtc": "^5.6.2", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.2", "version-rocket": "^1.7.4", "vue": "^3.4.27", "vue-i18n": "^9.13.1", "vue-router": "^4.3.3", "vue-tippy": "^6.4.1", "vue-types": "^5.1.2", "vuedraggable": "^2.24.3", "vxe-pc-ui": "4.3.40", "vxe-table": "4.9.33", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.4.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ion": "^1.2.6", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@pureadmin/theme": "^3.2.0", "@rollup/plugin-yaml": "^4.1.2", "@types/gradient-string": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.14.2", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.19", "boxen": "^7.1.1", "cssnano": "^7.0.2", "eslint": "^9.4.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "gradient-string": "^2.0.2", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.2", "rimraf": "^5.0.7", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.5", "stylelint": "^16.6.1", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.0", "svgo": "^3.3.2", "tailwindcss": "^3.4.4", "typescript": "^5.4.5", "vite": "^5.3.0", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-inspector": "^5.1.2", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}