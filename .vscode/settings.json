{
  "editor.formatOnType": true,
  "editor.formatOnSave": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.tabSize": 2,
  "editor.formatOnPaste": true,
  "editor.guides.bracketPairs": "active",
  "files.autoSave": "off",
  "git.confirmSync": false,
  "workbench.startupEditor": "newUntitledFile",
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "css.lint.propertyIgnoredDueToDisplay": "ignore",
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "files.associations": {
    "editor.snippetSuggestions": "top"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "iconify.excludes": [
    "el"
  ],
  "vscodeCustomCodeColor.highlightValue": [
    "v-loading",
    "v-auth",
    "v-copy",
    "v-longpress",
    "v-optimize",
    "v-ripple"
  ],
  "vscodeCustomCodeColor.highlightValueColor": "#b392f0",
  "i18n-ally.localesPaths": [
    "locale",
    "locale/lang"
  ],
}