import { http } from "@/utils/http";
type Result = {
  success: boolean;
  data?: {
    resultList: Array<any>;
  };
};
/** 获取系统管理-菜单管理列表 */
// export const getMenuList = (data?: object)
export const getMenuList = data => {
  return http.request<Result>("get", "/api/sys-menu/list", { params: data });
  // return new Promise(resolve => {
  //   resolve({
  //     success: true,
  //     data: {
  //       resultList: [
  //         /**
  //          * 一级 首页
  //          */
  //         {
  //           parentId: 0,
  //           id: 101,
  //           menuType: 0,
  //           title: "menus.pureHome",
  //           name: "welcome",
  //           path: "/home",
  //           component: "/views/home/<USER>",
  //           rank: 0,
  //           redirect: "",
  //           icon: "",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/home",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: import.meta.env.VITE_HIDE_HOME === "true" ? false : true,
  //           showParent: false
  //         },

  //         /**
  //          * 一级 全生命周期管理
  //          */
  //         {
  //           parentId: 0,
  //           id: 200,
  //           menuType: 0,
  //           title: "systems.pureSalesOrderLifecycle",
  //           name: "FullLifecycles",
  //           path: "/full-lifecycle",
  //           component: "",
  //           rank: 1,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-销售订单
  //         {
  //           parentId: 200,
  //           id: 201,
  //           menuType: 0,
  //           title: "common.pureSalesOrder",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-销售出库
  //         {
  //           parentId: 200,
  //           id: 202,
  //           menuType: 0,
  //           title: "common.pureSalesOutbound",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-生产订单
  //         {
  //           parentId: 200,
  //           id: 203,
  //           menuType: 0,
  //           title: "common.pureProductionOrder",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-生产入库
  //         {
  //           parentId: 200,
  //           id: 204,
  //           menuType: 0,
  //           title: "common.pureProductionWarehousing",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-完工检验
  //         {
  //           parentId: 200,
  //           id: 205,
  //           menuType: 0,
  //           title: "common.pureCompletionInspection",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-车间生产
  //         {
  //           parentId: 200,
  //           id: 206,
  //           menuType: 0,
  //           title: "common.pureWorkshopProduction",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-仓库备料
  //         {
  //           parentId: 200,
  //           id: 207,
  //           menuType: 0,
  //           title: "common.pureWarehouseMaterialPreparation",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-派工
  //         {
  //           parentId: 200,
  //           id: 208,
  //           menuType: 0,
  //           title: "common.pureDispatchWork",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-审核生产订单及用料清单
  //         {
  //           parentId: 200,
  //           id: 209,
  //           menuType: 0,
  //           title: "common.pureReviewProductionOrdersAndMaterialLists",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-计划订单
  //         {
  //           parentId: 200,
  //           id: 210,
  //           menuType: 0,
  //           title: "common.purePlannedOrder",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         // 全生命周期节点-MRP运算
  //         {
  //           parentId: 200,
  //           id: 211,
  //           menuType: 0,
  //           title: "common.pureMRPCalculation",
  //           component: "",
  //           rank: 2,
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },

  //         // 3级页面 - 生产订单
  //         // 流程节点-生产订单-车间入库明细
  //         {
  //           parentId: 203,
  //           id: 20301,
  //           menuType: 0,
  //           title: "systems.pureWorkshopInventoryDetailList",
  //           name: "ProductionInStockDetail",
  //           path: "/full-lifecycle/production-in-stock/production-in-stock-detail",
  //           component:
  //             "full-lifecycle/src/production-in-stock/production-in-stock-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/production-in-stock",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 流程节点-生产订单-车间生产明细
  //         {
  //           parentId: 203,
  //           id: 20302,
  //           menuType: 0,
  //           title: "systems.pureWorkshopProductionDetailList",
  //           name: "WorkshopProductionDetail",
  //           path: "/full-lifecycle/workshop-production/workshop-production-detail",
  //           component:
  //             "full-lifecycle/src/workshop-production/workshop-production-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/workshop-production",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 流程节点-生产订单-完工检验明细
  //         {
  //           parentId: 203,
  //           id: 20303,
  //           menuType: 0,
  //           title: "systems.pureProductionTaskCompletionInspectionDetails",
  //           name: "CompletionInspectionDetail",
  //           path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
  //           component:
  //             "full-lifecycle/src/completion-inspection/completion-inspection-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/production-order/inspection-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 流程节点-生产订单-仓库备料明细
  //         {
  //           parentId: 203,
  //           id: 20304,
  //           menuType: 0,
  //           title: "systems.pureWarehouseMaterialPreparationDetails",
  //           name: "WarehousePreparationDetail",
  //           path: "/full-lifecycle/warehouse-preparation/detail",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/wp-detail/index.vue",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/warehouse-preparation/out-order",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 4级页面-生产订单-仓库备料明细

  //         // 生产订单-仓库备料明细-计划发料单明细列表
  //         {
  //           parentId: 20304,
  //           id: 2040401,
  //           menuType: 0,
  //           title: "systems.pureDetailedListOfPlannedMaterialIssuanceOrders",
  //           name: "PlanMaterialOrderDetail",
  //           path: "/full-lifecycle/plan-material-order-detail",
  //           component:
  //             "full-lifecycle/src/send-laborers/send-laborers-son/plan-material-order/plan-material-order-detail.vue",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/order-picking-sheet-item-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 生产订单-仓库备料明细-拣选明细列表
  //         {
  //           parentId: 20304,
  //           id: 2030402,
  //           menuType: 0,
  //           title: "systems.purePickingDetailsList",
  //           name: "OrderPickingSheetDetail",
  //           path: "/full-lifecycle/order-picking-sheet-detail",
  //           component:
  //             "full-lifecycle/src/send-laborers/send-laborers-son/order-picking-sheet/order-picking-sheet-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/monad-dispatch-situation",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 3级页面 - 完工检验
  //         // 流程节点-完工检验-完工检验明细
  //         {
  //           parentId: 205,
  //           id: 20501,
  //           menuType: 0,
  //           title: "systems.pureProductionTaskCompletionInspectionDetails",
  //           name: "CompletionInspectionDetail",
  //           path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
  //           component:
  //             "full-lifecycle/src/completion-inspection/completion-inspection-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/production-order/inspection-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 某项采购明细相关的入库上架数据
  //         {
  //           parentId: 200,
  //           id: 202,
  //           menuType: 0,
  //           title: "systems.purePurchaseDetailsShelving",
  //           name: "WarehousingShelvesDetail",
  //           path: "/full-lifecycle/warehousingShelves/warehousingShelvesDetail",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/purchase-order-detail/warehousingShelves/warehousingShelvesDetail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/purchase-order",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 车间退补料
  //         {
  //           parentId: 200,
  //           id: 250,
  //           menuType: 0,
  //           title: "systems.pureSummaryMaterialReturnsSupplements",
  //           name: "ReturnProcess",
  //           path: "/full-lifecycle/workshop-production/return-process",
  //           component: "full-lifecycle/src/workshop-production/return-process",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/purchase-order",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 车间退料详情
  //         {
  //           parentId: 200,
  //           id: 208,
  //           menuType: 0,
  //           title: "systems.pureWorkshopReturnDetails",
  //           name: "ReturnDetail",
  //           path: "/full-lifecycle/workshop-production/return-detail",
  //           component:
  //             "full-lifecycle/src/workshop-production/return/return-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/workshop-production/return-process",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 车间补料详情
  //         {
  //           parentId: 200,
  //           id: 209,
  //           menuType: 0,
  //           title: "systems.pureWorkshopReplenishmentDetails",
  //           name: "FillDetail",
  //           path: "/full-lifecycle/workshop-production/fill-detail",
  //           component:
  //             "full-lifecycle/src/workshop-production/fill/fill-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/workshop-production/return-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },
  //         // 仓库备料子节点-采购订单
  //         {
  //           parentId: 200,
  //           id: 210,
  //           menuType: 0,
  //           title: "systems.pureWarehouseMaterialPreparationSubProcess",
  //           name: "preparationPurchaseOrder",
  //           path: "/full-lifecycle/preparation/purchase-order",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/purchase-order/index.vue",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/warehouse-preparation/detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 采购订单明细流程
  //         {
  //           parentId: 200,
  //           id: 211,
  //           menuType: 0,
  //           title: "systems.pureOverallPurchaseOrders",
  //           name: "preparationPurchaseOrderDetail",
  //           path: "/full-lifecycle/preparation/purchase-order-detail",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/purchase-order-detail/index.vue",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/preparation/purchase-order",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },
  //         // 跳转到《某项采购明细相关的来料检验数据》
  //         {
  //           parentId: 211,
  //           id: 241,
  //           menuType: 0,
  //           title: "systems.pureProcurementDetailsIncomingInspectionList",
  //           name: "IncomingInspectionDetail",
  //           path: "/full-lifecycle/incomingInspection/incomingInspectiondetail",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/purchase-order-detail/incomingInspection/incomingInspectiondetail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/incomingInspection",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 跳转到《某项采购明细相关的送货数据》
  //         {
  //           parentId: 211,
  //           id: 242,
  //           menuType: 0,
  //           title: "systems.pureProcurementDetailsDeliveryList",
  //           name: "ProcurementDeliveryDetail",
  //           path: "/full-lifecycle/procurementDelivery/procurementDeliveryDetail",
  //           component:
  //             "full-lifecycle/src/warehouse-preparation/purchase-order-detail/procurementDelivery/procurementDeliveryDetail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/procurementDelivery",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 仓库备料子节点-委外订单
  //         {
  //           parentId: 200,
  //           id: 212,
  //           menuType: 0,
  //           title: "systems.pureOverallOutsourcingOrders",
  //           name: "preparationOutOrder",
  //           path: "/full-lifecycle/warehouse-preparation/out-order",
  //           component: "full-lifecycle/src/warehouse-preparation/out-order",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/preparation/purchase-order-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 派工子流程
  //         {
  //           parentId: 200,
  //           id: 220,
  //           menuType: 0,
  //           title: "systems.pureDispatchSubProcess",
  //           name: "SendLaborersSon",
  //           path: "/full-lifecycle/send-laborers/send-laborers-son",
  //           component: "full-lifecycle/src/send-laborers/send-laborers-son",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/send-laborers/send-laborers-son",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // 单号下的计划派工情况
  //         {
  //           parentId: 200,
  //           id: 221,
  //           menuType: 0,
  //           title: "systems.pureListOfProductionTaskDispatchDetails",
  //           name: "MonadDispatchSituation",
  //           path: "/full-lifecycle/monad-dispatch-situation",
  //           component:
  //             "full-lifecycle/src/send-laborers/monad-dispatch-situation/index.vue",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/send-laborers/send-laborers-son",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: true,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // （点击拣选单号）拣选明细列表
  //         {
  //           parentId: 220,
  //           id: 223,
  //           menuType: 0,
  //           title: "systems.purePickingDetailsListCode",
  //           name: "OrderPickingSheetItemDetail",
  //           path: "/full-lifecycle/order-picking-sheet-item-detail",
  //           component:
  //             "full-lifecycle/src/send-laborers/send-laborers-son/order-picking-sheet/order-picking-sheet-item-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/order-picking-sheet-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         // (点击发料单号)计划发料单明细列表
  //         {
  //           parentId: 220,
  //           id: 225,
  //           menuType: 0,
  //           title:
  //             "systems.pureDetailedListOfPlannedMaterialIssuanceOrdersCode",
  //           name: "PlanMaterialOrderItemDetail",
  //           path: "/full-lifecycle/plan-material-order-item-detail",
  //           component:
  //             "full-lifecycle/src/send-laborers/send-laborers-son/plan-material-order/plan-material-order-item-detail",
  //           rank: null,
  //           redirect: "",
  //           icon: "ri:stackshare-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/full-lifecycle/plan-material-order-detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: false,
  //           showParent: false
  //         },

  //         /**
  //          * 一级 销售出库监控
  //          */
  //         {
  //           parentId: 0,
  //           id: 300,
  //           menuType: 0,
  //           title: "systems.pureStatisticalAnalysis",
  //           name: "AnalysisQuery",
  //           path: "/statistical-analysis-query",
  //           component: "",
  //           rank: 2,
  //           redirect: "",
  //           icon: "ri:rhythm-fill",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/statistical-analysis-query",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 300,
  //           id: 301,
  //           menuType: 0,
  //           title: "systems.pureProcurement_and_WarehouseManagement",
  //           name: "PurchaseWarehouse",
  //           path: "/statistical-analysis-query/purchase/warehouse",
  //           component: "analyze-statistics/procurement-warehouse/index",
  //           rank: 0,
  //           redirect: "",
  //           icon: "ri:archive-2-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/statistical-analysis-query/purchase/warehouse",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 300,
  //           id: 302,
  //           menuType: 0,
  //           title: "systems.pureQualityControlDepartment",
  //           name: "QualityTesting",
  //           path: "/statistical-analysis-query/quality-testing",
  //           component: "analyze-statistics/quality-control/index",
  //           rank: 1,
  //           redirect: "",
  //           icon: "ri:angularjs-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/statistical-analysis-query/quality-testing",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         /**
  //          * 一级 常用功能
  //          */
  //         {
  //           parentId: 0,
  //           id: 400,
  //           menuType: 0,
  //           title: "systems.pureCommonFunctions",
  //           name: "CommonFunctions",
  //           path: "/common-functions",
  //           component: "",
  //           rank: 3,
  //           redirect: "",
  //           icon: "ri:keyboard-box-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/common-functions",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 400,
  //           id: 401,
  //           menuType: 0,
  //           title: "systems.pureCommonFunctions",
  //           name: "CommonFunction",
  //           path: "/common-functions/detail",
  //           component: "/common-functions/detail",
  //           rank: 0,
  //           redirect: "",
  //           icon: "ri:keyboard-box-fill",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/common-functions/detail",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         /**
  //          * 一级 变更审核
  //          */
  //         {
  //           parentId: 0,
  //           id: 500,
  //           menuType: 0,
  //           title: "systems.pureChangeAudit",
  //           name: "ChangeReview",
  //           path: "/change-review",
  //           component: "",
  //           rank: 4,
  //           redirect: "",
  //           icon: "ri:google-play-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/change-review",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 500,
  //           id: 501,
  //           menuType: 0,
  //           title: "systems.pureChangesInBillOfMaterialReview",
  //           name: "MaterialList",
  //           path: "/change-review/material-list",
  //           component: "change-audit/changes-bill-of-material-review/index",
  //           rank: 0,
  //           redirect: "",
  //           icon: "ri:file-list-3-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/change-review/material-list",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 500,
  //           id: 502,
  //           menuType: 0,
  //           title: "systems.purePurchaseOrderChangeReview",
  //           name: "PurchaseOrder",
  //           path: "/change-review/purchase-order",
  //           component: "change-audit/purchase-order-change-review/index",
  //           rank: 1,
  //           redirect: "",
  //           icon: "ri:shape-2-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/change-review/purchase-order",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 500,
  //           id: 503,
  //           menuType: 0,
  //           title: "systems.pureOutsourcingOrderChangeReview",
  //           name: "OutsourcingOrders",
  //           path: "/change-review/outsourcing-orders",
  //           component: "change-audit/outsourcing-order-changereview/index",
  //           rank: 2,
  //           redirect: "",
  //           icon: "ri:shapes-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/change-review/outsourcing-orders",
  //           auths: [""],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         /**
  //          * 一级 系统管理
  //          */
  //         {
  //           parentId: 0,
  //           id: 600,
  //           menuType: 0,
  //           title: "menus.pureSysManagement",
  //           name: "SystemManagement",
  //           path: "/system",
  //           component: "",
  //           rank: 5,
  //           redirect: "",
  //           icon: "ri:settings-3-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/system",
  //           auths: "",
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: false,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 600,
  //           id: 601,
  //           menuType: 0,
  //           title: "menus.pureUser",
  //           name: "SystemUser",
  //           path: "/system/user/index",
  //           component: "/system/user/index",
  //           rank: 0,
  //           redirect: "",
  //           icon: "ri:admin-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/system/user/index",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 600,
  //           id: 602,
  //           menuType: 0,
  //           title: "menus.pureRole",
  //           name: "SystemRole",
  //           path: "/system/role/index",
  //           component: "/system/role/index",
  //           rank: 1,
  //           redirect: "",
  //           icon: "ri:admin-fill",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/system/role/index",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 600,
  //           id: 603,
  //           menuType: 0,
  //           title: "menus.pureSystemMenu",
  //           name: "SystemMenu",
  //           path: "/system/menu/index",
  //           component: "/system/menu/index",
  //           rank: 2,
  //           redirect: "",
  //           icon: "ep:menu",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/system/menu/index",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         },
  //         {
  //           parentId: 600,
  //           id: 604,
  //           menuType: 0,
  //           title: "menus.pureDept",
  //           name: "SystemDept",
  //           path: "/system/dept/index",
  //           component: "/system/dept/index",
  //           rank: 3,
  //           redirect: "",
  //           icon: "ri:git-branch-line",
  //           extraIcon: "",
  //           enterTransition: "",
  //           leaveTransition: "",
  //           activePath: "/system/dept/index",
  //           auths: ["admin"],
  //           frameSrc: "",
  //           frameLoading: true,
  //           keepAlive: true,
  //           hiddenTag: false,
  //           fixedTag: false,
  //           showLink: true,
  //           showParent: false
  //         }
  //       ]
  //     }
  //   });
  //   // resolve(getAllListRole())
  // });
};

/** 获取系统管理-字段管理列表 */
export const getFiledList = data => {
  return http.request<any>("get", "/api/sys-menu/tree", { params: data });
};

/** 菜单管理列表-新增菜单 */
export const addMenu = (data?: object) =>
  http.post("/api/sys-menu/add", { data });

/** 菜单管理列表-修改菜单 */
export const editMenu = (data?: object) =>
  http.put("/api/sys-menu/update", { data });

/** 菜单管理列表-删除菜单 */
export const deleteMenu = (id?: string) =>
  http.delete(`/api/sys-menu/delete/${id}`);

/** 菜单管理列表-修改某个角色权限菜单 */
export const editUserMenu = (data?: Object) =>
  http.post("/api/user/permission", { data });
export const editRoleMenu = (data?: Object) =>
  http.post("/api/roleMenu", { data });
