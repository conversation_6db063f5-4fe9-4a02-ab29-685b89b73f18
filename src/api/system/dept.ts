import { http } from "@/utils/http";

/** 获取部门管理列表 */
export const getDeptList = data => {
  return http.request<any>("get", `/api/dept`, { params: data });
};

/** 部门管理列表添加 */
export const addDeptList = data => {
  return http.request<any>("post", `/api/dept/add`, undefined, {
    data: data
  });
};

/** 部门管理列表修改 */
export const updateDeptList = data => {
  return http.request<any>("put", `/api/dept/upedit`, undefined, {
    data: data
  });
};

/** 部门管理列表删除 */
export const delDeptList = id => {
  return http.request<any>("delete", `/api/dept/${id}`);
};
