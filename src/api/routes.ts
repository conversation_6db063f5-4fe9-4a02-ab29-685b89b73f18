import { http } from "@/utils/http";

type Result = {
  success: boolean;
  data: Array<any>;
};

export const getAsyncRoutes = async () => {
  try {
    const response = await http.request<Result>("get", "/get-async-routes");
    console.log("API Response:", response); // 确认 API 返回的数据
    if (response.data.success) {
      return response.data;
    } else {
      console.error("获取动态路由失败:", response.data);
      return { data: [] }; // 返回一个空数据数组，以防止未定义错误
    }
  } catch (error) {
    console.error("请求错误:", error);
    return { data: [] }; // 返回一个空数据数组，以防止未定义错误
  }
};
