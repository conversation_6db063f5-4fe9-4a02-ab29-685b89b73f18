import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取数据对比结果列表 */
export const getDataComparisonListAPI = data => {
  return http.request<any>(
    "post",
    `/api/data-comparison-result/all`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取数据对比结果字段信息 */
export const getDataComparisonFieldAPI = () => {
  return http.request<any>("get", `/api/data-comparison-result/field`);
};

/** 数据对比结果excel导出 */
export const exportDataComparisonAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/data-comparison-result/all/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
