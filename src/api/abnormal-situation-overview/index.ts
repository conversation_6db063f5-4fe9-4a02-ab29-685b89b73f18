import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取异常情况一览列表 */
export const getAbnormalSituationOverview = data => {
  return http.request<any>("post", `/api/exception-list`, undefined, {
    data: data
  });
};

/** 获取异常情况一览列表1 */
export const getAbnormalSituationOverviewList = data => {
  return http.request<any>("post", `/api/exception-list/list`, undefined, {
    data: data
  });
};

/** 获取异常情况一览表字段信息 */
export const getAbnormalSituationOverviewFieldAPI = () => {
  return http.request<any>("get", `/api/exception-list/field`);
};

/** 批量备注异常情况 */
export const remarkAbnormalSituationAPI = data => {
  return http.request<any>(
    "post",
    `/api/exception-list/exception-update/batch`,
    undefined,
    {
      data: data
    }
  );
};

/** 异常情况一览列表excel导出 */
export const exportAbnormalSituationOverviewAPI = (queryId, query, columns) => {
  return blobHttp.request<any>("post", `/api/exception-list/export`, {
    data: { ...query, names: columns },
    responseType: "blob"
  });
};

/** 批量更新处理状态 */
export const updateAbnormalSituationStatusAPI = data => {
  return http.request<any>(
    "post",
    "/api/exception-list/exception-update/handle-status",
    undefined,
    {
      data
    }
  );
};

export const getAbnormalSituationHandler = formData => {
  return http.request<any>(
    "post",
    `/api/exception-list/exception-update/getHandler`,
    undefined,
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/** 批量更新分配处理人 */
export const updateAbnormalSituationHandlerAPI = data => {
  return http.request<any>(
    "post",
    "/api/exception-list/exception-update/distribute-handler",
    undefined,
    {
      data
    }
  );
};

/** 获取所有异常类型种类 */
export const getErrType = () => {
  return http.request<any>("get", `/api/exception-list/exception/err-type`);
};

// 关于undefined的用法
// const fetchTableData = getFetchTableDataMethod(getAbnormalSituationOverview, {
//   states: abnormalsituationoverviewMap
// });

/** 获取异常跟部门之间的关联 */
export const getDeptExceptionRelation = params => {
  return http.request<any>("get", `/api/dept-exception/list`, { params });
};

export const getRoleExceptionRelation = params => {
  return http.request<any>("get", `/api/role-exception/list`, { params });
};

export const updateDeptExceptionRelation = data => {
  return http.request<any>("post", `/api/dept-exception/update`, { data });
};

export const updateRoleExceptionRelation = data => {
  return http.request<any>("post", `/api/role-exception/update`, { data });
};


/**
 * 获取首页排序
 */
export const getHomeSort = () => {
  return http.request<any>("get", `/api/home-user/select`);
};

/**
 * 更新首页排序
 */
export const updateHomeSort = data => {
  return http.request<any>("post", `/api/home-user/updateOrAdd`, undefined, {
    data
  });
};
