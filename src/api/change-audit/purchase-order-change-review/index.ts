import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取采购订单列表 */
export const getPurchaseOrderChangeAPI = data => {
  return http.request<any>(
    "post",
    `/api/change-review/purchase-order`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取采购订单字段 */
export const getPurchaseOrderChangeFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/change-review/purchase-order/table/info`
  );
};

// 获取采购订单明细列表
export const getPurchaseOrderDetailsAPI = (data, query) => {
  return http.request<any>(
    "post",
    `/api/change-review/purchase-order/detail/${query.billNo}`,
    {
      data: data
    }
  );
};

/** 获取采购订单明细字段 */
export const getPurchaseOrderDetailsFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/change-review/purchase-order-detail/table/info`
  );
};

/** 变更审核采购订单excel导出 */
export const exportChangeReviewPurchaseOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/change-review/purchase-order/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 变更审核采购订单明细excel导出 */
export const exportChangeReviewPurchaseOrderDetailsAPI = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/change-review/detail/purchase-order/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
