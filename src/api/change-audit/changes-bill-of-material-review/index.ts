import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取生产用料清单列表 */
export const getChangesBillOfMaterialAPI = data => {
  return http.request<any>(
    "post",
    `/api/change-review/prod-material-bom`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取生产用料清单字段 */
export const getChangesBillOfMaterialFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/change-review/prod-material-bom/table/info`
  );
};

/** 获取生产用料清单明细列表 */
export const getMaterialListDetailsAPI = (data, query) => {
  return http.request<any>(
    "post",
    `/api/change-review/prod-material-bom/detail/${query.billNo}/${query.prodOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取生产用料清单明细字段 */
export const getMaterialListDetailsFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/change-review/prod-material-bom-detail/table/info`
  );
};

/** 生产用料清单excel导出 */
export const exportChangesBillOfMaterialAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/change-review/prod-material-bom/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 生产用料清单明细excel导出 */
export const exportMaterialListDetailsAPI = (queryId, query, columns) => {
  console.log("queryId变更审核", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/change-review/detail/prod-material-bom/${queryId.billNo}/${queryId.prodOrderNo}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
