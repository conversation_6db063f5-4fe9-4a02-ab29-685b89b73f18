// 生产订单
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取生产订单数据列表
export const getProductionOrderListAPI = (data, id) => {
  return http.request<any>("post", `/api/prod-order/${id}`, {
    data: data
  });
};

// 获取生产订单运算字段信息
export const getProductionOrderFieldsAPI = () => {
  return http.request<any>("get", `/api/prod-order/table/info`);
};

// 求和生产订单求和数据
export const getProductionOrderTotalAPI = (orderId, property, queryData) => {
  return http.request<any>(
    "post",
    `/api/prod-order/sum/${orderId.queryId}/${property}`,
    undefined,
    {
      data: queryData // 将查询数据传递给接口
    }
  );
};

/** 生产订单excel导出 */
export const exportProductionOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/prod-order/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
