import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取销售订单表字段信息 */
export const getSaleOrderKeys = () => {
  return http.request<any>("get", `/api/sale-order/table/info`);
};

/** 获取销售订单表字段订单明细信息 */
export const getSaleOrderDetailKeys = () => {
  return http.request<any>("get", `/api/sale-order/table/detail/info`);
};

/** 获取销售订单表字段订单排序信息 */
export const getSaleOrderSequence = tableId => {
  return http.request<any>("get", `/api/header-sort/${tableId}`);
};

/** 获取销售订单明细列表 */
export const getSaleOrderSequenceDetail = (data, orderId) => {
  return http.request<any>(
    "post",
    `/api/sale-order/detail/${orderId}`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取销售订单列表（数组形式） */
export const getSaleOrder = data => {
  return http.request<any>("post", `/api/sale-order`, undefined, {
    data: data
  });
};

/** 更新表头排序 */
export const updateSaleOrderSequence = table => {
  return http.request<any>(
    "post",
    `/api/header-sort/${table.tableId}`,
    undefined,
    {
      data: table.tableColumns
    }
  );
};

/** 获取销售订单求和 */
export const getSaleOrderTotalAPI = (property, name, queryData) => {
  return http.request<any>(
    "post",
    `/api/sale-order/detail/sum/${property.queryId}/${name}`,
    undefined,
    {
      data: queryData // 将查询数据传递给接口
    }
  );
};

/** 销售订单明细excel导出 */
export const exportSaleOrderDetailAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/sale-order/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 销售订单excel导出 */
export const exportSaleOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>("post", `/api/sale-order/export`, {
    data: { ...query, names: columns },
    responseType: "blob"
  });
};
