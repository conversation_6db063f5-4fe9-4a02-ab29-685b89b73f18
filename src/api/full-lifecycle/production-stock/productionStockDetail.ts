// 生产入库
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取某个生产任务单号的车间入库明细列表
export const getProductionStockDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/batch-work-shop-detail/detail/${queryId}`,
    {
      data: data
    }
  );
};

// 获取某个生产任务单号的批次车间入库明细字段
export const getProductionStockDetailTableAPI = () => {
  return http.request<any>(
    "get",
    `/api/batch-work-shop-detail/detail/table/info`
  );
};

/** 某个生产任务单号的批次车间入库明细excel导出 */
export const exportProductionStocDetailsAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/batch-work-shop-detail/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

// 求和某个生产任务单号的车间入库明细数据
export const getProductionStockDetailTotalAPI = (
  { queryId },
  property,
  queryData
) => {
  return http.request<any>(
    "post",
    `/api/batch-work-shop-detail/detail/sum/${queryId}/${property}`,
    undefined,
    {
      data: queryData // 将查询数据传递给接口
    }
  );
};
