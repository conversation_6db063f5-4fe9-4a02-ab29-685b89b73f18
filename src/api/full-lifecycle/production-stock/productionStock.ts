// 生产入库
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取生产入库数据列表
export const getProductionStockListAPI = (data, orderId) => {
  return http.request<any>("post", `/api/batch-work-shop-detail/${orderId}`, {
    data: data
  });
};

// 获取生产入库运算字段信息
export const getProductionStockFieldsAPI = () => {
  return http.request<any>("get", `/api/batch-work-shop-detail/table/info`);
};

// 求和生产入库求和数据
export const getProductionStockTotalAPI = (
  { queryId },
  property,
  queryData
) => {
  return http.request<any>(
    "post",
    `/api/batch-work-shop-detail/sum/${queryId}/${property}`,
    undefined,
    {
      data: queryData // 将查询数据传递给接口
    }
  );
};

/** 批次车间入库excel导出 */
export const exportProductionStockAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/batch-work-shop-detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
