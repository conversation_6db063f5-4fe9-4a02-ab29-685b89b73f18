import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取销售出库求和 */
export const getSellOutTotalAPI = (orderId, name, query?: any[] | []) => {
  return http.request<any>(
    "post",
    `/api/sale-outbound/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取销售出库列表
export const getSellOutList = (data, orderId) => {
  return http.request<any>("post", `/api/sale-outbound/${orderId}`, undefined, {
    data: data
  });
};

// 获取销售出库字段信息
export const getSellOutTableList = () => {
  return http.request<any>("get", `/api/sale-outbound/table/info`);
};

/** 销售出库excel导出 */
export const exportSellOutAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/sale-outbound/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
