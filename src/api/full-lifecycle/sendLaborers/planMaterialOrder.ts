import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和创建计划发料单数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getPlanMaterialOrderTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/creation-pi/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取创建计划发料单列表
 * @param data 查询条件
 * @param saleOrderNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getPlanMaterialOrderAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/creation-pi/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取创建计划发料单字段信息
 * @returns {data: any[]} 返回数据
 */
export const getPlanMaterialOrderTableAPI = () => {
  return http.request<any>("get", `/api/dispatch-work/creation-pi/table/info`);
};

/**
 * @function 获取求和创建计划发料单明细数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getPlanMaterialOrderTotalDetailAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/planned/issue/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取创建计划发料单明细列表
 * @param data 查询条件
 * @param saleOrderNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getPlanMaterialOrderDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/planned/issue/detail/${queryId}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取创建计划发料单明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getPlanMaterialOrderTableDetailAPI = () => {
  return http.request<any>(
    "get",
    `/api/dispatch-work/planned/issue/detail/table/info`
  );
};

/** 计划发料单明细excel导出*/
export const exportPlanMaterialOrderTableDetailAPI = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/planned/issue/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 创建计划发料excel导出*/
export const exportPlanMaterialOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/creation-pi/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
