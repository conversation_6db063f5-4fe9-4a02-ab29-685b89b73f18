import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和计划派工数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getPlanDispatchWorkersTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/plan/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取计划派工列表
 * @param data 查询条件
 * @param saleOrderNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getPlanDispatchWorkersAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/plan/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取计划派工字段信息
 * @returns {data: any[]} 返回数据
 */
export const getPlanDispatchWorkersTableAPI = () => {
  return http.request<any>("get", `/api/dispatch-work/plan/table/info`);
};

/** 计划派工excel导出 */
export const exportPlanDispatchWorkersAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/plan/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
