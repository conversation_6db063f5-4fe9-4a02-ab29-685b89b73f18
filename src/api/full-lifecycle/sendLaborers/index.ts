// 派工
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取派工求和 */
export const getSendLaborersTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取派工列表
export const addSendLaborersAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取派工字段信息
export const getDispatchWorkersTableList = () => {
  return http.request<any>("get", `/api/dispatch-work/table/info`);
};

/** 派工数据全部导出 */
export const exportDispatchWorkersAPI = (queryId, query, columns) => {
  console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

export * from "./orderPickingSheet";
export * from "./planDispatchWorkers";
export * from "./orderPickingSheet";
