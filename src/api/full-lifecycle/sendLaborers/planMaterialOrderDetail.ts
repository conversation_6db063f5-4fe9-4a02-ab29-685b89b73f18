import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和某个物料的计划发料单明细数据 */
export const getSomePlanMaterialOrderTotalAPI = (
  { queryId, materialCode },
  name,
  query: any[]
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/pi/material/sum/${queryId}/${materialCode}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取某个物料的计划发料单明细列表
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getSomePlanMaterialOrderDetailAPI = (data, query) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/pi/material/${query.queryId}/${query.materialCode}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取某个物料的计划发料单明细字段信息
/**
 * @function 获取某个物料的计划发料单明细字段信息 WorkshopReturnMaterial
 * @param data 查询条件
 * @returns {records: any[]} 返回数据
 */
export const getSomePlanMaterialOrderTableAPI = () => {
  return http.request<any>("get", `/api/dispatch-work/pi/material/table/info`);
};

/** 某个物料的计划发料单明细excel导出*/
export const exportSomePlanMaterialOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/pi/material/${queryId.queryId}/${queryId.materialCode}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
