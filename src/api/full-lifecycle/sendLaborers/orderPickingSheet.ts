import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和创建拣选单数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getOrderPickingSheetTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/creation-pick-order/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取创建拣选单列表
 * @param data 查询条件
 * @param saleOrderNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getOrderPickingSheetAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/creation-pick-order/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取创建拣选单字段信息
 * @returns {data: any[]} 返回数据
 */
export const getOrderPickingSheetTableAPI = () => {
  return http.request<any>(
    "get",
    `/api/dispatch-work/creation-pick-order/table/info`
  );
};

/**
 * @function 获取求和拣选单明细数据
 * @param orderId 拣选单号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getOrderPickingSheetDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/pick/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取拣选单明细列表
 * @param data 查询条件
 * @param saleOrderNo 拣选单号
 * @returns {records: any[]} 返回数据
 */
export const getOrderPickingSheetDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/dispatch-work/pick/detail/${queryId}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取拣选单明细数据字段
 * @returns {data: any[]} 返回数据
 */
export const getOrderPickingSheetTableDetailAPI = () => {
  return http.request<any>("get", `/api/dispatch-work/pick/detail/table/info`);
};

/** 创建拣选单excel导出*/
export const exportOrderPickingSheetAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/creation-pick-order/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 拣选单明细excel导出*/
export const exportOrderPickingSheetDetailAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/dispatch-work/pick/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
