import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 求和某个生产任务单号的完工检验明细数据
 * @param orderId 生产任务单号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getInspectionCompletionDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/completion-inspect/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取某个生产任务单号的完工检验明细列表
 * @param data 查询条件
 * @param saleOrderNo 生产任务单号
 * @returns {records: any[]} 返回数据
 */
export const getInspectionCompletionDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/completion-inspect/detail/${queryId}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取某个生产任务单号的完工检验明细字段
 * @returns {data: any[]} 返回数据
 */
export const getInspectionCompletionDetailTableAPI = () => {
  return http.request<any>("get", `/api/completion-inspect/detail/table/info`);
};

/** 某个生产任务单号的完工检验明细excel导出 */
export const exportInspectionCompletionDetailAPI = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/completion-inspect/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
