// 派工
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和完工检验数据 */
export const getInspectionCompletionsTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/completion-inspect/sum/${orderId}/${name}`,
    {
      data: query
    }
  );
};

// 获取完工检验列表
export const getInspectionCompletionAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/completion-inspect/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取完工检验字段
export const getInspectionCompletionTableAPI = () => {
  return http.request<any>("get", `/api/completion-inspect/table/info`);
};

/** 完工检验excel导出 */
export const exportInspectionCompletionAPI = (queryId, query, columns) => {
  console.log("queryId变更审核", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/completion-inspect/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
