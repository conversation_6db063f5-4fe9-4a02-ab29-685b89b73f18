import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取审核订单及用料列表
export const getOrderAndMaterialList = data => {
  return http.request<any>(
    "post",
    `/api/review-prod-order-and-material`,
    undefined,
    {
      data: data
    }
  );
};
// 获取审核订单及用料字段信息
export const getOrderAndMaterialTableList = () => {
  return http.request<any>(
    "get",
    `/api/review-prod-order-and-material/table/info`
  );
};

// 求和审核订单及用料数据
export const addReviewAndmaterial = (property, queryData) => {
  return http.request<any>(
    "post",
    `/api/review-prod-order-and-material/sum/${property}`,
    undefined,
    {
      data: queryData // 将查询数据传递给接口
    }
  );
};

/** 审核订单及用料excel导出 */
export const exportMReviewAndmaterialAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/review-prod-order-and-material/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
