// 派工
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和车间生产数据 */
export const getWorkshopProductionsTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取车间生产列表
export const getWorkshopProductionAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取车间生产字段信息
export const getWorkshopProductionTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/table/info`);
};

/** 车间生产excel导出 */
export const exportWorkshopProductionAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
