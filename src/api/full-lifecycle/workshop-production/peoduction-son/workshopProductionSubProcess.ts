// 车间生产 - 子流程
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和退补料汇总数据 */
export const getFeedBackMaterialTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/summary/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取退补料汇总列表
export const getFeedBackMaterialAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/summary/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取退补料汇总字段信息
export const getFeedBackMaterialTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/summary/table/info`);
};

/** 退补料汇总excel导出 */
export const exportFeedBackMaterialAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/summary/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
