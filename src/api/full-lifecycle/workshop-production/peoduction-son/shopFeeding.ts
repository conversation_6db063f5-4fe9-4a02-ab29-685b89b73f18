// 车间生产 - 子流程
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和车间补料数据 */
export const getShopFeedingTotalAPI = (orderId, name, query?: any[] | []) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/feed/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取车间补料列表
export const getShopFeedingAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/feed/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取车间补料字段信息
export const getShopFeedingTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/feed/table/info`);
};

/** 车间补料excel导出 */
export const exportShopFeedingAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/feed/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
