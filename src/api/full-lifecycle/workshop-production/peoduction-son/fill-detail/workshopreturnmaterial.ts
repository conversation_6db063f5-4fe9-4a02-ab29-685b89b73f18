// 车间生产 - 子流程 - 车间补料明细
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和车间补料明细数据 */
export const getFillDetailTotalAPI = (queryTotal, name, query: any[]) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/feed/details/sum/${queryTotal.productionOrderNo}/${queryTotal.materialCode}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取车间补料明细列表
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getFillDetailAPI = (data, query) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/feed/details/${query.productionOrderNo}/${query.materialCode}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取车间补料明细字段信息
/**
 * @function 获取车间补料明细列表 WorkshopReturnMaterial
 * @param data 查询条件
 * @returns {records: any[]} 返回数据
 */
export const getFillDetailTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/feed/details/table/info`);
};

/** 车间补料明细excel导出 */
export const exportFillDetailAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/feed/details/${queryId.productionOrderNo}/${queryId.materialCode}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
