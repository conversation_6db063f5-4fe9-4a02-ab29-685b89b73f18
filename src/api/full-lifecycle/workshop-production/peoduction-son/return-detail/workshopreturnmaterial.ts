// 车间生产 - 子流程 - 车间退料明细
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和车间退料明细数据 */
export const getReturnMaterialDetailTotalAPI = (
  queryParams,
  name,
  query: any[]
) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/return/details/sum/${queryParams.queryId}/${queryParams.materialCode}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取车间退料明细列表 WorkshopReturnMaterial
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getWorkshopReturnMaterialDetailAPI = (data, query) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/return/details/${query.queryId}/${query.materialCode}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取车间退料明细字段信息
/**
 * @function 获取车间退料明细列表 WorkshopReturnMaterial
 * @param data 查询条件
 * @returns {records: any[]} 返回数据
 */
export const getWorkshopReturnMaterialDetailTableAPI = () => {
  return http.request<any>(
    "get",
    `/api/workshop-prod/return/details/table/info`
  );
};

/** 车间退料明细excel导出 */
export const exportWorkshopReturnMaterialDetailAPI = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/return/details/${queryId.queryId}/${queryId.materialCode}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
