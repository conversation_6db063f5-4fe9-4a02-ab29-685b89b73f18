// 车间生产 - 子流程
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和车间退料数据 */
export const getWorkshopReturnMaterialTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/return/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取车间退料列表 WorkshopReturnMaterial
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getWorkshopReturnMaterialAPI = (data, saleOrderNo) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/return/${saleOrderNo}`,
    undefined,
    {
      data: data
    }
  );
};

// 获取车间退料详情字段信息
/**
 * @function 获取车间退料列表 WorkshopReturnMaterial
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getWorkshopReturnMaterialTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/return/table/info`);
};

/** 车间退料excel导出 */
export const exportWorkshopReturnMaterialAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/return/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
