import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 求和某个生产任务单号的车间生产明细数据
 * @param orderId 生产任务单号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getWorkshopProductionsDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取某个生产任务单号的车间生产明细列表
 * @param data 查询条件
 * @param saleOrderNo 生产任务单号
 * @returns {records: any[]} 返回数据
 */
export const getWorkshopProductionDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/workshop-prod/detail/${queryId}`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取某个生产任务单号的车间生产明细字段
 * @returns {data: any[]} 返回数据
 */
export const getWorkshopProductionDetailTableAPI = () => {
  return http.request<any>("get", `/api/workshop-prod/detail/table/info`);
};

/** 车间生产明细excel导出 */
export const exportWorkshopProductionDetailAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/workshop-prod/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
