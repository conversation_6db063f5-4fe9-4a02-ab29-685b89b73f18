import { http } from "@/utils/http";

/** 获取生产订单及用料进度 */
export const getProductOrderProgress = id => {
  return http.request<any>(
    "get",
    `/api/review-prod-order-and-material/progress/${id}`
  );
};

/** 获取MRP用料进度 */
export const getMRPProgress = id => {
  return http.request<any>("get", `/api/mrp-calc/progress/${id}`);
};

/** 获取计划订单进度 */
export const getPlanOrderProgress = id => {
  return http.request<any>("get", `/api/plan-order/progress/${id}`);
};

/** 获取派工进度 */
export const getDispatchWorkersProgress = id => {
  return http.request<any>("get", `/api/dispatch-work/progress/${id}`);
};

/** 获取仓库备料进度 */
export const getWarehousePreparationProgress = id => {
  return http.request<any>("get", `/api/store-material-prep/progress/${id}`);
};

/** 获取车间生产进度 */
export const getWorkshopProductionProgress = id => {
  return http.request<any>("get", `/api/workshop-prod/progress/${id}`);
};

/** 获取完工检验进度 */
export const getInspectionCompletionProgress = id => {
  return http.request<any>("get", `/api/completion-inspect/progress/${id}`);
};

// 获取生产入库进度条
export const getProductionStockProgress = saleOrderNo => {
  return http.request<any>(
    "get",
    `/api/batch-work-shop-detail/order-summary/${saleOrderNo}`
  );
};

// 获取生产订单进度条
export const getProductionOrderProgress = saleOrderNo => {
  return http.request<any>("get", `/api/prod-order/progress/${saleOrderNo}`);
};

// 获取销售出库进度条
export const getSellOutProgress = saleOrderNo => {
  return http.request<any>("get", `/api/sale-outbound/progress/${saleOrderNo}`);
};

// 获取销售订单进度条
export const getSaleOrderProgress = saleOrderNo => {
  return http.request<any>(
    "get",
    `/api/sale-order/detail/progress/${saleOrderNo}`
  );
};

/** 仓库备料子流程 - 采购订单 */
/** 获取采购订单进度 */
export const getPurchaseOrderProgress = saleOrderNo => {
  return http.request<any>(
    "get",
    `/api/purchase-order/progress/${saleOrderNo}`
  );
};
/** 获取委外订单进度 */
export const getOutOrderProgress = saleOrderNo => {
  return http.request<any>("get", `/api/sub-order/progress/${saleOrderNo}`);
};
