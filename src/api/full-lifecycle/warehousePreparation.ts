// 派工
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 求和仓库备料数据 */
// export const gettWarehousePreparationsTotalAPI = (name, query?: any[] | []) => {
//   return http.request<any>("post", `/api/store-material-prep/sum/${name}`, {
//     data: query
//   });
// };

/** 求和仓库备料数据 */
export const gettWarehousePreparationsTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/store-material-prep/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

// 获取仓库备料列表`
export const getWarehousePreparationAPI = (data, q) => {
  return http.request<any>("post", `/api/store-material-prep/${q}`, undefined, {
    data: data
  });
};

// 获取仓库备料字段信息
export const getWarehousePreparationTableAPI = () => {
  return http.request<any>("get", `/api/store-material-prep/table/info`);
};

/** 仓库备料excel导出 */
export const exporttWarehousePreparationAPI = (queryId, query, columns) => {
  console.log("queryId仓库备料", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/store-material-prep/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
