import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取MRP运算列表
export const getMrpCalculationList = (data, orderId = 0) => {
  orderId;
  return http.request<any>("post", `/api/mrp-calc`, undefined, {
    data: data
  });
};

// 求和MRP运算数据
export const addMrpCalculationList = (property, queryData) => {
  return http.request<any>("post", `/api/mrp-calc/sum/${property}`, undefined, {
    data: queryData // 将查询数据传递给接口
  });
};

// 获取MRP运算字段信息
export const getMrpAddList = () => {
  return http.request<any>("get", `/api/mrp-calc/table/info`);
};

// 获取MRP运算进度
export const getMrpOperationSchedule = saleOrderNo => {
  return http.request<any>("get", `/api/mrp-calc/progress/${saleOrderNo}`);
};

/** MRP运算excel导出 */
export const exportMrpOperationAPI = (queryId, query, columns) => {
  console.log("queryId变更审核", queryId);

  return blobHttp.request<any>("post", `/api/mrp-calc/export`, {
    data: { ...query, names: columns },
    responseType: "blob"
  });
};
