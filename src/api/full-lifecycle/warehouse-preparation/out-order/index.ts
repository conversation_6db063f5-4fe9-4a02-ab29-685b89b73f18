import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";
/**
 * @function 获取委外订单列表 PurchaseOrder
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getOutOrderAPI = (data, billNo) => {
  return http.request<any>("post", `/api/sub-order/${billNo}`, {
    data: data
  });
};

/**
 * @function 获取委外订单字段信息
 * @returns {data: any[]} 返回数据
 */
export const getOutOrderFieldAPI = () => {
  return http.request<any>("get", `/api/sub-order/table/info`);
};

/**
 * @function 获取委外订单字段信息
 * @returns {data: any[]} 返回数据
 */
export const getOutOrderFieldByTableIdAPI = tableId => {
  return http.request<any>("get", `/api/sub-order/${tableId}`);
};

/**
 * @function 获取求和委外订单数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getOutOrderAPITotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>("post", `/api/sub-order/sum/${queryId}/${name}`, {
    data: query
  });
};

/** 委外订单excel导出 */
export const exportOutOrderAPI = (queryId, query, columns) => {
  // console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/sub-order/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
/**
 * 采购订单四个子节点节点进度
 * @param saleOrderNo
 */
export const getOutOrderProgressAPI = (saleOrderNo: string) => {
  return http.request<{ data: number }>(
    "get",
    `/api/sub-order/subProgress/${saleOrderNo}`
  );
};
