// 仓库备料-明细
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和仓库备料明细
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getWPDetailAPITotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/store-material-prep/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取仓库备料明细列表 PurchaseOrder
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getWPDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/store-material-prep/detail/${queryId}`,
    {
      data: data
    }
  );
};

/**
 * @function 获取仓库备料字段信息
 * @returns {data: any[]} 返回数据
 */
export const getWPDetailFieldAPI = () => {
  return http.request<any>("get", `/api/store-material-prep/detail/table/info`);
};

/** 仓库备料明细excel导出 */
export const exporttWarehousePreparationAPI = (queryId, query, columns) => {
  console.log("queryId仓库备料明细", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/store-material-prep/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
