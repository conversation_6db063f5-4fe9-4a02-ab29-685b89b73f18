// 仓库备料-采购订单-总体-采购送货
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取采购送货求和数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryAPITotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/delivery/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取采购送货列表<->仓库备料-采购订单-总体-采购送货
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getProcurementDeliveryAPI = (data, billNo) => {
  return http.request<any>("post", `/api/purchase-order/delivery/${billNo}`, {
    data: data
  });
};

/**
 * @function 获取采购送货字段信息
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryFieldAPI = () => {
  return http.request<any>("get", `/api/purchase-order/delivery/table/info`);
};

/** 采购送货excel导出 */
export const exportProcurementDeliveryAPI = (queryId, query, columns) => {
  console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/purchase-order/delivery/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
