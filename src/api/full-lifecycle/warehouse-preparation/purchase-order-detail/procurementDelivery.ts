// 仓库备料-采购订单-明细-采购送货
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取采购送货明细求和数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryDetailTotalAPI = (
  queryId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/delivery/detail/sum/${queryId.queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取采购送货列表<->仓库备料-采购订单-明细-采购送货
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getProcurementDeliveryDetailAPI = (data, queryId) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/delivery/detail/${queryId.queryId}`,
    {
      data: data
    }
  );
};

/**
 * @function 获取采购送货明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/purchase-order/delivery/detail/table/info`
  );
};

/** 采购送货明细excel导出 */
export const exportProcurementDeliveryDetailAPI = (queryId, query, columns) => {
  console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/purchase-order/delivery/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
