// 仓库备料-采购订单-明细-来料检验
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和来料检验明细数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getIncomingInspectionDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/inspection/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取入库上架列表<->仓库备料-采购订单-明细-来料检验
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getIncomingInspectionDetailAPI = (data, { queryId }) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/inspection/detail/${queryId}`,
    {
      data: data
    }
  );
};

/**
 * @function 获取来料检验明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getIncomingInspectionDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/purchase-order/inspection/detail/table/info`
  );
};

/** 来料检验明细excel导出 */
export const exportIncomingInspectionDetailAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/purchase-order/inspection/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
