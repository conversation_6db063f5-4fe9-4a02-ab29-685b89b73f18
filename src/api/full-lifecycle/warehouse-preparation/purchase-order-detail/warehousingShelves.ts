// 仓库备料-采购订单-明细-入库上架
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和入库上架明细数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getWarehousingShelvesDetailTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/put-away/detail/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取入库上架列表<->仓库备料-采购订单-明细-入库上架
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getWarehousingShelvesDetailAPI = (data, billNo) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/put-away/detail/${billNo.queryId}`,
    {
      data: data
    }
  );
};

/**
 * @function 获取入库上架明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getWarehousingShelvesDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/purchase-order/put-away/detail/table/info`
  );
};

/** 入库上架明细excel导出 */
export const exportWarehousingShelvesDetailAPI = (queryId, query, columns) => {
  console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/purchase-order/put-away/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
