// 仓库备料-采购订单-明细
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 获取求和采购订单明细数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getPurchaseOrderDetailTotalAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/detail/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取采购订单明细列表 PurchaseOrder
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getPurchaseOrderDetailAPI = (data, billNo) => {
  return http.request<any>(
    "post",
    `/api/purchase-order/detail/${billNo.queryId}`,
    {
      data: data
    }
  );
};

/**
 * @function 获取采购订单明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getPurchaseOrderDetailFieldAPI = () => {
  return http.request<any>("get", `/api/purchase-order/detail/table/info`);
};

/** 采购订单明细excel导出 */
export const exportPurchaseOrderDetailsAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/purchase-order/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/**
 * 获取采购订单四个子节点的进度条
 * @param saleOrderNo
 */
export const getPurchaseOrderProgressAPI = (saleOrderNo: string) => {
  return http.request<{ data: number }>(
    "get",
    `/api/purchase-order/subProgress/${saleOrderNo}`
  );
};
