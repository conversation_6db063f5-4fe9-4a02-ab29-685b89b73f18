import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";
/**
 * @function 获取委外订单明细列表 PurchaseOrder
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getOutOrderDetailAPI = (data, billNo) => {
  return http.request<any>("post", `/api/sub-order/detail/${billNo}`, {
    data: data
  });
};

/**
 * @function 获取委外出库明细列表
 * @param data 查询条件
 * @param outsourcingWorkOrderCode 委外工单编号
 * @returns {records: any[]} 返回数据
 */
export const getOutsourcingDetailAPI = (data, outsourcingWorkOrderCode) => {
  return http.request<any>(
    "post",
    `/api/sub-order/delivery/outsourcing/detail/${outsourcingWorkOrderCode}`,
    {
      data: data
    }
  );
};

/** 获取委外出库明细求和 */
export const getWarehouseSumPutAwayAPI = (
  orderId,
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/sub-order/delivery/outsourcing/detail/sum/${orderId.queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取委外订单明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getOutOrderDetailFieldAPI = () => {
  return http.request<any>("get", `/api/sub-order/detail/table/info`);
};

/**
 * @function 获取委外出库明细字段信息
 * @returns {data: any[]} 返回数据
 */
export const getOutsourcingDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/sub-order/delivery/outsourcing/detail/table/info`
  );
};

/**
 * @function 获取求和委外订单m数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getOutOrderDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/sub-order/detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取求和委外订单m数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getOutsourcingDetailTotalAPI = (
  { saleOrderNo },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/sub-order/delivery/outsourcing/sum/${saleOrderNo}/${name}`,
    {
      data: query
    }
  );
};

/** 委外订单excel导出 */
export const exportOutOrderAPI = (queryId, query, columns) => {
  // console.log("queryIdpaigon", queryId);

  return blobHttp.request<any>(
    "post",
    `/api/sub-order/detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/**
 * 采购订单四个子节点节点进度
 * @param saleOrderNo
 */
export const getOutOrderDetailProgressAPI = (saleOrderNo: string) => {
  return http.request<{ data: number }>(
    "get",
    `/api/sub-order/subProgress/detail/${saleOrderNo}`
  );
};
