// 仓库备料-采购订单-总体-来料检验
import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/**
 * @function 求和委外订单明细-采购送货数据
 * @param orderId 订单编号
 * @param name 求和字段名
 * @param query 查询参数
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryDetailTotalAPI = (
  { queryId },
  name,
  query?: any[] | []
) => {
  return http.request<any>(
    "post",
    `/api/sub-order/delivery/procurement-detail/sum/${queryId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * @function 获取委外订单明细-采购送货列表
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getProcurementDeliveryDetailTableAPI = (data, billNo) => {
  console.log("billNo---", billNo);

  return http.request<any>(
    "post",
    `/api/sub-order/delivery/procurement-detail/${billNo.queryId}`,
    {
      data: data
    }
  );
};

/**s
 * @function 获取委外订单-采购送货字段信息
 * @returns {data: any[]} 返回数据
 */
export const getProcurementDeliveryDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/sub-order/delivery/procurement-detail/table/info`
  );
};

/** 来料检验excel导出 */
export const exportIncomingInspectionAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/sub-order/delivery/procurement-detail/${queryId.queryId}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
