import { http } from "@/utils/http";
/** 首页卡片 */

/** 销售订单准交率 */
export const getSaleOrderOnTimeRateAPI = date =>
  http.get(
    `/api/system/statistic/${date.startDate}/${date.endDate}/sale-order-deliverable-rate`
  );

/** 工厂生产的良品率 */
export const getFactoryProdYieldRateAPI = date =>
  http.get(
    `/api/system/statistic/${date.startDate}/${date.endDate}/factory-prod-yield-rate`
  );

/** 计划达成率 */
export const getPlanAchievementRateAPI = date =>
  http.get(
    `/api/system/statistic/${date.startDate}/${date.endDate}/plan-achievement-rate`
  );

/** 销售订单数量 一月内、三月内、半年内、一年内*/
export const getSalesOrderCountByPeriodAPI = async () => {
  enum dateEnum {
    "1_month" = "1个月内",
    "3_months" = "3个月内",
    "6_months" = "6个月内",
    "1_year" = "12个月内"
  }

  const res = await http.get(`/api/system/statistic/sales-orders-metric`);

  // 转换为月份
  const mappedData = res.data.map(item => {
    // 复制原始 item 数据
    const mappedItem = {
      ...item,
      total: `${item.order_count} / \n ${item.total_sales_qty}`
    };

    // 对比 period 和 dateEnum，修改对应的键名
    if (dateEnum[item.period]) {
      // 修改 period 字段的值为对应的中文
      mappedItem.period = dateEnum[item.period];
    }

    return mappedItem;
  });

  // 一个辅助函数，用来将中文月份转为对应的数字
  const monthToNumber = (month: string) => {
    const months = {
      "1月": 1,
      "3月": 3,
      "6月": 6,
      "12月": 12
    };
    return months[month] || 0; // 默认返回 0
  };

  // 排序：根据 period 的中文月份来排序
  mappedData.sort((a, b) => {
    const monthA = monthToNumber(a.period); // 转换 a.period 为对应的数字
    const monthB = monthToNumber(b.period); // 转换 b.period 为对应的数字
    return monthA - monthB; // 按照数字大小排序
  });

  return new Promise(resolve => {
    resolve({
      data: mappedData
    });
  });
};

/** 7日临期销售订单数量 
    3天、7天、15天*/
export const getSevenDayExpiringSaleOrderAPI = async () => {
  const dateArray = [3, 7, 15];
  // promise数组
  const requestArray = dateArray.map(item => {
    return http.get(`/api/system/statistic/sale-order/${item}/expired`);
  });

  // 集中请求
  const res = await Promise.all(requestArray);
  const returnDateArray = res.map((item, index) => {
    return {
      date: dateArray[index] + "天临期",
      total: item.data
    };
  }); // console.log(returnDateArray, "res");

  return { data: returnDateArray };
};

/** 各个分类id的销售数据销量 */
export const getCategorySalesNumberAPI = async date => {
  const defaultData = [
    {
      pinbPrdType: "铝材+五金",
      totalQuantity: 0
    },
    {
      pinbPrdType: "其他",
      totalQuantity: 0
    },
    {
      pinbPrdType: "玻璃",
      totalQuantity: 0
    },
    {
      pinbPrdType: "底盆+淋浴柱+顶盖",
      totalQuantity: 0
    },
    {
      pinbPrdType: "缸",
      totalQuantity: 0
    },
    {
      pinbPrdType: "洗手盆",
      totalQuantity: 0
    }
  ];
  const res = await http.get(
    `/api/system/statistic/${date.startDate}/${date.endDate}/category-sales-number`
  );

  return new Promise(resolve => {
    resolve({
      data: res.data.length > 0 ? res.data : defaultData
    });
  });
};
