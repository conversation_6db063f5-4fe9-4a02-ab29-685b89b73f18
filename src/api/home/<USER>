import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取销售订单表字段信息 */
export const getSaleOrderKeys = () => {
  return http.request<any>("get", `/api/sale-order/table/info`);
};

/** 获取销售订单表字段订单信息 */
export const getSaleOrderDetailKeys = () => {
  return http.request<any>("get", `/api/sale-order/table/info`);
};

/** 获取销售订单表字段订单排序信息 */
export const getSaleOrderSequence = tableId => {
  return http.request<any>("get", `/api/header-sort/${tableId}`);
};

/** 获取销售订单列表 */
export const getSaleOrder = data => {
  return http.request<any>("post", `/api/sale-order`, undefined, {
    data: data
  });
};

/** 获取销售订单下拉列表 */
export const getSaleOrderSelect = () => {
  return http.request<any>("get", `/api/sale-order/getSaleOrderListBill`);
};

/** 更新表头排序 */
export const updateSaleOrderSequence = table => {
  return http.request<any>(
    "post",
    `/api/header-sort/${table.tableId}`,
    undefined,
    {
      data: table.tableColumns
    }
  );
};

/** 销售订单excel导出 */
export const exportSaleOrderAPI = (queryId, query, columns) => {
  return blobHttp.request<any>("post", `/api/sale-order/export`, {
    data: { ...query, names: columns },
    responseType: "blob"
  });
};

/** 交易异常备注 */
export const addSaleExceptionInformation = data => {
  return http.request<any>(
    "post",
    `/api/sale-order/addSaleExceptionInformation`,
    undefined,
    {
      data: data
    }
  );
};
