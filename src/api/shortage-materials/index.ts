import { http } from "@/utils/http";

/** 获取物料欠料列表  */
export const getMaterialShortageDataAPI = () => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situation/three_sales_orders`,
    undefined,
    {
      timeout: 60000 * 10
    }
  );
};

/**
 * 查询所有订单
 */
export const getAllOrderAPI = params => {
  return http.request<any>(
    "get",
    `/api/materia-shortage-situation/getAllOrder`,
    params
  );
};

/**
 * 查询订单欠料情况
 */
export const getOrderShortageAPI = data => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situation/sales_orders`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 查询采购明细列表
 */
export const getPurchaseDetailAPI = data => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situatio/sales_orders/${data.materiaId}`,
    undefined,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      data: data
    }
  );
};

/**
 * 获取订单筛选
 */
export const getOrderScreeningAPI = () => {
  return http.request<any>("get", `/api/order-screening/select`);
};

/**
 * 更新订单筛选
 */
export const updateOrderScreeningAPI = data => {
  return http.request<any>(
    "post",
    `/api/order-screening/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 获取物料筛选
 */
export const getMaterialScreeningAPI = () => {
  return http.request<any>("get", `/api/material-screening/select`);
};

/**
 * 更新物料筛选
 */
export const updateMaterialScreeningAPI = data => {
  return http.request<any>(
    "post",
    `/api/material-screening/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 默认获取物料欠料列表
 */
export const getMatterSelectAPI = data => {
  return http.request<any>("post", `/api/matter/select`, undefined, {
    data: data
  });
};

/**
 * 获取全部物料欠料列表
 */
export const getMatterSelectALLAPI = data => {
  return http.request<any>("post", `/api/matter/select/all`, undefined, {
    data: data
  });
};

/**
 * 获取全部销售订单号列表
 */
export const getMatterSelectSaleOrderAPI = () => {
  return http.request<any>("get", `/api/matter/select/saleOrder`);
};

/**
 * 获取全部物料编码列表
 */
export const getMatterMaterialIdAPI = () => {
  return http.request<any>("get", `/api/matter/select/getMatterMaterialId`);
};
