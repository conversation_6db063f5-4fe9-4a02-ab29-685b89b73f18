import { http } from "@/utils/http";

/** 获取销售订单列表 */
export const getSaleOrderArr = data => {
  return http.request<any>("post", `/api/sale-order/detail/arr`, undefined, {
    data: data
  });
};

/** 各供应商物料来料合格率 */
export const getQualifiedMaterialsSupplierAPI = (data, date) => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/supplier-material-qualified-rate/${date[0]}/${date[1]}`,
    undefined,
    {
      data: data
    }
  );
};

/** 各物料来料合格率 */
export const getPassMaterialIncomingAPI = (data, date) => {
  console.log(date, "date");

  return http.request<any>(
    "post",
    `/api/statistic/purchasing/material-qualified-rate/${date[0]}/${date[1]}`,
    undefined,
    {
      data: data
    }
  );
};

/** 统计每种生产物料的合格率及不合格率 */
export const getMaterialPassFailRateStatisticsAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/production-material/2020-02-14/2025-2-18`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取统计每种生产物料的合格率及不合格率字段 */
export const getMaterialQualityRates = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/production-material/field`
  );
};

/** 供应商批次合格率 */
export const getSupplierBatchPassRateAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/supplier-batch/2024-02-14/2025-02-18`,
    undefined,
    {
      data: data
    }
  );
};
/** 按产品类型统计某个时段中产品的合格率 */
export const getPassRateByProductTypeForPeriodAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/product-type/2024-02-14/2025-02-18`,
    undefined,
    {
      data: data
    }
  );
};

/** 各个供应商各个物料的来料合格率和不合格率字段 */
export const getQualifiedMaterialsSupplierFieldsAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/supplier-material-qualified-rate/field`,
    undefined,
    {}
  );
};

/** 获取各物料来料合格率字段 */
export const getPassMaterialIncomingFieldsAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-qualified-rate/field`,
    undefined,
    {}
  );
};
