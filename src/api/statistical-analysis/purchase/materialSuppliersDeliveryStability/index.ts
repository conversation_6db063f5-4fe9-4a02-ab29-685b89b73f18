import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各物料各供应商交期稳定性 */
export const getMaterialSuppliersDeliveryStabilityAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/sup-mat-delivery-stability/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 各物料各供应商交期稳定性 */
export const getMaterialSuppliersDeliveryStabilityAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/sup-mat-delivery-stability/getMaterialSupplierDeliveryStability`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取各物料各供应商交期稳定性字段 */
export const getMaterialSuppliersDeliveryStabilityFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-delivery-stability/field`
  );
};

/** 各物料各供应商交期稳定性excel导出 */
export const exportMaterialSuppliersDeliveryStabilityAPI = (
  date,
  query,
  columns
) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/sup-mat-delivery-stability/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
