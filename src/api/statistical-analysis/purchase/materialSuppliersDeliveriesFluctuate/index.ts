import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各个供应商各个物料的交付波动天数 */
export const getMaterialSuppliersDeliveriesFluctuateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/sup-mat-delivery-fluctuation/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};
/** 各个供应商各个物料的交付波动天数 */
export const getMaterialSuppliersDeliveriesFluctuateAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/sup-mat-delivery-fluctuation/getMaterialSupplierDeliveryFluctuation`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取各个供应商各个物料的交付波动天数字段 */
export const getMaterialSuppliersDeliveriesFluctuateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/sup-mat-delivery-fluctuation/field`
  );
};

/** 各个供应商各个物料的交付波动天数excel导出 */
export const exportMaterialSuppliersDeliveriesFluctuateAPI = (
  date,
  query,
  columns
) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/sup-mat-delivery-fluctuation/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
