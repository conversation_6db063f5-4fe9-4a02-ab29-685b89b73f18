import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各物料来料合格率 */
export const getQualifiedRateIncomingMaterialsAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/material-qualified-rate/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};
/** 各物料来料合格率 */
export const getQualifiedRateIncomingMaterialsAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/material-qualified-rate/SupplierMaterialIncomingList`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取各物料来料合格率字段 */
export const getQualifiedRateIncomingMaterialsFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-qualified-rate/field`
  );
};

/** 各物料来料合格率excel导出 */
export const exportQualifiedRateIncomingMaterialsAPI = (
  date,
  query,
  columns
) => {
  const [startDate, endDate] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/supplier-material-qualified-rate/${startDate}/${endDate}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
