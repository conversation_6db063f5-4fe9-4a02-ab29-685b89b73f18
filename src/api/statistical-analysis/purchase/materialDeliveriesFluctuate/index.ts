import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各个物料某时间段交付波动天数 */
export const getMaterialDeliveriesFluctuateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/material-delivery-fluctuation/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};
/** 各个物料某时间段交付波动天数 */
export const getMaterialDeliveriesFluctuateAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/material-delivery-fluctuation/getMaterialDeliveryFluctuation`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取各个物料某时间段交付波动天数字段 */
export const getMaterialDeliveriesFluctuateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-delivery-fluctuation/field`
  );
};

/** 各个物料某时间段交付波动天数excel导出 */
export const exportMaterialDeliveriesFluctuateAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/material-delivery-fluctuation/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
