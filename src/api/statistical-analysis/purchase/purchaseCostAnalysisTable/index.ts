import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各个供应商各个物料的平均采购价格 */
export const getSupplierMaterialAvgPurchasePriceAPI = (data, year) => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price/${year}`,
    undefined,
    {
      data: data
    }
  );
};
/** 各个供应商各个物料的平均采购价格 */
export const getSupplierMaterialAvgPurchasePriceAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price/PurchaseCostAnalysisDeatilList`,
    undefined,
    {
      data: data
    }
  );
};

/** 各个物料某年的采购均价 */
export const getMaterialAnnualAveragePriceAPI = (data, year) => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price-material/${year}`,
    undefined,
    {
      data: data
    }
  );
};

/** 各个物料某年的采购均价 */
export const getMaterialAnnualAveragePriceAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price-material/PurchaseCostAnalysisList`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取各个物料某年的采购均价字段 */
export const getMaterialAnnualAveragePriceFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/average-purchase-price-material/field`,
    undefined,
    {}
  );
};

/** 获取各个供应商各个物料的平均采购价格字段 */
export const getSupplierMaterialAvgPurchasePriceFiledAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/average-purchase-price/field`,
    undefined,
    {}
  );
};

/** 各个供应商各个物料的平均采购价格excel导出 */
export const exportSupplierMaterialAvgPurchasePriceAPI = (
  year,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price/${year.date}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 各个物料某年的采购均价excel导出 */
export const exportMaterialAnnualAveragePriceAPI = (year, query, columns) => {
  console.log("yearfu", year.date.value);

  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/average-purchase-price-material/${year.date.value}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
