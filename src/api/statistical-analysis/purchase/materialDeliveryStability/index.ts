import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各个物料的交期稳定性 */
export const getMaterialDeliveryStabilityAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/material-delivery-stability/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取各个物料的交期稳定性字段 */
export const getMaterialDeliveryStabilityFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-delivery-stability/field`
  );
};

/** 各个物料的交期稳定性excel导出 */
export const exportMaterialDeliveryStabilityAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/material-delivery-stability/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
