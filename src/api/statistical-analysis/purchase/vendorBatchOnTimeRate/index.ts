import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 供应商批次准交率 */
export const getBatchOnTimeRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/batch-delivery-rate/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 供应商批次准交率 */
export const getBatchOnTimeRateAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/batch-delivery-rate/ProcurementDetailRateList`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取供应商批次准交率字段 */
export const getBatchOnTimeRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/batch-delivery-rate/field`
  );
};

/** 供应商批次准交率excel导出
 *
 */
export const exportBatchOnTimeAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/batch-delivery-rate/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
