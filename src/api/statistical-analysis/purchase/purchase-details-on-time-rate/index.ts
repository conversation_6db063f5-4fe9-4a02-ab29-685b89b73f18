import { http } from "@/utils/http";

/** 采购明细准交率 */
export const getPurchaseDetailsOnTimeRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/supplier-batch/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取采购明细准交率字段 */
export const getPurchaseDetailsOnTimeRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/supplier-batch/field`
  );
};
