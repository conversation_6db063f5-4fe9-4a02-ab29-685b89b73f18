import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 各供应商物料来料合格率 */
export const getQualifiedMaterialsSupplierAPI = (data, date) => {
  if (date[0] && date[1]) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/supplier-material-qualified-rate/${date[0]}/${date[1]}`,
      undefined,
      {
        data: data
      }
    );
  }
};
/** 各供应商物料来料合格率 */
export const getQualifiedMaterialsSupplierAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/supplier-material-qualified-rate/supplierMaterialQualifiedRateList`,
    undefined,
    {
      data: data
    }
  );
};

/** 各物料来料合格率 */
export const getPassMaterialIncomingAPI = (data, date) => {
  console.log(date, "date");
  if (date[0] && date[1]) {
    return http.request<any>(
      "post",
      `/api/statistic/purchasing/material-qualified-rate/${date[0]}/${date[1]}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 各物料来料合格率 */
export const getPassMaterialIncomingAPINoDate = data => {
  return http.request<any>(
    "post",
    `/api/statistic/purchasing/material-qualified-rate/SupplierMaterialIncomingList`,
    undefined,
    {
      data: data
    }
  );
};

/** 各个供应商各个物料的来料合格率和不合格率字段 */
export const getQualifiedMaterialsSupplierFieldsAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/supplier-material-qualified-rate/field`,
    undefined,
    {}
  );
};

/** 获取各物料来料合格率字段 */
export const getPassMaterialIncomingFieldsAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/purchasing/material-qualified-rate/field`,
    undefined,
    {}
  );
};

/** 各个供应商各个物料的来料合格率和不合格率excel导出 */
export const exportQualifiedMaterialsSupplierAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/supplier-material-qualified-rate/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 各物料来料合格率excel导出 */
export const exportPassMaterialIncomingAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/purchasing/material-qualified-rate/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
