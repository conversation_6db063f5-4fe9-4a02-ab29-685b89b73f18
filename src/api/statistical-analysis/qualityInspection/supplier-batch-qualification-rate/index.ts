import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 供应商批次合格率 */
export const getSupplierBatchPassAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/quality-inspection/supplier_pass_rate/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 供应商批次合格率字段 */
export const getSupplierBatchPassRatesAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/supplier_pass_rate/field`
  );
};

/** 供应商批次合格率excel导出 */
export const exportSupplierBatchPassRatesAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/quality-inspection/supplier_pass_rate/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
