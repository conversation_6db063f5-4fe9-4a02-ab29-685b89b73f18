import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 新增班组生产良品率 */
export const getTeamProductionYieldRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/quality-inspection/factory-prod-yield-rate/new-team/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取新增班组生产良品率字段 */
export const getTeamProductionYieldRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/factory-prod-yield-rate/new-team/field`
  );
};

/** 供应商批次合格率excel导出 */
export const exportTeamProductionYieldRateAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/quality-inspection/factory-prod-yield-rate/new-team/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
