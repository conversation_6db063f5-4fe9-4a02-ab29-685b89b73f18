import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 按产品类型统计某个时段中产品的合格率 */
export const getPassRateByProductTypeForPeriodAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/quality-inspection/product-type/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取按产品类型统计某个时段中产品的合格率字段 */
export const getPassRateByProductTypeForPeriodFiledAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/product-type/field`
  );
};

/** 按产品类型统计某个时段中产品的合格率excel导出 */
export const exportPassRateByProductTypeForPeriodAPI = (
  date,
  query,
  columns
) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/quality-inspection/product-type/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
