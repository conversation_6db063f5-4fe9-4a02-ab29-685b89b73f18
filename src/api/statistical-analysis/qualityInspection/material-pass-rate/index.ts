import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 统计每种生产物料的合格率及不合格率 */
export const getMaterialPassFailRateStatisticsAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/quality-inspection/production-material/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取统计每种生产物料的合格率及不合格率字段 */
export const getMaterialQualityRatesAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/production-material/field`
  );
};

/** 统计每种生产物料的合格率及不合格率excel导出 */
export const exportMaterialQualityRatesAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/quality-inspection/production-material/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
