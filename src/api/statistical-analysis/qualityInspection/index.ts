import { http } from "@/utils/http";

/** 获取销售订单列表 */
export const getSaleOrderArr = data => {
  return http.request<any>("post", `/api/sale-order/detail/arr`, undefined, {
    data: data
  });
};

/** 各供应商物料来料合格率 */
export const getQualifiedMaterialsSupplierAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/supplier-material-qualified-rate/2024-02-14/2025-02-18`,
    undefined,
    {
      data: data
    }
  );
};

/** 各物料来料合格率 */
export const getPassMaterialIncomingAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/material-qualified-rate/2024-02-14/2025-02-18`,
    undefined,
    {
      data: data
    }
  );
};

/** 统计每种生产物料的合格率及不合格率 */
export const getMaterialPassFailRateStatisticsAPI = (data, date) => {
  const [start, end] = date.date.value;
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/production-material/${start}/${end}`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取统计每种生产物料的合格率及不合格率字段 */
export const getMaterialQualityRatesAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/production-material/field`
  );
};

/** 供应商批次合格率 */
export const getSupplierBatchPassAPI = (data, date) => {
  const [start, end] = date.date.value;
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/supplier-batch/${start}/${end}`,
    undefined,
    {
      data: data
    }
  );
};
/** 供应商批次合格率字段 */
export const getSupplierBatchPassRatesAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/supplier-batch/field`
  );
};

/** 按产品类型统计某个时段中产品的合格率 */
export const getPassRateByProductTypeForPeriodAPI = (data, date) => {
  const [start, end] = date.date.value;
  return http.request<any>(
    "post",
    `/api/statistic/quality-inspection/product-type/${start}/${end}`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取按产品类型统计某个时段中产品的合格率字段 */
export const getPassRateByProductTypeForPeriodFiledAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/product-type/field`
  );
};
