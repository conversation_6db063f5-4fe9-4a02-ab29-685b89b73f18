import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 新增生产车间生产良品率 */
export const getWorkshopProductionYieldRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/quality-inspection/factory-prod-yield-rate/new-prod-workshop/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取新增生产车间生产良品率字段 */
export const getWorkshopProductionYieldRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/quality-inspection/factory-prod-yield-rate/new-prod-workshop/field`
  );
};

/** 车间生产良品率excel导出 */
/** 供应商批次合格率excel导出 */
export const exportWorkshopProductionYieldRateAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/quality-inspection/factory-prod-yield-rate/new-prod-workshop/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
