import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

// 获取库存与短期消耗预测报表
export const getInventoryShortTermAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/warehousing/inventory-and-short-term`,
    undefined,
    {
      data: data
    }
  );
};

// 获取获取库存与短期消耗预测报表字段
export const getInventoryShortTermFields = () => {
  return http.request<any>(
    "get",
    `/api/statistic/warehousing/inventory-and-short-term/field`
  );
};

// 库存与短期消耗预测报表excel导出
export const exportInventoryShortTermExcel = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/statistic/warehousing/inventory-and-short-term/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

// 获取库存供需追踪与短缺分析报表
export const getInventoryTrackingShortagesAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/warehousing/inventory-demand-tracking-shortage`,
    undefined,
    {
      data: data
    }
  );
};

// 获取库存供需追踪与短缺分析报表字段
export const getInventoryTrackingShortagesFields = () => {
  return http.request<any>(
    "get",
    `/api/statistic/warehousing/inventory-demand-tracking-shortage/field`
  );
};

// 库存供需追踪与短缺分析报表excel导出
export const exportInventoryTrackingShortagesExcel = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>(
    "post",
    `/api/statistic/warehousing/inventory-demand-tracking-shortage/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

// 获取物料入库上架的完成情况统计字段
export const getMaterialsArePutOnShelfFields = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/put-on/field/info`
  );
};

/** 获取物料入库上架的完成情况统计 */
export const getMaterialsArePutOnShelfAPI = data => {
  return http.request<any>(
    "post",
    `/api/sale-outbound-monitor/put-on`,
    undefined,
    {
      data: data
    }
  );
};

// 获取物料入库上架的完成情况统计excel导出
export const exportMaterialsArePutOnShelfExcel = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/sale-outbound-monitor/put-on/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

// 物料入库上架的完成情况统计-入库上架达成率
export const getStockingAchievementRate = date => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/put-on-rate/${date[0]}/${date[1]}`
  );
};

// 物料入库上架的完成情况统计-应入库上架批次数
export const getPlannedStockingBatchCount = date => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/put-on-qty/${date[0]}/${date[1]}`
  );
};
// 物料入库上架的完成情况统计-未入库上架批次数
export const getUnstockedBatches = date => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/no-put-on-qty/${date[0]}/${date[1]}`
  );
};
// 物料入库上架的完成情况统计-超期入库上架批次数
export const getOverdueStockingBatches = date => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/extended-put-on-qty/${date[0]}/${date[1]}`
  );
};

// 获取车间退补料情况
export const getReturnProcessTotalAPI = data => {
  return http.request<any>(
    "post",
    `/api/statistic/warehousing/return-process-total`,
    undefined,
    {
      data: data
    }
  );
};

// 获取获取库存与短期消耗预测报表字段
export const getReturnProcessTotalFields = () => {
  return http.request<any>(
    "get",
    `/api/statistic/warehousing/return-process-total/field`
  );
};

// 库存与短期消耗预测报表excel导出
export const exportReturnProcessTotalExcel = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/statistic/warehousing/return-process-total/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 求和退补料汇总数据 */
export const getReturnProcessTotalSummaryAPI = (name, query?: any[] | []) => {
  return http.request<any>(
    "post",
    `/api/statistic/warehousing/return-process-total/summary/sum/${name}`,
    {
      data: query
    }
  );
};
