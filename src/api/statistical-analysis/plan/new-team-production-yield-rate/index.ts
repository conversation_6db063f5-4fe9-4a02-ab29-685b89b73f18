import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 新增班组计划达成率 */
export const getNewTeamProductionYieldRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/planning/plan-achievement-rate/new-team/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取新增班组计划达成率字段 */
export const getNewTeamProductionYieldRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/planning/plan-achievement-rate/new-team/field`
  );
};

/** 新增班组计划达成率excel导出 */
export const exportNewTeamProductionAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/planning/plan-achievement-rate/new-team/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
