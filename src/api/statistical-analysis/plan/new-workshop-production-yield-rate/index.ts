import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 新增生产车间达成率 */
export const getNewWorkshopProductionYieldRateAPI = (data, date) => {
  const [start, end] = date.date.value;
  if (start && end) {
    return http.request<any>(
      "post",
      `/api/statistic/planning/plan-achievement-rate/new-prod-workshop/${start}/${end}`,
      undefined,
      {
        data: data
      }
    );
  }
};

/** 获取新增生产车间达成率字段 */
export const getNewWorkshopProductionYieldRateFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/statistic/planning/plan-achievement-rate/new-prod-workshop/field`
  );
};

/** 新增生产车间达成率excel导出 */
export const exportNewWorkshopProductionAPI = (date, query, columns) => {
  const [start, end] = date.date.value;
  return blobHttp.request<any>(
    "post",
    `/api/statistic/planning/plan-achievement-rate/new-prod-workshop/${start}/${end}/export`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};
