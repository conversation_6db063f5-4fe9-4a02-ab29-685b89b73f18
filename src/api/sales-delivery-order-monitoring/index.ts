import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取销售出库单监控表头 */
export const getSalesDeliveryOrderMonitoringListAPI = data => {
  return http.request<any>("post", `/api/sale-outbound-monitor`, undefined, {
    data: data
  });
};

/** 获取销售出库单监控表头字段 */
export const getSalesDeliveryOrderMonitoringFieldAPI = () => {
  return http.request<any>("get", `/api/sale-outbound-monitor/field/info`);
};

/** 表头-逾期未检验数量 */
export const getNumberOfOverdueInspectionsAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/expired-untested-qty`
  );
};

/** 表头-3天临期未检验数量 */
export const getHeadThreeDaysTheNumberOfPendingTestsAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/expired-untested-qty/3`
  );
};

/** 表头-7天临期未检验数量 */
export const getHeadSevenDaysTheNumberOfPendingTestsAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/expired-untested-qty/7`
  );
};

/** 表头-未完成第三方检验数量 */
export const getHeaderNumberOfThirdPartyTestsNotCompletedAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/not-completed-tp-verified`
  );
};

/** 销售出库单监控表excel导出 */
export const exportSalesDeliveryOrderMonitoringAPI = (
  queryId,
  query,
  columns
) => {
  return blobHttp.request<any>("post", `/api/sale-outbound-monitor/export`, {
    data: { ...query, names: columns },
    responseType: "blob"
  });
};

/** 到港时间修改 */
export const updateArrivalTimeAPI = (dateTime, id) => {
  return http.request<any>(
    "post",
    `/api/sale-outbound-monitor/statistic/arrival-date/${dateTime}`,
    {
      data: { id }
    }
  );
};

/** 可以批量选择数据 */
export const updateSalesDeliveryOrderMonitorAPI = data => {
  return http.request<any>(
    "post",
    "/api/sale-outbound-monitor/statistic/whether-completed",
    undefined,
    {
      data
    }
  );
};
