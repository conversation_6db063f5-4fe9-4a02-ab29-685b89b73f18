import { http } from "@/utils/http";
import { blobHttp } from "@/utils/http/blobHttp";

/** 获取销售出库单明细 */
export const getSalesDeliveryOrderDetailsAPI = (data, query) => {
  console.log("query----", query);

  return http.request<any>(
    "post",
    `/api/sale-outbound-monitor/detail/${query.queryId}`,
    undefined,
    {
      data: data
    }
  );
};

/** 获取销售出库单明细字段 */
export const getSalesDeliveryOrderDetailsFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/detail/field/info`
  );
};

/** 逾期未检验数量 */
export const getNumberOfOverdueInspectionsDetailAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/overdue-quantity`
  );
};

/**3临期未检验数量 */
export const getHeadThreeDaysTheNumberOfPendingTestsDetailAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/unsuspected-quantity`
  );
};

/** 7临期未检验数量 */
export const getHeadSevenDaysTheNumberOfPendingTestsDetailAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/unsuspected-quantity`
  );
};

/** 第三方检验完成率 */
export const getHeaderNumberOfThirdPartyTestsNotCompletedDetailAPI = () => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/statistic/inspection-completion-rate`
  );
};

/** 销售出库单明细excel导出 */
export const exportSalesDeliveryOrderDetailsAPI = (queryId, query, columns) => {
  return blobHttp.request<any>(
    "post",
    `/api/sale-outbound-monitor/detail/export/${queryId.queryId}`,
    {
      data: { ...query, names: columns },
      responseType: "blob"
    }
  );
};

/** 可以批量选择数据 */
export const updateSalesDeliveryOrderMonitoringAPI = data => {
  return http.request<any>(
    "post",
    "/api/sale-outbound-monitor/statistic/bulk-selection",
    undefined,
    {
      data
    }
  );
};

/** 图片上传 */
export const uploadImageAPI = (id, file) => {
  return http.request<any>(
    "post",
    `/api/sale-outbound-monitor/detail/${id}/image`,
    undefined,
    {
      data: file,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/** 获取图片 */
export const getImageAPI = id => {
  return http.request<any>(
    "get",
    `/api/sale-outbound-monitor/detail/${id}/image`
  );
};

/**
 * 图片删除
 */
export const deleteImageAPI = imageId => {
  return http.request<any>(
    "delete",
    `/api/sale-outbound-monitor/detail/image/${imageId}`
  );
};
