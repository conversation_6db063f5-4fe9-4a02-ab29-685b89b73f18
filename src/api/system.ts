import { http } from "@/utils/http";
import { faker } from "@faker-js/faker/locale/zh_CN";
type Result = {
  success: boolean;
  data?: {
    resultList: Array<any>;
  };
};

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
    records: Array<any>;
    current?: number;
    pages?: number;
    size?: number;
  };
};

/** 获取系统管理-用户管理列表 */
export const getUserList = (data?: object) => {
  console.log("data8888888888888", data);

  return new Promise(resolve => {
    let list = [
      {
        avatar: "https://avatars.githubusercontent.com/u/44761321",
        username: "admin1",
        nickname: "小铭",
        phone: "15888886789",
        email: faker.internet.email(),
        sex: 0,
        id: 1,
        status: 1,
        dept: {
          // 部门id
          id: 103,
          // 部门名称
          name: "研发部门"
        },
        remark: "管理员",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common2",
        nickname: "小林",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common3",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common4",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common5",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common6",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common7",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common8",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common9",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common10",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common11",
        nickname: "小王",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      },
      {
        avatar: "https://avatars.githubusercontent.com/u/52823142",
        username: "common12",
        nickname: "小王111",
        phone: "18288882345",
        email: faker.internet.email(),
        sex: 1,
        id: 2,
        status: 1,
        dept: {
          id: 105,
          name: "测试部门"
        },
        remark: "普通用户",
        createTime: 1605456000000
      }
    ];
    // list = list.filter(item => item.username.includes(data?.username));
    // list = list.filter(item =>
    //   String(item.status).includes(String(data?.status))
    // );
    // if (data.phone) list = list.filter(item => item.phone === data.phone);
    // if (data.deptId) list = list.filter(item => item.dept.id === data.deptId);

    // 筛选数据
    let filteredList = list.filter(item => {
      return (
        (!data?.username || item.username.includes(data.username)) &&
        (!data?.status || item.status === data.status) &&
        (!data?.phone || item.phone === data.phone) &&
        (!data?.deptId || item.dept.id === data.deptId)
      );
    });

    // 分页数据
    const pageSize = data?.pageSize;
    const currentPage = data?.currentPage;
    const total = filteredList.length;
    const paginatedList = filteredList.slice(
      (currentPage - 1) * pageSize,
      currentPage * pageSize
    );

    resolve({
      success: true,
      data: {
        list: paginatedList,
        total, // 总条目数
        pageSize, // 每页显示条目个数
        currentPage // 当前页数
      }
    });
  });
};

export const getAbnormalSituationOverviewFieldAPI = () => {
  return new Promise(resolve => {
    let list = [
      {
        label: "异常情况123",
        prop: "errMsg",
        width: 147,
        fixed: false,
        visible: true
      },
      {
        label: "异常类型",
        prop: "errType",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "创建时间",
        prop: "createDate",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "处理状态",
        prop: "resolved",
        width: 171,
        fixed: false,
        visible: true
      },
      {
        label: "异常订单号",
        prop: "orderNo",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "备注",
        prop: "remark",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "处理人",
        prop: "handler",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "处理时间",
        prop: "handleDate",
        width: 200,
        fixed: false,
        visible: true
      },
      {
        label: "异常表id",
        prop: "id",
        width: 422,
        fixed: false,
        visible: true
      }
    ];
    resolve({
      code: 200,
      data: {
        list
      }
    });
  });
};

export const getAbnormalSituationOverview = (data?: Object) => {
  return new Promise(resolve => {
    let records = [
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },

      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CGD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      },
      {
        errMsg: "异常",
        errType: "不知道",
        createDate: "2024-08-22T14:43:11",
        resolved: "false",
        orderNo: "CJD123456",
        remark: "E1001,E158木托，8个/托",
        handler: "套",
        handleDate: "2024-08-22T14:43:11",
        id: "666"
      }
    ];
    // 获取前端传入的分页参数
    const currentPage = data?.page?.current || 1;
    const pageSize = data?.page?.size || 20;

    // 计算记录的起始和结束索引
    const offset = (currentPage - 1) * pageSize;
    const recordsPerPage = records.slice(offset, offset + pageSize);

    // 计算总页数
    const total = records.length;
    const pages = Math.ceil(total / pageSize);
    resolve({
      code: 200,
      data: {
        records: recordsPerPage,
        total,
        size: pageSize,
        current: currentPage,
        pages
      }
    });
  });
};

/** 系统管理-用户管理-获取所有角色列表 */
export const getAllRoleList = () => {
  return new Promise(resolve => {
    console.log(33);
    resolve({
      success: true,
      data: [
        { id: 1, name: "超级管理员" },
        { id: 2, name: "普通角色" }
      ]
    });
    // resolve(getAllListRole())
  });
};

/** 系统管理-用户管理-根据userId，获取对应角色id列表（userId：用户id） */
export const getRoleIds = (data?: object) => {
  // return http.request<Result>("post", "/list-role-ids", { data });
  return new Promise(resolve => {
    if (data.userId) {
      if (data.userId == 1) {
        resolve({
          success: true,
          data: [1]
        });
      } else if (data.userId == 2) {
        resolve({
          success: true,
          data: [2]
        });
      }
    } else {
      resolve({
        success: false,
        data: []
      });
    }
  });
};

/** 获取系统管理-角色管理列表 */
export const getRoleList = (data?: object) => {
  // return http.request<ResultTable>("post", "/role", { data });
  return new Promise(resolve => {
    let list = [
      {
        createTime: 1605456000000, // 时间戳（毫秒ms）
        updateTime: 1684512000000,
        id: 1,
        name: "超级管理员",
        code: "admin",
        status: 1, // 状态 1 启用 0 停用
        remark: "超级管理员拥有最高权限"
      },
      {
        createTime: 1605456000000,
        updateTime: 1684512000000,
        id: 2,
        name: "普通角色",
        code: "common",
        status: 1,
        remark: "普通角色拥有部分权限"
      }
    ];
    list = list.filter(item => item.name.includes(data?.name));
    list = list.filter(item =>
      String(item.status).includes(String(data?.status))
    );
    if (data.code) list = list.filter(item => item.code === data.code);
    resolve({
      success: true,
      data: {
        list,
        total: list.length, // 总条目数
        pageSize: 10, // 每页显示条目个数
        currentPage: 1 // 当前页数
      }
    });
  });
};

/** 获取系统管理-部门管理列表 */
// export const getDeptList = (data?: object)
export const getDeptList = () => {
  // return http.request<Result>("post", "/dept", { data });
  return new Promise(resolve => {
    resolve({
      success: true,
      data: [
        {
          name: "佛山市品柏智能科技有限公司",
          parentId: 0,
          id: 100,
          sort: 0,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 1, // 状态 1 启用 0 停用
          type: 1, // 1 公司 2 分公司 3 部门
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "锦泽致盛科技有限公司",
          parentId: 100,
          id: 101,
          sort: 1,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 1,
          type: 2,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "研发部门",
          parentId: 101,
          id: 103,
          sort: 1,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 1,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },

        {
          name: "市场部门",
          parentId: 101,
          id: 104,
          sort: 2,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 1,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },

        {
          name: "测试部门",
          parentId: 101,
          id: 105,
          sort: 3,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 0,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "财务部门",
          parentId: 101,
          id: 106,
          sort: 4,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 1,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "运维部门",
          parentId: 101,
          id: 107,
          sort: 5,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 0,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "业务部门",
          parentId: 101,
          id: 108,
          sort: 6,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 0,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        },
        {
          name: "采购部门",
          parentId: 101,
          id: 109,
          sort: 7,
          phone: "15888888888",
          principal: faker.person.firstName(),
          email: faker.internet.email(),
          status: 0,
          type: 3,
          createTime: 1605456000000,
          remark: "这里是备注信息这里是备注信息这里是备注信息这里是备注信息"
        }
      ]
    });
  });
};

/** 获取系统监控-在线用户列表 */
export const getOnlineLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/online-logs", { data });
};

/** 获取系统监控-登录日志列表 */
export const getLoginLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/login-logs", { data });
};

/** 获取系统监控-操作日志列表 */
export const getOperationLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/operation-logs", { data });
};

/** 获取系统监控-系统日志列表 */
export const getSystemLogsList = (data?: object) => {
  return http.request<ResultTable>("post", "/system-logs", { data });
};

/** 获取系统监控-系统日志-根据 id 查日志详情 */
export const getSystemLogsDetail = (data?: object) => {
  return http.request<Result>("post", "/system-logs-detail", { data });
};

/** 获取角色管理-权限-菜单权限 */
// export const getRoleMenu = (data?: object)
export const getRoleMenu = () => {
  // return http.request<Result>("post", "/role-menu", { data });
  return new Promise(resolve => {
    resolve({
      success: true,
      data: [
        // 外部页面
        {
          parentId: 0,
          id: 100,
          menuType: 0, // 菜单类型（0代表菜单、1代表iframe、2代表外链、3代表按钮）
          title: "menus.pureExternalPage"
        },
        {
          parentId: 100,
          id: 101,
          menuType: 0,
          title: "menus.pureExternalDoc"
        },
        {
          parentId: 101,
          id: 102,
          menuType: 2,
          title: "menus.pureExternalLink"
        },
        {
          parentId: 101,
          id: 103,
          menuType: 2,
          title: "menus.pureUtilsLink"
        },
        {
          parentId: 100,
          id: 104,
          menuType: 1,
          title: "menus.pureEmbeddedDoc"
        },
        {
          parentId: 104,
          id: 105,
          menuType: 1,
          title: "menus.pureEpDoc"
        },
        {
          parentId: 104,
          id: 106,
          menuType: 1,
          title: "menus.pureTailwindcssDoc"
        },
        {
          parentId: 104,
          id: 107,
          menuType: 1,
          title: "menus.pureVueDoc"
        },
        {
          parentId: 104,
          id: 108,
          menuType: 1,
          title: "menus.pureViteDoc"
        },
        {
          parentId: 104,
          id: 109,
          menuType: 1,
          title: "menus.purePiniaDoc"
        },
        {
          parentId: 104,
          id: 110,
          menuType: 1,
          title: "menus.pureRouterDoc"
        },
        // 权限管理
        {
          parentId: 0,
          id: 200,
          menuType: 0,
          title: "menus.purePermission"
        },
        {
          parentId: 200,
          id: 201,
          menuType: 0,
          title: "menus.purePermissionPage"
        },
        {
          parentId: 200,
          id: 202,
          menuType: 0,
          title: "menus.purePermissionButton"
        },
        {
          parentId: 202,
          id: 203,
          menuType: 3,
          title: "添加"
        },
        {
          parentId: 202,
          id: 204,
          menuType: 3,
          title: "修改"
        },
        {
          parentId: 202,
          id: 205,
          menuType: 3,
          title: "删除"
        },
        // 系统管理
        {
          parentId: 0,
          id: 300,
          menuType: 0,
          title: "menus.pureSysManagement"
        },
        {
          parentId: 300,
          id: 301,
          menuType: 0,
          title: "menus.pureUser"
        },
        {
          parentId: 300,
          id: 302,
          menuType: 0,
          title: "menus.pureRole"
        },
        {
          parentId: 300,
          id: 303,
          menuType: 0,
          title: "menus.pureSystemMenu"
        },
        {
          parentId: 300,
          id: 304,
          menuType: 0,
          title: "menus.pureDept"
        },
        // 系统监控
        {
          parentId: 0,
          id: 400,
          menuType: 0,
          title: "menus.pureSysMonitor"
        },
        {
          parentId: 400,
          id: 401,
          menuType: 0,
          title: "menus.pureOnlineUser"
        },
        {
          parentId: 400,
          id: 402,
          menuType: 0,
          title: "menus.pureLoginLog"
        },
        {
          parentId: 400,
          id: 403,
          menuType: 0,
          title: "menus.pureOperationLog"
        },
        {
          parentId: 400,
          id: 404,
          menuType: 0,
          title: "menus.pureSystemLog"
        },
        // 标签页操作
        {
          parentId: 0,
          id: 500,
          menuType: 0,
          title: "menus.pureTabs"
        },
        {
          parentId: 500,
          id: 501,
          menuType: 0,
          title: "menus.pureTabs"
        }
        // {
        //   parentId: 500,
        //   id: 502,
        //   menuType: 0,
        //   title: "query传参模式"
        // },
        // {
        //   parentId: 500,
        //   id: 503,
        //   menuType: 0,
        //   title: "params传参模式"
        // }
      ]
    });
  });
};

/** 获取角色管理-权限-菜单权限-根据角色 id 查对应菜单 */
export const getRoleMenuIds = (data?: object) => {
  // return http.request<Result>("post", "/role-menu-ids", { data });
  return new Promise(resolve => {
    if (data.id == 1) {
      resolve({
        success: true,
        data: [
          100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 200, 201, 202,
          203, 204, 205, 300, 301, 302, 303, 304, 400, 401, 402, 403, 404, 500,
          501, 502, 503
        ]
      });
    } else if (data.id == 2) {
      resolve({
        success: true,
        data: [
          100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 404, 500, 501,
          502, 503
        ]
      });
    }
  });
};
