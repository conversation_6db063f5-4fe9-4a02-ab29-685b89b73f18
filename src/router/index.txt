1. 动态路由引入和处理
javascript
复制代码
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);
这部分代码使用了 import.meta.glob 动态加载位于 src/router/modules 目录下的所有路由模块（.ts 文件），并且排除了 remaining.ts 文件。eager: true 表示在加载时立即引入这些模块。加载的模块通过 modules[key].default 作为路由配置数组进行合并。

2. 静态路由构建
javascript
复制代码
const routes = [];
Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});
这个部分将通过 import.meta.glob 加载的路由模块加入到 routes 数组中。

javascript
复制代码
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);
通过一系列的函数处理（ascending, buildHierarchyTree, formatFlatteningRoutes, formatTwoStageRoutes），最终生成了一个扁平化、二级化的路由配置数组 constantRoutes。

3. 路由白名单
javascript
复制代码
const whiteList = ["/login"];
这是一个白名单数组，表示在用户未登录时可以访问的路由。通常用于避免未登录用户被重定向到登录页。

4. 路由守卫（beforeEach）
javascript
复制代码
router.beforeEach((to: ToRouteType, _from, next) => {
  NProgress.start();
  const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);

  if (Cookies.get(multipleTabsKey) && userInfo) {
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
      next({ path: "/error/403" });
    }
    if (VITE_HIDE_HOME === "true" && to.fullPath === "/home") {
      next({ path: "/error/404" });
    }
    toCorrectRoute();
  } else {
    if (to.path !== "/login") {
      if (whiteList.indexOf(to.path) !== -1) {
        next();
      } else {
        removeToken();
        next({ path: "/login" });
      }
    } else {
      next();
    }
  }
});
这里是全局路由守卫，主要作用是：

启动 NProgress 进度条。
检查用户是否登录以及是否具有访问该页面的权限。
根据路由的权限配置，如果没有权限则跳转到 403 页面。
如果用户未登录且试图访问非白名单页面，则会被重定向到登录页。
5. 页面标题和国际化
javascript
复制代码
if (!externalLink) {
  to.matched.some(item => {
    if (!item.meta.title) return "";
    const Title = getConfig().Title;
    const translatedTitle = transformI18n(item.meta.title);
    if (Title) document.title = `${translatedTitle} | ${Title}`;
    else document.title = translatedTitle as string;
  });
}
这部分代码会根据路由的 meta.title 设置页面的标题，并支持国际化。

6. 路由后的操作（afterEach）
javascript
复制代码
router.afterEach(() => {
  NProgress.done();
});
页面加载完毕后，关闭进度条。

7. 路由重置
javascript
复制代码
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}
该方法可以重置路由，移除已经添加的动态路由，并且重新加载静态路由。

总结：
这段代码主要实现了以下几个功能：

动态路由加载和权限控制：通过 import.meta.glob 和路由守卫，动态加载路由并根据用户权限控制访问。
页面标题设置和国际化：根据路由的 meta.title 设置页面标题，并且支持国际化。
路由白名单和登录验证：通过白名单和用户信息验证来处理登录状态。
路由重置和清缓存：提供了重置路由的功能。
操作步骤：
配置路由模块：在 src/router/modules 下创建不同的路由模块，并在该文件中通过 import.meta.glob 动态加载。
修改 constantRoutes 和 constantMenus：根据项目需求修改路由的层级结构、菜单项和动态路由配置。
路由守卫逻辑：根据项目需求，调整 beforeEach 和 afterEach 中的权限校验、页面标题和路由跳转等逻辑。
如果你不清楚如何具体操作，建议从以下步骤开始：

理解每个函数的作用，如 formatTwoStageRoutes, handleAliveRoute 等。
根据项目需要，在 src/router/modules 下创建模块并引入路由配置。
调整 beforeEach 中的登录和权限校验逻辑，确保正确地处理不同用户的访问权限。