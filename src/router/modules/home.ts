const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/home",
  meta: {
    icon: "ri:home-2-line",
    title: "menus.pureHome",
    rank: 0
  },
  children: [
    {
      path: "/home",
      name: "welcome",
      component: () => import("@/views/home/<USER>"),
      meta: {
        title: "menus.pureHome",
        showLink: VITE_HIDE_HOME === "true" ? false : true,
        keepAlive: true
      }
    }
  ]
} satisfies RouteConfigsTable;
