const Text = () => import("@/views/test/index.vue");
const HJ = () => import("@/views/test/test-hj.vue");
const HYX = () => import("@/views/test/index_hyx.vue");

export default {
  path: "/text",
  redirect: "/test",
  meta: {
    icon: "ri:cloud-off-line",
    title: "systems.pureTestPage",
    rank: 1000
  },
  children: [
    {
      path: "/test/fzw",
      name: "fzw",
      component: Text,
      meta: {
        title: "systems.pureTestPageFzw",
        keepAlive: true // 表示该页面需要缓存
      }
    },
    {
      path: "/test/hj",
      name: "hj",
      component: HJ,
      meta: {
        title: "systems.pureTestPageHj",
        keepAlive: true // 表示该页面需要缓存
      }
    },
    {
      path: "/test/hyx",
      name: "hyx",
      component: HYX,
      meta: {
        title: "systems.pureTestPageHyx",
        keepAlive: true // 表示该页面需要缓存
      }
    }
  ]
} satisfies RouteConfigsTable;
