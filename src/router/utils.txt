动态路由管理 (handleAsyncRoutes)：

动态加载路由并存储缓存（本地存储中），使用 getAsyncRoutes 从后端获取动态路由。
如果启用了路由缓存（通过 getConfig()?.CachingAsyncRoutes 判断），会将路由信息存储到 localStorage，避免每次刷新页面都重新加载。
路由缓存操作 (handleAliveRoute)：

路由缓存操作包括添加、删除和刷新缓存。handleAliveRoute 函数根据传入的 mode（如 add、delete、refresh）来操作缓存的路由。
具体操作会通过 usePermissionStoreHook().cacheOperate 来进行（缓存增删改操作）。
路由匹配 (addPathMatch)：

通过 addPathMatch 为路由添加一个通配符路由 /pathMatch(.*)，用于捕获未定义的路径并重定向到 404 页面。
格式化和排序路由 (ascending)：

路由根据 meta.rank 进行升序排序，确保路由的优先级正确。
从本地存储获取已缓存路由并初始化 (initRouter)：

初始化路由时，检查本地存储是否已缓存动态路由。如果有缓存，则直接使用缓存路由；如果没有，则从后端请求动态路由，并缓存到本地存储。
总结来说，缓存操作和动态路由加载主要通过 handleAsyncRoutes 和 handleAliveRoute 函数来完成。initRouter 负责初始化路由，并根据配置决定是否从本地存储加载已缓存的路由。