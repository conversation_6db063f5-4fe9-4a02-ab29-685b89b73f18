import { defineStore } from "pinia";
import { useLocalStorage } from "@vueuse/core";

export const useNodeStore = defineStore("node", {
  state: () => ({
    nodeId: useLocalStorage("nodeId", "1"), // 默认值为第一个节点的 id
    nodeLabel: useLocalStorage("nodeLabel", "销售订单"), // 默认值为第一个节点的 label
    sonPurchaseOrder: useLocalStorage("sonPurchaseOrder", {
      id: "1",
      label: "采购订单",
      productionOrderNo: "" // 生产订单号
    }),
    sonPurchaseOrderDetail: useLocalStorage("sonPurchaseOrderDetail", {
      // 采购订单明细流程
      id: "1",
      label: "采购订单",
      finalMaterialSourceNo: "", // 采购订单明细流程的最终物料来源编号
      purchaseOrderDetailLineNumber: "" // 采购订单明细行号
    }),
    sonPlanMaterialOrder: useLocalStorage("sonPlanMaterialOrder", {
      // 计划发料单明细流程
      id: "1",
      label: "发料单号",
      workOrdersNumber: "" // 发料单号
    }),
    sonOrderPickingSheet: useLocalStorage("sonOrderPickingSheet", {
      // 拣选单明细流程
      id: "1",
      label: "拣选单号",
      pickingOrderCode: "" // 拣选单号
    }),
    sonWarehousePreparationPlanDetail: useLocalStorage(
      "sonWarehousePreparationPlanDetail",
      {
        // 仓库备料明细-跳转到计划发料单明细
        id: "1",
        label: "发料单明细-生产任务单号",
        productionOrderNo: "", // 生产任务单号
        materialCode: "" // 物料代码
      }
    ),
    sonWarehousePreparationSelectDetail: useLocalStorage(
      "sonWarehousePreparationSelectDetail",
      {
        // 仓库备料明细-跳转到拣选单明细
        id: "1",
        label: "拣选单明细-生产任务单号",
        productionOrderNo: "", // 生产任务单号
        materialCode: "" // 物料代码
      }
    ),
    // sonPurchaseOrderDetailLineNumber: useLocalStorage("sonPurchaseOrderDetailLineNumber", {
    //   // 采购订单明细行号
    //   purchaseOrderDetailLineNumber: ""
    // }),
    sonWorkshopProduction: useLocalStorage("sonWorkshopProduction", {
      id: "1",
      label: "车间退补料情况汇总"
    }), // 默认值为第一个节点的 label
    sonOutOrder: useLocalStorage("sonOutOrder", {
      id: "1",
      label: "委外订单"
    }), // 默认值为第一个节点的 label
    sonOutOrderDetail: useLocalStorage("sonOutOrderDetail", {
      id: "1",
      label: "委外订单",
      billNo: "", // 委外订单号,用于跳转明细
      purOrderNo: "", // 采购订单号
      purOrderEntrySeq: "" // 采购订单行号
    }),
    sonSendLaborers: useLocalStorage("sonSendLaborers", {
      id: "2",
      label: "计划派工"
    }), // 默认值为第一个节点的 label

    queryId: useLocalStorage("queryId", ""), // 查询条件

    saleOrderId: useLocalStorage("saleOrderId", ""), // 销售订单号

    outsourcingWorkOrderCode: useLocalStorage("setOutsourcingWorkOrderCode", "")
  }),
  actions: {
    setNode(node) {
      this.nodeId = node.id;
      this.nodeLabel = node.label;
      // localStorage.setItem("nodeId", node.id); // 保存到本地存储
      // localStorage.setItem("nodeLabel", node.label); // 保存到本地存储
    },
    setSonPurchaseOrder({ id, label }) {
      this.sonPurchaseOrder.id = id;
      this.sonPurchaseOrder.label = label;
    },
    // setSonProductionOrderNo({ productionOrderNo }) {
    //   this.sonPurchaseOrder.productionOrderNo = productionOrderNo;
    // },
    // 仓库备料-明细-采购任务单号修改
    setSonPurchaseOrderProductionOrderNo(productionOrderNo) {
      this.sonPurchaseOrder.productionOrderNo = productionOrderNo;
    },
    // 仓库备料明细-计划发料单明细-发料单号
    setSonPlanMaterialOrderWorkOrdersNumber(workOrdersNumber) {
      this.sonPlanMaterialOrder.workOrdersNumber = workOrdersNumber;
    },
    // 仓库备料明细-拣选单明细-拣选单号
    setSonOrderPickingSheetPickingOrderCode(pickingOrderCode) {
      this.sonOrderPickingSheet.pickingOrderCode = pickingOrderCode;
    },
    // 仓库备料明细-发料单明细-生产任务单号&物料代码
    setWarehousePreparationPlanProductionOrderNoMaterialCode(
      productionOrderNo,
      materialCode
    ) {
      this.sonWarehousePreparationPlanDetail.productionOrderNo =
        productionOrderNo;
      this.sonWarehousePreparationPlanDetail.materialCode = materialCode;
    },
    // 仓库备料明细-拣选单明细-生产任务单号&物料代码
    setWarehousePreparationSelectProductionOrderNoMaterialCode(
      productionOrderNo,
      materialCode
    ) {
      this.sonWarehousePreparationSelectDetail.productionOrderNo =
        productionOrderNo;
      this.sonWarehousePreparationSelectDetail.materialCode = materialCode;
    },
    setSonPurchaseOrderDetail({ id, label }) {
      this.sonPurchaseOrderDetail.id = id;
      this.sonPurchaseOrderDetail.label = label;
    },
    // 设置采购订单明细流程的最终物料来源编号
    setSonPurchaseOrderDetailFinalMaterialSourceNo(finalMaterialSourceNo) {
      this.sonPurchaseOrderDetail.finalMaterialSourceNo = finalMaterialSourceNo;
    },
    /** 行号 */
    setPurchaseOrderDetailLineNumber(purchaseOrderDetailLineNumber) {
      this.sonPurchaseOrderDetail.purchaseOrderDetailLineNumber =
        purchaseOrderDetailLineNumber;
    },
    setWorkshopProduction({ id, label }) {
      this.sonWorkshopProduction.id = id;
      this.sonWorkshopProduction.label = label;
    },
    setSonOutOrder({ id, label }) {
      this.sonOutOrder.id = id;
      this.sonOutOrder.label = label;
    },
    setSonOutOrderDetail({ id, label }) {
      this.sonOutOrderDetail.id = id;
      this.sonOutOrderDetail.label = label;
    },
    /**
     * 委外订单号
     * @param billNo
     */
    setSonOutOrderDetailBillNo(billNo) {
      this.sonOutOrderDetail.billNo = billNo;
    },
    /**
     *  采购订单号
     * @param purOrderNo
     */
    setSonOutOrderDetailPurOrderNo(purOrderNo) {
      this.sonOutOrderDetail.purOrderNo = purOrderNo;
    },
    /**
     * 采购订单行号
     * @param purOrderEntrySeq
     */
    setSonOutOrderDetailMaterialNo(purOrderEntrySeq) {
      this.sonOutOrderDetail.purOrderEntrySeq = purOrderEntrySeq;
    },
    setSonSendLaborers({ id, label }) {
      this.sonSendLaborers.id = id;
      this.sonSendLaborers.label = label;
    },
    setQueryId(Id) {
      this.queryId = Id;
    },
    setSaleOrderId(id) {
      this.saleOrderId = id;
    },
    setOutsourcingWorkOrderCode(code) {
      this.outsourcingWorkOrderCode = code;
    }
  }
});
