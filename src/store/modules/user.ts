import { defineStore } from "pinia";
import {
  type userType,
  store,
  router,
  resetRouter,
  routerArrays,
  storageLocal
} from "../utils";
import {
  type UserResult,
  type RefreshTokenResult,
  getLogin,
  refreshTokenApi
} from "@/api/user";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useMultiTagsStoreHook } from "./multiTags";
import { type DataInfo, setToken, removeToken, userKey } from "@/utils/auth";
export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    userId: storageLocal().getItem<DataInfo<number>>(userKey)?.userId ?? "",
    // 头像
    avatar:
      storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? "/logo.png",
    // 用户名
    username: storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? "",
    // 昵称
    nickname: storageLocal().getItem<DataInfo<number>>(userKey)?.nickname ?? "",
    // 页面级别权限
    roles: storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [],
    // 用户姓名
    name: storageLocal().getItem<DataInfo<number>>(userKey)?.name ?? "",
    // 用户性别
    sex: storageLocal().getItem<DataInfo<number>>(userKey)?.sex ?? "",
    // 手机号
    phone: storageLocal().getItem<DataInfo<number>>(userKey)?.phone ?? "",
    // 部门id
    deptId: storageLocal().getItem<DataInfo<number>>(userKey)?.deptId ?? "",
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 30
  }),
  actions: {
    /** 存储头像 */
    SET_USER_ID(userId: string) {
      this.userId = userId;
    },
    /** 存储头像 */
    SET_DEPT_ID(avatar: string) {
      this.avatar = avatar;
    },
    /** 存储头像 */
    SET_AVATAR(avatar: string) {
      this.avatar = avatar;
    },
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      // 获取当前的用户数据
      const currentUserData =
        storageLocal().getItem<DataInfo<number>>(userKey) || {};
      console.log("currentUserData-----username", currentUserData);

      // 更新 sex 值
      currentUserData.username = username;
      // 将更新后的数据存储到本地存储
      storageLocal().setItem<DataInfo<number>>(userKey, currentUserData);
      // 更新 Pinia 状态中的 sex
      this.username = username;
    },
    /** 存储昵称 */
    SET_NICKNAME(nickname: string) {
      this.nickname = nickname;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储用户姓名 */
    SET_NAME(name: Array<string>) {
      // 获取当前的用户数据
      const currentUserData =
        storageLocal().getItem<DataInfo<number>>(userKey) || {};

      // 更新 sex 值
      currentUserData.name = name;
      // 将更新后的数据存储到本地存储
      storageLocal().setItem<DataInfo<number>>(userKey, currentUserData);
      // 更新 Pinia 状态中的 sex
      this.name = name;
    },
    /** 存储用户性别 */
    SET_SEX(sex: Array<string>) {
      // this.sex = sex;
      // // 将性别值存储到本地存储
      // storageLocal().setItem<DataInfo<string>>(userKey, { sex });

      // 获取当前的用户数据
      const currentUserData =
        storageLocal().getItem<DataInfo<number>>(userKey) || {};
      console.log("currentUserData----------sex", currentUserData);

      // 更新 sex 值
      currentUserData.sex = sex;
      // 将更新后的数据存储到本地存储
      storageLocal().setItem<DataInfo<number>>(userKey, currentUserData);
      // 更新 Pinia 状态中的 sex
      this.sex = sex;
    },
    /** 存储手机号 */
    SET_PHONE(phone: Array<string>) {
      // 获取当前的用户数据
      const currentUserData =
        storageLocal().getItem<DataInfo<number>>(userKey) || {};
      // 更新 sex 值
      currentUserData.phone = phone;
      // 将更新后的数据存储到本地存储
      storageLocal().setItem<DataInfo<number>>(userKey, currentUserData);
      // 更新 Pinia 状态中的 sex
      this.phone = phone;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value: number) {
      this.loginDay = Number(value);
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<UserResult>((resolve, reject) => {
        getLogin(data)
          .then(data => {
            if (data?.code === 200) {
              data.success = true;
              setToken(data.data);
            }
            resolve(data);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.username = "";
      this.roles = [];
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      usePermissionStoreHook().clearAllCachePage(); // 清除权限缓存
      localStorage.removeItem("async-routes");
      router.replace("/login");
      window.location.reload(); // 强制刷新页面，否则会下次登录的时候第一次的权限会缓存上一个用户的Cookies
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken(data.data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
