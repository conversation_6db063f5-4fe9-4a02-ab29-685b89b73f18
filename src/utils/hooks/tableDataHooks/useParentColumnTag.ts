import { ref } from "vue";

export const useParentColumnTag = () => {
  const setParentColumnTag = (row, columns, columnList, title) => {
    // 获取所有列的字段，并映射每个字段的中文名称和对应的值

    // 根据 columnList 筛选 columns 和 row 中的数据
    const filteredColumnFields = columns
      .filter(col => columnList.includes(col.prop)) // 只保留 shaixuan 中的字段
      .map(col => ({
        label: col.label, // 列的显示名称
        value: row[col.prop], // 列的字段值
        prop: col.prop // 列的字段名
      }));

    // 存储筛选后的列数据到 localStorage
    localStorage.setItem(title, JSON.stringify(filteredColumnFields));
  };

  const getParentColumnTag = t => {
    const otherField = ref([]);
    // otherField.value = route.state
    // console.log("otherField.value :", otherField.value);

    otherField.value = JSON.parse(localStorage.getItem(t));

    console.log("otherField:", otherField);
    return otherField;
  };

  return {
    setParentColumnTag,
    getParentColumnTag
  };
};
