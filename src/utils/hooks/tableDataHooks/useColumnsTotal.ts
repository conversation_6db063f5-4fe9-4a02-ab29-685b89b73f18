/**
 * @function table底部求和函数
 * @param columns 求和的列
 * @param request 求和请求API
 * @param queryData 请求参数
 * @param orderId 默认参数id
 * @param {Boolean} more 是否有更多参数
 * @param moreQueryData 更多参数，以数组形式传入
 * @returns 求和列数字
 */
export const useColumnsTotal = async (
  columns,
  request,
  queryData,
  orderId = {}
) => {
  const result = {}; // 用对象来存储请求结果

  // 为每个 column 创建请求任务
  const tasks = columns.map(
    item =>
      // 为每个请求定义 Promise
      new Promise(async resolve => {
        try {
          // 执行请求并等待结果
          const res = await (Object.keys(orderId).length > 0
            ? request(orderId, item, [...queryData])
            : request(item, [...queryData]));

          // 确保 res.data 是有效的数字，如果是数字则保留两位小数
          result[item] =
            res.data != null && !isNaN(res.data)
              ? parseFloat(res.data.toFixed(2))
              : 0; // 如果请求失败，返回默认值 0

          resolve(true);
        } catch (error) {
          console.error(`请求 ${item} 时出错: `, error);
          result[item] = 0; // 如果请求失败，返回默认值 0
          resolve(false); // 确保 Promise 结束
        }
      })
  );

  // 等待所有任务完成
  await Promise.all(tasks);

  return result;
};
