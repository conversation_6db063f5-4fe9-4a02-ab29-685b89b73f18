// 获取store的query数据
import { useNodeStore } from "@/store/modules/fullLifecycle";
/**
 * @function 获取store实例,检测是否缓存了billNo的Hook
 * @returns {Store, checkBillNoCache}
 */
export const useCheckBillNoCache = () => {
  // 获取store实例
  const nodeStore = useNodeStore();

  /**
   * @function 检测是否缓存了billNo
   * @param home 是否是home节点
   * @returns 根据home? [...query] : "子节点订单号" || [] : false
   */
  const checkBillNoCache = (home = false) => {
    console.log(nodeStore.queryId, "id");

    // 如果 nodeStore.queryId 存在，返回相应结果
    if (nodeStore.queryId) {
      if (home) {
        return [
          {
            name: "sytText1",
            query: nodeStore.queryId,
            logic: "and",
            condition: "eq"
          }
        ];
      }
      return nodeStore.queryId;
    }

    // 如果 nodeStore.queryId 不存在，返回 home 的值
    return home ? [] : "";
  };

  return {
    nodeStore,
    checkBillNoCache
  };
};
