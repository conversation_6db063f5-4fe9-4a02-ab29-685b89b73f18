// useCardData.js
import { ref, onMounted } from "vue";
interface CardConfig {
  title: string;
  api: () => Promise<any>;
  formatter?: (data: any) => any;
}

export const useCardData = (cardConfig: CardConfig[]) => {
  const cardList = ref(
    cardConfig.map(config => ({
      title: config.title,
      data: null
    }))
  );
  console.log("cardList123456789", cardList);

  const fetchCardData = async () => {
    try {
      const apiPromises = cardConfig.map(config => config.api());
      console.log("apiPromises", apiPromises);

      const apiResults = await Promise.all(apiPromises);
      cardList.value = cardConfig.map((config, index) => {
        const originalData = apiResults[index].data;
        const formattedData = config.formatter
          ? config.formatter(originalData)
          : originalData;
        return {
          title: config.title,
          data: formattedData
        };
      });
    } catch (error) {
      console.error("数据获取失败:", error);
    }
  };
  onMounted(fetchCardData);
  return {
    cardList,
    fetchCardData
  };
};
