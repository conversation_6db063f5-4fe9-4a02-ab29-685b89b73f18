// stateMapper.js

/**
 * 处理整个数据集，将状态映射的方法应用于每条记录
 * @param Object dataItem 响应数据
 * @param Object statesConfig 状态配置
 * @returns Array 映射后的记录列表
 */
export const mapHandle = (mapData, statesConfig) => {
  let records = mapData.map(item => {
    /** 处理状态映射 */
    if (statesConfig.states) {
      const enumsObj = stateMapHandle(item, statesConfig?.states);
      return { ...item, ...enumsObj };
    } else {
      return item;
    }
  });
  // 判断是否存在排序需求，有则进行排序
  if (statesConfig.sortFields) {
    records = workingProcedureCodeSort(records, statesConfig.sortFields);
  }

  //跟statesConfig.addPercentSigns的数组匹配到的字段需要添加百分号
  if (statesConfig.addPercentSigns) {
    records = records.map(record => {
      statesConfig.addPercentSigns.forEach(field => {
        if (record[field] !== undefined) {
          record[field] =
            `${record[field] != null ? record[field] + "%" : ""} `;
        }
      });
      return record;
    });
  }
  // 返回映射后的记录列表
  return records;
};

/**
 * 根据 states 和 recordsItem 执行状态映射
 * @param {Object} recordsItem 需要映射的记录项
 * @param {Object} states 状态映射数组
 * @returns {Object} 映射后的item对象
 */
const stateMapHandle = (recordsItem, states) => {
  const enums = states.map(state => {
    const stateName = state.name;

    // 返回映射的状态属性
    return { [stateName]: state[recordsItem[stateName]] };
  });

  // 合并 enums 中的所有对象
  return enums.reduce((acc, enumItem) => ({ ...acc, ...enumItem }), {});
};

/**
 * @function 根据提取的内容进行排序,根据这些部分进行排序。对于每个字段，如果前缀相同，则比较数字部分；如果不同，则按字母顺序比较前缀。
 * @param records 需要排序的记录列表
 * @param fields 条件字段数组
 * @returns 排序后的记录列表
 */
const workingProcedureCodeSort = (records, fields) => {
  //根据提取的内容进行排序 根据这些部分进行排序。对于每个字段，如果前缀相同，则比较数字部分；如果不同，则按字母顺序比较前缀。
  return records.sort((a, b) => {
    for (const field of fields) {
      // 如果字段不存在，则跳过该字段
      if (!a[field] || !b[field]) return;
      const partA = getFieldParts(a[field]);
      const partB = getFieldParts(b[field]);
      if (partA.prefix === partB.prefix) {
        if (partA.num !== partB.num) {
          return partA.num - partB.num;
        }
      } else {
        return partA.prefix.localeCompare(partB.prefix);
      }
    }
    return 0;
  });
};

/**
 *
 * @param value 需要处理的字段值
 * @returns 正则获取的前缀和数字部分
 */
const getFieldParts = value => {
  const prefix = value.match(/^[A-Z]+/)[0];
  const num = parseInt(value.match(/\d+$/)[0], 10);
  return { prefix, num };
};
