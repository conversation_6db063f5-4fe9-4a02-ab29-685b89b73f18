// import { useRoute } from "vue-router";
// const route = useRoute();
// console.log(route.path, "faroute");
export const useButtonPermission = route => {
  const routerPath = route.path;
  const asyncRoutes = JSON.parse(localStorage.getItem("async-routes"));

  // 按钮
  function extractBtnRoles(routerData, currentPath) {
    // 深度优先搜索函数
    function dfs(routes, path) {
      for (const route of routes) {
        // 如果匹配到当前路径
        if (route.path === path) {
          return route.meta.btnRoles || [];
        }
        // 递归查找子路由
        if (route.children && route.children.length > 0) {
          const result = dfs(route.children, path);
          if (result.length > 0) {
            return result;
          }
        }
      }
      return [];
    }

    return dfs(routerData, currentPath);
  }
  const btnRoles = extractBtnRoles(asyncRoutes, routerPath);


  // 判断当前用户是否有某个权限
  function hasPermission(permission) {
    const role = btnRoles.find(role => role.auths == permission);
    return role !== undefined; // 如果找到了符合条件的角色，返回 true
  }
  /**
   * 截取字符串，获取冒号之前的数据
   * @param str 输入的字符串
   * @returns 冒号之前的数据，如果没有冒号则返回原字符串
   */
  function getStringBeforeColon(str: string): string {
    const index = str.indexOf(":");
    return index === -1 ? str : str.slice(0, index).trim();
  }

  return {
    hasPermission,
    getStringBeforeColon,
    btnRoles
  };
};
