// const datas = new Array(100).fill(0).map((_, i) => i + 1);
// export function aa() {
//   const taskHandler = (_, i) => {
//     const div = document.createElement("div");
//     div.innerText = i;
//     document.body.appendChild(div);
//   };

//   //  调度器
//   // 非浏览器平台打开以下注释
//   // const scheduler = task => {
//   //   //每一秒执行一个渲染帧
//   //   // setTimeout(() => {
//   //   //   const now = performance.now();
//   //   //   // 每一个分片执行3毫秒
//   //   //   task(() => performance.now() - now <= 10);
//   //   // }, 1000);
//   // };
//   // performChunk(datas, taskHandler, scheduler);

//   //浏览器环境下使用以下函数即可
//   browserPerformChunk(datas, taskHandler);
// }

/**
 *
 * @param datas 执行次数或者数组
 * @param taskHandler 每个任务需要处理的内容
 * @param scheduler 调度器(浏览器使用requestIdCallback()，其它平台使用setTimeout)
 * @returns 浏览器环境下使用requestIdCallback，其它平台使用setTimeout
 */
function performChunk(datas, taskHandler, scheduler) {
  //使用参数归一化判断是执行次数还是数组进程分片
  if (typeof datas === "number") {
    datas = {
      length: datas
    };
  }
  if (datas.length === 0) return; // 如果没有数据了，就结束

  let i = 0;
  //开启下一个分片的执行
  function _run() {
    if (i >= datas.length) return;

    //开启调度器 一个渲染帧中 空闲时开启分片执行
    scheduler(gon => {
      while (gon() && i < datas.length) {
        taskHandler(datas[i++], i); //执行分片任务
        i++; //执行下一个任务
      }
      //此次分片的完成
      _run();
    });
  }
  _run();
}

/**
 *
 * @param datas 执行次数或者数组
 * @param taskHandler 每个任务需要处理的内容
 * @returns 浏览器环境下使用requestIdCallback，其它平台使用setTimeout
 */
export function browserPerformChunk(datas, taskHandler) {
  //  调度器
  const scheduler = task => {
    requestIdleCallback(idle => {
      // 每一个分片执行 多久取决于idle.timeRemaining
      task(() => idle.timeRemaining() > 0);
    });
  };

  performChunk(datas, taskHandler, scheduler);
}
