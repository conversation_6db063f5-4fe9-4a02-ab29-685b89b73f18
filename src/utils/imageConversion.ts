import * as imageConversion from "image-conversion";
// import { getToken } from "@/utils/auth";

// 从 LocalStorage 获取 Token
const tokenData = JSON.parse(localStorage.getItem("authorized-token") || "{}");
console.log("tokenData: ", JSON.parse(tokenData));

// 根据环境变量设置基础 URL
const baseURL: string =
  import.meta.env.MODE === "production"
    ? import.meta.env.VITE_BASE_URL
    : "/cors-api";

// 将 Blob 转换为 File
const blobToFile = (blob: Blob, fileName: string): File => {
  return new File([blob], fileName, { type: blob.type });
};

/**
 * 压缩并上传图片函数
 * @param file - 上传的文件 (Blob 或 File)
 * @param uploadUrl - 上传的 URL
 * @param compressRate - 压缩比率 (默认 0.7)
 * @returns - 上传结果的 JSON 响应
 */
export const compressAndUploadImage = async (
  file: Blob | File,
  uploadUrl: string,
  compressRate: any = 0.7,
  id: string
): Promise<any> => {
  // 判断是否开启压缩
  const compressEnabled: boolean = JSON.parse(
    localStorage.getItem("compressEnabled") || "true"
  );

  let finalFile: File =
    file instanceof Blob ? blobToFile(file, "converted_image") : file;

  if (compressEnabled && finalFile instanceof File) {
    try {
      const compressedBlob: Blob = await imageConversion.compress(
        finalFile,
        compressRate
      );
      finalFile = blobToFile(compressedBlob, finalFile.name);
      console.log(finalFile, "压缩后");
    } catch (error) {
      console.error("Error compressing image: ", error);
    }
  } else {
    console.log(finalFile, "未压缩");
  }

  const formData = new FormData();
  formData.append("file", finalFile);
  formData.append("id", id);
  try {
    const response: Response = await fetch(baseURL + uploadUrl, {
      method: "POST",
      headers: {
        Authorization: `${JSON.parse(tokenData).data}` // 自动附加 Token
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error("Image upload failed");
    }

    return await response.json();
  } catch (error) {
    console.error("Error uploading image: ", error);
    throw error;
  }
};
