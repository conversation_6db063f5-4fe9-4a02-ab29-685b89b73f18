/**
 *
 * @param dateTimeStr 格式化日期时间
 * @returns
 */
export function formatDateTime(dateTimeStr: string): string {
  if (dateTimeStr == null || dateTimeStr == undefined) {
    return "";
  }
  const date = new Date(dateTimeStr);
  if (isNaN(date.getTime())) {
    return "";
  }

  const formattedDate = date
    .toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    })
    .replace(/\//g, "-");
  const formattedTime = date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
  return `${formattedDate} ${formattedTime}`;
}
