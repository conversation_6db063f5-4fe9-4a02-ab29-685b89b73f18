import * as XLSX from "xlsx";

// 格式化当前时间到小时
const formatCurrentTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hour = String(now.getHours()).padStart(2, "0");
  const minute = String(now.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day}-${hour}点${minute}分`;
};

const exportToExcel = (columns, data, fileName) => {
  // 将 Proxy 数据转换为普通对象
  const plainData = JSON.parse(JSON.stringify(data));

  // 创建工作簿
  const wb = XLSX.utils.book_new();

  // 创建表头
  const wsData = [
    columns.map(col => col.label) // 表头
  ];

  // 添加数据
  plainData.forEach(item => {
    const rowData = columns.map(col => {
      const key = col.prop;
      return item[key] != null ? item[key] : "";
    });
    wsData.push(rowData);
  });

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(wsData);

  // 设置列宽自适应
  const colWidths = columns.map(col => {
    const headerWidth = col.label.length;
    const maxDataWidth = Math.max(
      ...plainData.map(item => {
        const value = String(item[col.prop] || "");
        // 计算字符串总字符数，中文字符算2，英文字符算1
        return value.split("").reduce((sum, char) => {
          return sum + (char.match(/[^\x00-\xff]/) ? 2 : 1); // 中文字符长度按2算
        }, 0);
      })
    );
    console.log(maxDataWidth);

    // 根据 maxDataWidth 值动态调整宽度倍率
    let width;
    if (maxDataWidth <= 10) {
      width = Math.max(headerWidth, maxDataWidth) + 2; // 短字符串乘以 2
    } else if (maxDataWidth <= 20) {
      width = Math.max(headerWidth * 2, maxDataWidth) + 2; // 中等长度乘以 1.5
    } else {
      width = Math.max(headerWidth * 2, maxDataWidth) + 2; // 较长字符串乘以 1.2
    }
    return { wch: width };
  });
  ws["!cols"] = colWidths;

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

  // 拼接当前时间到文件名，精准到小时
  const currentTime = formatCurrentTime();
  const fullFileName = `${fileName}_${currentTime}.xlsx`;

  // 生成 Excel 文件
  XLSX.writeFile(wb, fullFileName);
};

export default exportToExcel;
