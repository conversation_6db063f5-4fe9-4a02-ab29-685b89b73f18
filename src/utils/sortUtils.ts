// // utils/sortUtils.ts

// import _ from "lodash";

// class SortUtils {
//   /**
//    * 排序数组
//    * @param array 需要排序的数组
//    * @param iteratee 排序条件
//    * @param order 排序方式 'asc' 或 'desc'
//    * @returns 排序后的数组
//    */
//   static sortArray<T>(
//     array: T[],
//     iteratee: _.ValueIteratee<T>,
//     order: "asc" | "desc" = "asc"
//   ): T[] {
//     return _.orderBy(array, item => _.get(item, iteratee, null), order);
//   }

//   /**
//    * 多字段排序
//    * @param array 需要排序的数组
//    * @param iteratees 排序条件数组
//    * @param orders 排序方式数组
//    * @returns 排序后的数组
//    */
//   static sortByMultipleFields<T>(
//     array: T[],
//     iteratees: _.ValueIteratee<T>[],
//     orders: Array<"asc" | "desc">
//   ): T[] {
//     const safeIteratees = iteratees.map(
//       iteratee => item => _.get(item, iteratee, null)
//     );
//     return _.orderBy(array, safeIteratees, orders);
//   }
// }

// export default SortUtils;

// // 使用案例
// // 按单个字段排序 const sortedByAge = SortUtils.sortArray(users, 'age', 'asc'); console.log('按年龄升序排序:', sortedByAge); // 按多个字段排序 const sortedByNameAndAge = SortUtils.sortByMultipleFields(users, ['name', 'age'], ['asc', 'desc']); console.log('按名称升序和年龄降序排序:', sortedByNameAndAge);
