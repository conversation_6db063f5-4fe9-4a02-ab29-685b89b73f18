import Cookies from "js-cookie";
import { storageLocal } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";

export interface DataInfo<T> {
  /** token */
  token: string;
  /** `token`的过期时间（时间戳） */
  expires: T;
  /** 用于调用刷新token的接口时所需的token */
  refreshToken: string;
  userId: string;
  /** 头像 */
  avatar?: string;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 当前登录用户的角色 */
  roles?: Array<string>;
  /**用户姓名 */
  name?: string;
  /**用户性别 */
  sex?: string;
  /**手机号 */
  phone?: string;
  deptId?: string;
}

export const userKey = "user-info";
export const TokenKey = "authorized-token";
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs";

/** 获取`token` */
export function getToken(): DataInfo<number> {
  // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
  return storageLocal().getItem(TokenKey) || storageLocal().getItem(userKey);
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`token`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`token`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`token`的过期时间（比如2小时））、`expires`（`token`的过期时间）
 * 将`token`、`expires`、`refreshToken`这三条信息放在key值为authorized-token的localStorage里（过期自动销毁）
 * 将`avatar`、`username`、`nickname`、`roles`、`refreshToken`、`expires`这六条信息放在key值为`user-info`的localStorage里（利用`multipleTabsKey`当浏览器完全关闭后自动销毁）
 */
// 导出一个函数，用于设置 Token
export function setToken(data: DataInfo<Date>) {
  // let expires = 0;
  // const { refreshToken } = data.token;
  // console.log(refreshToken);

  const { isRemembered, loginDay } = useUserStoreHook();
  // expires = new Date().getTime(); // 如果后端直接设置时间戳，将此处代码改为expires = data.expires，然后把上面的DataInfo<Date>改成DataInfo<number>即可
  const tokenData = JSON.stringify({
    data: data.token,
    erpToken: data.erpToken,
    mesToken: data.mesToken
  });

  storageLocal().setItem(TokenKey, tokenData); // 将 TokenKey 的值保存到 localStorage

  Cookies.set(
    multipleTabsKey,
    "true",
    isRemembered
      ? {
          expires: loginDay
        }
      : {}
  );

  function setUserKey({
    avatar,
    username,
    name,
    phone,
    sex,
    roles,
    deptId,
    erpToken,
    mesToken,
    token
  }: any) {
    useUserStoreHook().SET_AVATAR(avatar);
    useUserStoreHook().SET_USERNAME(username);
    useUserStoreHook().SET_NAME(name);
    useUserStoreHook().SET_SEX(sex);
    useUserStoreHook().SET_PHONE(phone);

    // useUserStoreHook().SET_NICKNAME(nickname);
    useUserStoreHook().SET_ROLES(roles);
    storageLocal().setItem(userKey, {
      username,
      roles,
      name,
      phone,
      sex,
      deptId,
      avatar,
      token,
      erpToken,
      mesToken
    });
  }

  if (data.roles) {
    const {
      username,
      roles,
      name,
      phone,
      sex,
      deptId,
      token,
      erpToken,
      mesToken
    } = data;
    setUserKey({
      avatar: data?.avatar ?? "/logo.png",
      username,
      roles,
      name,
      phone,
      sex,
      deptId,
      token,
      erpToken,
      mesToken
    });
  } else {
    const userData = storageLocal().getItem<DataInfo<number>>(userKey) || {};
    console.log(userData, "userData");

    const { avatar, username, name, phone, sex, userId, deptId, roles } = data;
    setUserKey({
      avatar,
      username,
      name,
      phone,
      sex,
      roles,
      userId,
      deptId
    });
  }
}

export function setUserKey({
  avatar,
  username,
  name,
  phone,
  sex,
  roles,
  userId,
  deptId
}) {
  useUserStoreHook().SET_AVATAR(avatar + "?time=" + new Date().getTime());
  useUserStoreHook().SET_USERNAME(username);
  useUserStoreHook().SET_NAME(name);
  useUserStoreHook().SET_SEX(sex);
  useUserStoreHook().SET_PHONE(phone);
  useUserStoreHook().SET_DEPT_ID(avatar + "?time=" + new Date().getTime());
  useUserStoreHook().SET_USER_ID(userId);
  // useUserStoreHook().SET_NICKNAME(nickname);
  useUserStoreHook().SET_ROLES(roles);
  storageLocal().setItem(userKey, {
    avatar,
    username,
    name,
    phone,
    userId,
    deptId,
    // avatar,
    sex,
    roles: "admin"
  });
}
/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  storageLocal().removeItem(TokenKey); // 从 localStorage 删除 TokenKey
  Cookies.remove(multipleTabsKey);
  storageLocal().removeItem(userKey);
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return token;
};

/** 清空所有缓存 */
export function clearAllCache() {
  // 清除 localStorage 中的所有数据
  storageLocal().clear();
  // 清除所有 cookies
  Object.keys(Cookies.get()).forEach(cookieName => {
    Cookies.remove(cookieName);
  });
  // 清除 sessionStorage
  sessionStorage.clear();
}

// 监听页面关闭事件
if (typeof window !== "undefined") {
  // 标记刷新
  window.addEventListener("beforeunload", () => {
    sessionStorage.setItem("isRefresh", "true");
  });

  // 页面加载时判断
  window.addEventListener("load", () => {
    if (sessionStorage.getItem("isRefresh")) {
      // 是刷新，不清空缓存
      sessionStorage.removeItem("isRefresh");
    } else {
      // 不是刷新，判定为关闭，清空缓存
      clearAllCache();
    }
  });
}
