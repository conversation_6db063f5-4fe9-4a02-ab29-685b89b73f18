class Responsive {
  private resizeListener: () => void;

  constructor(
    private widthThreshold: number,
    private callback: () => void
  ) {
    this.resizeListener = this.checkWidth.bind(this);
    window.addEventListener("resize", this.resizeListener);
    this.checkWidth(); // Initial check
  }

  private checkWidth() {
    const width = window.innerWidth;
    if (width <= this.widthThreshold) {
      this.callback();
    }
  }

  destroy() {
    window.removeEventListener("resize", this.resizeListener);
  }
}

export default Responsive;
