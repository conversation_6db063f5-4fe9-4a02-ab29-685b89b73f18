class OrderWebSocket {
  constructor(url) {
    this.url = url;
    this.socket = null;
    this.onMessageCallback = null;
    this.onErrorCallback = null;
  }

  connect() {
    this.socket = new WebSocket(this.url);

    this.socket.onopen = () => {
      console.log("WebSocket connected");
    };

    this.socket.onmessage = event => {
      const data = JSON.parse(event.data);
      if (this.onMessageCallback) {
        this.onMessageCallback(data);
      }
    };

    this.socket.onerror = error => {
      console.error("WebSocket error:", error);
      if (this.onErrorCallback) {
        this.onErrorCallback(error);
      }
    };

    this.socket.onclose = () => {
      console.log("WebSocket disconnected");
    };
  }

  sendRequest(requestData) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(requestData));
    } else {
      console.error("WebSocket is not open");
    }
  }

  onMessage(callback) {
    this.onMessageCallback = callback;
  }

  onError(callback) {
    this.onErrorCallback = callback;
  }

  close() {
    if (this.socket) {
      this.socket.close();
    }
  }
}

export default OrderWebSocket;

//下面是使用案例
// 引入 OrderWebSocket 类
import OrderWebSocket from "./OrderWebSocket";

// 创建 WebSocket 实例
const orderWebSocket = new OrderWebSocket("ws://your-backend-url");

// 设置接收消息的回调函数
orderWebSocket.onMessage(data => {
  console.log("Received data:", data);

  // 根据返回的数据类型进行处理
  if (data.type === "abnormalOrders") {
    console.log("异常订单数据:", data.payload);
    // 在这里更新你的 UI，显示异常订单数据
  } else if (data.type === "orderList") {
    console.log("订单列表:", data.payload);
    // 在这里更新你的 UI，显示订单列表
  }
});

// 设置错误处理的回调函数
orderWebSocket.onError(error => {
  console.error("WebSocket error:", error);
  // 在这里处理错误，例如显示错误提示
});

// 连接到 WebSocket 服务器
orderWebSocket.connect();

// 发送请求获取异常订单数据
orderWebSocket.sendRequest({ action: "getAbnormalOrders" });

// 发送请求获取订单列表
orderWebSocket.sendRequest({ action: "getOrderList" });

// 在组件卸载或不再需要时关闭连接
// orderWebSocket.close();
