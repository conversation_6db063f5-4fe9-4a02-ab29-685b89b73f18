<template>
  <div class="lay-ai-container">
    <!-- 浮动按钮 -->
    <div
      class="ai-button"
      :class="{ 'is-active': isOpen }"
      @click="toggleDialog($event)"
    >
      <el-icon><ChatDotRound /></el-icon>
    </div>

    <!-- 弹窗 -->
    <div v-show="isOpen" ref="dialogRef" class="ai-dialog" :style="dialogStyle">
      <!-- 弹窗头部 -->
      <div class="ai-dialog-header" @mousedown="startDialogDrag($event)">
        <span>枢密院AI助手</span>
        <div class="ai-dialog-actions">
          <el-icon @click="toggleDialog($event)">
            <Close />
          </el-icon>
        </div>
      </div>

      <!-- 弹窗内容 -->
      <div class="ai-dialog-content">
        <slot />
      </div>

      <!-- 调整大小控件 -->
      <!--      <div class="resize-controls">-->
      <!--        <el-button size="large" circle @click="resizeDialog('height', -50)">-->
      <!--          <el-icon><ArrowUp /></el-icon>-->
      <!--        </el-button>-->
      <!--        <el-button size="large" circle @click="resizeDialog('height', 50)">-->
      <!--          <el-icon><ArrowDown /></el-icon>-->
      <!--        </el-button>-->
      <!--        <el-button size="large" circle @click="resizeDialog('width', -50)">-->
      <!--          <el-icon><ArrowLeft /></el-icon>-->
      <!--        </el-button>-->
      <!--        <el-button size="large" circle @click="resizeDialog('width', 50)">-->
      <!--          <el-icon><ArrowRight /></el-icon>-->
      <!--        </el-button>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import {
  ChatDotRound,
  Close,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight
} from "@element-plus/icons-vue";

const isOpen = ref(false);
const dialogRef = ref<HTMLElement | null>(null);
const buttonPosition = ref({ x: 0, y: 0 });
const dialogSize = ref({ width: 1344, height: 800 });
const dialogPosition = ref({ x: 0, y: 0 });
const isDialogDragging = ref(false);
const dialogDragStart = ref({ x: 0, y: 0, left: 0, top: 0 });

// 计算弹窗位置
const dialogStyle = computed(() => {
  const { x, y } = buttonPosition.value;
  const { width, height } = dialogSize.value;

  // 如果dialogPosition已设置（已经被拖动过），则使用保存的位置
  if (dialogPosition.value.x !== 0 || dialogPosition.value.y !== 0) {
    return {
      left: `${dialogPosition.value.x}px`,
      top: `${dialogPosition.value.y}px`,
      width: `${width}px`,
      height: `${height}px`
    };
  }

  // 计算弹窗位置，确保不会超出屏幕
  let left = x - width - 20; // 默认在按钮左侧
  let top = y - height - 20; // 默认在按钮上方

  // 如果左侧空间不足，则显示在右侧
  if (left < 0) {
    left = x + 60; // 按钮宽度 + 间距
  }

  // 如果上方空间不足，则显示在下方
  if (top < 0) {
    top = y + 60; // 按钮高度 + 间距
  }

  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`
  };
});

// 开始拖拽对话框
const startDialogDrag = (e: MouseEvent) => {
  // 如果点击的是关闭按钮等控件，不启动拖拽
  if ((e.target as HTMLElement).closest(".ai-dialog-actions")) {
    return;
  }

  e.preventDefault();
  isDialogDragging.value = true;

  // 获取对话框当前位置
  const dialog = dialogRef.value;
  if (dialog) {
    const rect = dialog.getBoundingClientRect();

    // 保存初始值
    dialogDragStart.value = {
      x: e.clientX,
      y: e.clientY,
      left: rect.left,
      top: rect.top
    };

    // 添加事件监听
    document.addEventListener("mousemove", handleDialogDrag);
    document.addEventListener("mouseup", stopDialogDrag);
  }
};

// 处理对话框拖拽
const handleDialogDrag = (e: MouseEvent) => {
  if (!isDialogDragging.value) return;

  e.preventDefault();

  // 计算拖拽距离
  const deltaX = e.clientX - dialogDragStart.value.x;
  const deltaY = e.clientY - dialogDragStart.value.y;

  // 更新对话框位置
  dialogPosition.value = {
    x: dialogDragStart.value.left + deltaX,
    y: dialogDragStart.value.top + deltaY
  };
};

// 停止对话框拖拽
const stopDialogDrag = () => {
  isDialogDragging.value = false;
  document.removeEventListener("mousemove", handleDialogDrag);
  document.removeEventListener("mouseup", stopDialogDrag);
};

// 切换弹窗状态
const toggleDialog = (e: MouseEvent) => {
  // 阻止事件冒泡，防止被handleClickOutside捕获
  e.stopPropagation();

  isOpen.value = !isOpen.value;
  console.debug("isOpen", isOpen.value);

  if (isOpen.value) {
    // 获取按钮位置
    const button = document.querySelector(".ai-button");
    if (button) {
      const rect = button.getBoundingClientRect();
      buttonPosition.value = {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
    }

    // 重置对话框位置（如果想要每次打开时保持上次位置可以注释掉）
    dialogPosition.value = { x: 0, y: 0 };

    // 使用nextTick确保在DOM更新后再获取dialogRef
    nextTick(() => {
      console.debug("nextTick dialogRef", dialogRef.value);
    });
  }
};

// 调整弹窗大小
const resizeDialog = (dimension: "width" | "height", amount: number) => {
  if (dimension === "width") {
    // 限制宽度在300-800之间
    const newWidth = dialogSize.value.width + amount;
    dialogSize.value.width = Math.max(300, Math.min(800, newWidth));
  } else {
    // 如果是增加高度且当前高度已经达到600，则不响应
    if (amount > 0 && dialogSize.value.height >= 600) {
      console.debug("高度已达上限", dialogSize.value.height);
      return;
    }

    // 限制高度在200-600之间
    const newHeight = dialogSize.value.height + amount;
    dialogSize.value.height = Math.max(200, Math.min(600, newHeight));
  }
};

// 点击外部关闭弹窗
const handleClickOutside = (e: MouseEvent) => {
  // 先检查是否点击的是按钮本身
  const button = document.querySelector(".ai-button");
  if (button && button.contains(e.target as Node)) {
    return;
  }

  if (
    isOpen.value &&
    dialogRef.value &&
    !dialogRef.value.contains(e.target as Node)
  ) {
    console.debug("点击外部关闭弹窗");
    isOpen.value = false;
  }
};

onMounted(() => {
  // 使用捕获阶段而不是冒泡阶段
  document.addEventListener("click", handleClickOutside, true);
  console.debug("组件已挂载", dialogRef.value);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside, true);
  document.removeEventListener("mousemove", handleDialogDrag);
  document.removeEventListener("mouseup", stopDialogDrag);
});
</script>

<style scoped>
.lay-ai-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9999;
}

.ai-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  color: white;
  cursor: pointer;
  background-color: var(--el-color-primary);
  border-radius: 50%;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  transition: all 0.3s;
}

.ai-button:hover {
  transform: scale(1.1);
}

.ai-button.is-active {
  transform: rotate(45deg);
}

.ai-dialog {
  position: fixed;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: transparent;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.ai-dialog-header {
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: move;
  user-select: none;
  background-color: #f5f7fa;
  border-bottom: 1px solid var(--el-border-color-light);
}

.ai-dialog-actions {
  display: flex;
  gap: 8px;
}

.ai-dialog-actions .el-icon {
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
}

.ai-dialog-actions .el-icon:hover {
  background-color: var(--el-fill-color-light);
}

.ai-dialog-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.resize-controls {
  z-index: 1000;
  display: flex;
  gap: 8px;
  justify-content: center;
  padding: 4px;
  background-color: rgb(255 255 255 / 90%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}
</style>
