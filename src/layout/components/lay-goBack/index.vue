<template>
  <div>
    <!-- 返回按钮 -->
    <el-button
      v-if="canGoBackComputed && isGoBack"
      class="button"
      type="primary"
      size="small"
      @click="goBack"
      ><el-icon><ArrowLeft /></el-icon>返回</el-button
    >

    <!-- 插槽，用于嵌入子组件 -->
    <slot />
  </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router";
import { ref, computed, onMounted, watch } from "vue";
import { ElIcon } from "element-plus"; // 确保引入Element Plus图标组件
import { ArrowLeft } from "@element-plus/icons-vue";
const router = useRouter();
const route = useRoute();
console.log(router.history);
console.log(route, "route");

const canGoBack = ref(false);
const aa = window.history.length;
function checkCanGoBack() {
  // 使用-1来判断是否有上一级页面
  canGoBack.value =
    window.history.length > 1 && window.history.state.back !== null;
}
function goBack() {
  if (canGoBack.value) {
    router.go(-1); // 使用 go(-1) 返回上一页
  }
}
const hiddenGoBackArr = ["/chat/deepseek"];

const isGoBack = computed(() => {
  // 检查当前路径是否在 hiddenGoBackArr 数组中
  return !hiddenGoBackArr.some(path => route.path.includes(path));
});

// 在组件挂载时进行检查
onMounted(() => {
  checkCanGoBack();
});
watch(
  () => route.path,
  () => {
    checkCanGoBack();
  }
);
// 动态判断是否可以返回
const canGoBackComputed = computed(() => window.history.length > 1);
</script>

<style scoped>
.button:hover {
  font-size: 16px;
  cursor: pointer;
}

.button {
  position: absolute;
  top: 85px;
  left: 5px;

  /* margin-bottom: 200px; */
  z-index: 999;
  display: flex;
  align-items: center;

  /* width: 100px; */
  justify-content: center;
  padding: 10px;
  cursor: pointer;

  /* background-color: #fff; */
  border: none;
}
</style>
