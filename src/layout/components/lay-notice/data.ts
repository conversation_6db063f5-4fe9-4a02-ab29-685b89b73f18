import {
  // getAbnormalSituationOverview,
  getAbnormalSituationOverviewList
} from "@/api/abnormal-situation-overview";

export interface ListItem {
  avatar: string;
  title: string;
  datetime: string;
  type: string;
  description: string;
  status?: "primary" | "success" | "warning" | "info" | "danger";
  extra?: string;
}

export interface TabItem {
  key: string;
  name: string;
  list: ListItem[];
  emptyText: string;
}

export const queryData = {
  page: { current: 1, size: 5, default: 20 },
  query: [],
  order: [
    {
      name: "createDate",
      sort: "desc"
    }
  ]
};

export const noticesData: TabItem[] = [
  {
    key: "1",
    name: "待办",
    list: [],
    emptyText: "暂无待办"
  },
  {
    key: "2",
    name: "消息",
    list: [],
    emptyText: "暂无消息"
  }
];

async function fetchAndSetNotices() {
  try {
    const res = await getAbnormalSituationOverviewList(queryData);

    // 遍历 response.data.records 并生成需要的对象
    res.data.records.forEach(record => {
      const formattedDate = record.createDate.replace("T", " ");
      noticesData[0].list.push({
        avatar: "",
        title: record.errType,
        description: record.errMsg,
        datetime: formattedDate,
        extra: record.resolved ? "已解决" : "待解决",
        status: record.resolved ? "success" : "danger",
        type: "3"
      });
    });
    console.log(33333);

    // 将数据保存到本地存储
    localStorage.setItem("noticesData", JSON.stringify(noticesData));
    console.log(noticesData); // 打印结果以查看是否正确
  } catch (error) {
    console.error("Error fetching data:", error);
  }
}

// 调用异步函数获取并设置通知数据
fetchAndSetNotices();
