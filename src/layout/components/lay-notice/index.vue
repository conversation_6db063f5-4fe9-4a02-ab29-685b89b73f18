<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { noticesData, queryData } from "./data";
import NoticeList from "./components/NoticeList.vue";
import BellIcon from "@iconify-icons/ep/bell";
import {
  getAbnormalSituationOverview,
  getAbnormalSituationOverviewList
} from "@/api/abnormal-situation-overview";
const noticesNum = ref(0);
const notices = ref(noticesData);
// 默认选中哪个
const activeKey = ref(noticesData[0]?.key);

console.log(notices.value, "noticesNum.value");

const getLabel = computed(
  () => item =>
    item.name + (item.list.length > 0 ? `(${item.list.length})` : "")
);

onMounted(async () => {
  const res = await getAbnormalSituationOverviewList(queryData);
  notices.value[0].list = res.data.records.map(record => {
    return {
      ...record,
      avatar: "",
      title: record.errType,
      description: record.errMsg,
      datetime: record.createDate,
      extra:
        record.resolved === 0
          ? "待处理"
          : record.resolved === 1
            ? "已分配"
            : record.resolved === 2
              ? "处理中"
              : record.resolved === 3
                ? "已处理"
                : "未知状态",
      status: record.resolved ? "success" : "danger",
      type: "3"
    };
  });
  notices.value.map(v => (noticesNum.value += v.list.length));
});

const handleDropdownVisibleChange = async (visible: boolean) => {
  console.log(notices.value, "boo");
  if (visible) {
    const res = await getAbnormalSituationOverviewList(queryData);
    notices.value[0].list = res.data.records.map(record => {
      return {
        ...record,
        avatar: "",
        title: record.errType,
        description: record.errMsg,
        datetime: record.createDate,
        extra:
          record.resolved === 0
            ? "待处理"
            : record.resolved === 1
              ? "已分配"
              : record.resolved === 2
                ? "处理中"
                : record.resolved === 3
                  ? "已处理"
                  : "未知状态",
        status: record.resolved ? "success" : "danger",
        type: "3"
      };
    });
  }
};
</script>

<template>
  <el-dropdown
    trigger="click"
    placement="bottom-end"
    @visible-change="handleDropdownVisibleChange"
  >
    <span
      :class="[
        'dropdown-badge',
        'navbar-bg-hover',
        'select-none',
        Number(noticesNum) !== 0 && 'mr-[10px]'
      ]"
    >
      <el-badge :value="Number(noticesNum) === 0 ? '' : noticesNum" :max="99">
        <span :class="['header-notice-icon', { flash: notices.length > 0 }]">
          <IconifyIconOffline :icon="BellIcon" />
        </span>
      </el-badge>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-tabs
          v-model="activeKey"
          :stretch="true"
          class="dropdown-tabs"
          :style="{ width: notices.length === 0 ? '200px' : '330px' }"
        >
          <el-empty
            v-if="notices.length === 0"
            description="暂无消息"
            :image-size="60"
          />
          <span v-else>
            <template v-for="item in notices" :key="item.key">
              <el-tab-pane :label="getLabel(item)" :name="`${item.key}`">
                <el-scrollbar max-height="330px">
                  <div class="noticeList-container">
                    <NoticeList :list="item.list" :emptyText="item.emptyText" />
                  </div>
                </el-scrollbar>
              </el-tab-pane>
            </template>
          </span>
        </el-tabs>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style lang="scss" scoped>
.dropdown-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 48px;
  cursor: pointer;

  .header-notice-icon {
    font-size: 18px;
  }
}

.dropdown-tabs {
  .noticeList-container {
    padding: 15px 24px 0;
  }

  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap)::after {
    height: 1px;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 36px;
  }
}

@keyframes flash {
  0% {
    opacity: 1;
  }

  10% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  30% {
    opacity: 0;
  }

  40% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  60% {
    opacity: 1;
  }

  70% {
    opacity: 0;
  }

  80% {
    opacity: 1;
  }

  90% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.flash {
  animation: flash 8s; /* 将持续时间缩短至 2 秒 */
}
</style>
