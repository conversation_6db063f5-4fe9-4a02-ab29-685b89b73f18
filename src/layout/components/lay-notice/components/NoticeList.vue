<script setup lang="ts">
import { PropType, ref, watch } from "vue";
import { ListItem } from "../data";
import NoticeItem from "./NoticeItem.vue";
import { useRouter } from "vue-router";
const router = useRouter();

const props = defineProps({
  list: {
    type: Array as PropType<Array<ListItem>>,
    default: () => []
  },
  emptyText: {
    type: String,
    default: ""
  }
});

const list = ref(props.list);

const goMoreList = () => {
  router.push("/notification-list");
};

const noticeClick = item => {
  router.push({
    path: "/abnormal-situation-overview",
    query: {
      id: item.id,
      errMsg: item.errMsg
    }
  });
};

// 监听 props.list 的变动，并更新 list
watch(
  () => props.list,
  newList => {
    console.log(newList, "newList");

    list.value = newList;
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div>
    <NoticeItem
      v-for="(item, index) in list"
      :key="index"
      :noticeItem="item"
      @noticeClick="noticeClick"
    />
    <div
      class="flex"
      style="
        justify-content: center;
        width: 100%;
        margin: 5px 0;
        color: #09c;
        cursor: pointer;
      "
      @click="goMoreList"
    >
      查看更多
    </div>
  </div>
  <!-- <el-empty v-else :description="emptyText" /> -->
</template>
