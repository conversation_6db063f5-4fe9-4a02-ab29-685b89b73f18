<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="changesBillOfMaterialReviewMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :showTotal="false"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      height="auto"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { materialList } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";
import { useParentColumnTag } from "@/utils/hooks";

import {
  getChangesBillOfMaterialAPI,
  getChangesBillOfMaterialFieldAPI,
  exportChangesBillOfMaterialAPI
} from "@/api/change-audit/changes-bill-of-material-review";

/** 表格状态数据 */
import { changesBillOfMaterialReviewMap } from "@/views/change-audit/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useRouter } from "vue-router";

// 日期
const date = ref([]);
defineOptions({
  name: "MaterialList"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const { setParentColumnTag } = useParentColumnTag();
// 定义表格配置
const router = useRouter();
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "issuanceStatus",
      sort: "desc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  materialList,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getChangesBillOfMaterialFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getChangesBillOfMaterialAPI,
  {
    states: changesBillOfMaterialReviewMap
  },
  false,
  { date: date }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportChangesBillOfMaterialAPI, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  const columnList = ["billNo", "moBillNo", "moEntrySeq"];
  setParentColumnTag(
    row,
    columns.value,
    columnList,
    "MaterialsListChangeReviewColumnFields"
  );
  // 跳转至用料清单明细
  router.push({
    path: "/changes-bill-of-material-review/material-list-details",
    query: {
      billNo: row.billNo,
      prodOrderNo: row.moBillNo + row.moEntrySeq
    }
  });
  console.log("row.moBillNo+row.moEntrySeq", row.moBillNo + row.moEntrySeq);
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {};
</script>
