<script setup lang="ts">
import { ref } from "vue";
import {
  useDark,
  useGlobal,
  deviceDetection,
  useResizeObserver
} from "@pureadmin/utils";
defineOptions({
  name: "hj"
});
import {
  Delete,
  Download,
  Message,
  Plus,
  ZoomIn
} from "@element-plus/icons-vue";

import type { UploadFile } from "element-plus";
const isDialogVisible = ref(false);
const isMobile = deviceDetection();
const activeIndex = ref(0); // 当前的卡片索引

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const disabled = ref(false);
const fileList = ref<UploadFile[]>([]);

// 移动端选择拍照/上传
const mobileActionVisible = ref(false);

// 处理文件选择的函数
// 处理文件选择
function handleChange(event) {
  const files = event.target.files;
  if (files && files.length > 0) {
    Array.from(files).forEach(file => {
      if (1) {
        const reader = new FileReader();

        // 对图片进行base64转换
        reader.readAsDataURL(file);
        reader.onload = () => {
          fileList.value.push({
            name: file.name,
            type: file.type,
            url: reader.result // 存储base64编码
          });
        };
        console.log(fileList.value, "fileList.value");
      } else {
        Message.error("请选择图片文件！"); // 如果不是图片文件，提示错误
      }
    });
  }
}

const handleRemove = (file: UploadFile) => {
  // 使用 filter 删除 fileList 中与 file.name 相同的文件
  fileList.value = fileList.value.filter(item => item.name !== file.name);
  // console.log(file);
};

const handlePictureCardPreview = (file: UploadFile) => {
  dialogImageUrl.value = file.url!;
  dialogVisible.value = true;
};

const handleDownload = (file: UploadFile) => {
  console.log(file);
};

// 自定义点击处理
const handleCustomClick = e => {
  console.log(isMobile, "isMobile");

  e.preventDefault();
  // PC端直接触发文件选择
  const input = document.createElement("input");
  input.type = "file";
  input.accept = "";
  input.onchange = handleChange;
  // document.body.appendChild(input); // 将元素添加到 DOM 中
  input.click();
};
</script>

<template>
  <div class="box">
    <div class="flex flex-wrap justify-between m-4">
      <div
        v-for="item in 4"
        :key="item"
        class="w-full xs:w-1/2 sm:w-1/3 md:w-1/3 lg:w-1/4 p-4"
      >
        <el-card>
          <div class="flex flex-col items-center space-x-4">
            <!-- <img
              class="w-full h-auto rounded-lg"
              src="F:\下载\新建文件夹\动漫素材\梗图\preview (2).jpg"
              alt="图片描述"
            /> -->
            <div class="flex-1 text-left text-gray-600 text-sm">
              <p>这里是图片描述</p>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <el-button class="mt-10" @click="isDialogVisible = true"> 图片 </el-button>
  </div>

  <!-- 对话框 -->
  <el-dialog
    v-model="isDialogVisible"
    title="上传图片"
    :width="isMobile ? '100%' : '60%'"
    @close="handleDialogClose"
  >
    <el-form>
      <el-form-item label="图片">
        <div class="flex flex-wrap gap-4">
          <el-upload
            v-model:file-list="fileList"
            :class="isMobile ? 'w-full w-1/2' : 'w-full'"
            :disabled="true"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :on-change="handleChange"
          >
            <!-- 自定义触发按钮 -->
            <template #trigger>
              <div class="custom-trigger" @click="handleCustomClick">
                <el-icon><Plus /></el-icon>
              </div>
            </template>

            <template #file="{ file }">
              <div>
                <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  alt=""
                />
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handlePictureCardPreview(file)"
                  >
                    <el-icon><zoom-in /></el-icon>
                  </span>
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleDownload(file)"
                  >
                    <el-icon><Download /></el-icon>
                  </span>
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>

          <el-dialog v-model="dialogVisible">
            <img :src="dialogImageUrl" alt="Preview Image" />
          </el-dialog>
        </div>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" />
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <span class="dialog-footer">
        <el-button @click="isDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="uploadImage">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.custom-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  height: 100%;
  cursor: pointer;
}
</style>
