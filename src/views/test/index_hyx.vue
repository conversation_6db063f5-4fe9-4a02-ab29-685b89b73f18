<script setup lang="ts">
import { ref } from "vue";
import VueFlow from "@/components/ReVueFlow/workshop-production-flow/index.vue";
import VueFlow2 from "@/components/ReVueFlow/sell-out-flow/index.vue";

defineOptions({
  name: "hyx"
});
const nodeName = ref("车间生产");

const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<template>
  <VueFlow @click-node="clickNodes" />
  <VueFlow2 @click-node="clickNodes" />
</template>

<style lang="scss" scoped></style>
