import axios from "axios";
import CryptoJ<PERSON> from "crypto-js";
const XF_API_KEY = import.meta.env.VITE_XF_API_KEY;
const XF_API_SECRET = import.meta.env.VITE_XF_API_SECRET;
const XF_APP_ID = import.meta.env.VITE_XF_APP_ID;

const getAuthUrl = () => {
  const url = "https://openapi.xfyun.cn/v2/aiui";
  const date = new Date().toGMTString();
  const signatureOrigin = `host: openapi.xfyun.cn\ndate: ${date}\nGET /v2/aiui HTTP/1.1`;
  const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, XF_API_SECRET);
  const signature = CryptoJS.enc.Base64.stringify(signatureSha);
  const authorizationOrigin = `api_key="${XF_API_KEY}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`;
  const authorization = btoa(authorizationOrigin);
  return `${url}?authorization=${authorization}&date=${date}&host=openapi.xfyun.cn`;
};

export const sendMessage = async message => {
  const url = getAuthUrl();
  const response = await axios.post(url, {
    header: {
      app_id: XF_APP_ID
    },
    parameter: {
      chat: {
        domain: "general"
      }
    },
    payload: {
      message: {
        text: message
      }
    }
  });
  return response.data;
};
