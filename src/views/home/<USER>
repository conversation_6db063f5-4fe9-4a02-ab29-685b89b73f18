<script setup lang="ts">
import { useDark, randomGradient } from "./components/Card/utils";
import DateTimePicker from "./components/DateTimePicker/index.vue";
import AbnormalTable from "./components/AbnormalTable/index.vue";
import CommonFunctions from "./components/CommonFunctions/index.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import Card from "./components/Card/index.vue";
import SaleTable from "./components/SaleTable/index.vue";
import { ref, computed, onMounted, nextTick } from "vue";
import { useVisibility } from "@/utils/useVisibility";
import AbnormalSituationOverview from "@/views/abnormal-situation-overview/index.vue";
import Sortable from "sortablejs";
import { Edit, Rank, View, Hide } from "@element-plus/icons-vue";

// import iconifyIconOffline from "@/components/ReIcon/src/iconifyIconOffline";
defineOptions({
  name: "welcome"
});
const date = ref([]);
const updateDate = time => {
  date.value = time;
  console.log(date.value, "time");
};

const { isVisible } = useVisibility();

const { isDark } = useDark();

// table配置项
const abnormalFields = ref([]);
const abnormalTableData = ref([]);
import {
  getAbnormalSituationOverviewFieldAPI,
  getAbnormalSituationOverview,
  updateHomeSort,
  getHomeSort
} from "@/api/abnormal-situation-overview";
import { abnormalsituationoverviewMap } from "@/views/abnormal-situation-overview/utils";
import { useButtonPermission } from "@/utils/hooks";
import { storageLocal } from "@pureadmin/utils";
import type { optionsItem } from "@/layout/components/lay-search/types";
import file from "@iconify-icons/ri/file-list-2-fill";
import file1 from "@iconify-icons/ri/file-list-3-fill";
import file2 from "@iconify-icons/ri/folder-chart-fill";
import file3 from "@iconify-icons/ri/file-list-fill";
import file4 from "@iconify-icons/ri/file-copy-2-fill";
import file5 from "@iconify-icons/ri/file-text-fill";
import file6 from "@iconify-icons/ri/file-upload-fill";
import file7 from "@iconify-icons/ri/layout-top-fill";
import file8 from "@iconify-icons/ri/layout-top-2-fill";
import file9 from "@iconify-icons/ri/archive-fill";
import file10 from "@iconify-icons/ri/article-fill";

// 获取表头信息
const getWidth = field => {
  switch (field) {
    case "id":
      return 100;
    case "errMsg":
      return 320;
    case "errType":
      return 100;
    case "handleDate":
    case "createDate":
      return 160;
    case "orderNo":
      return 180;
    case "remark":
      return 200;
    default:
      return "";
  }
};

getAbnormalSituationOverviewFieldAPI().then(res => {
  abnormalFields.value = res.data.map(item => {
    return {
      ...item,
      width: getWidth(item.field)
    };
  });
});

// 获取表格数据
const params = {
  page: {
    current: 1,
    size: 4,
    default: 20
  },
  query: [],
  order: [
    {
      name: "createDate",
      sort: "desc"
    },
    {
      name: "id",
      sort: "desc"
    }
  ]
};
getAbnormalSituationOverview(params).then(res => {
  // 格式化时间
  abnormalTableData.value = res.data.records.map(item => {
    return {
      ...item,
      createDate: item.createDate ? item.createDate.replace(/T/g, " ") : "",
      handleDate: item.handleDate ? item.handleDate.replace(/T/g, " ") : "",
      // 处理状态映射
      resolved: abnormalsituationoverviewMap[0][item.resolved]
    };
  });
});

// 常用功能
/**
 * 截取字符串，获取冒号之前的数据
 * @param str 输入的字符串
 * @returns 冒号之前的数据，如果没有冒号则返回原字符串
 */
function getStringBeforeColon(str: string): string {
  const index = str.indexOf(":");
  return index === -1 ? str : str.slice(0, index).trim();
}

const icon = [
  file,
  file1,
  file2,
  file3,
  file4,
  file5,
  file6,
  file7,
  file8,
  file9,
  file10
];

function getStorageItem(key) {
  return storageLocal().getItem<optionsItem[]>(key) || [];
}

// 定义板块类型
interface Section {
  id: string;
  title: string;
  component: string;
  isHidden?: boolean;
  cardList?: object;
  props?: {
    iconList?: any[];
    title?: string;
  };
}

// 定义路由类型
interface RouteItem extends optionsItem {
  children?: {
    meta: {
      btnRoles: Array<{
        title: string;
        auths: string;
      }>;
    };
  }[];
}

const asyncRoutes = getStorageItem("async-routes") as RouteItem[];

const btnRoles =
  asyncRoutes.find(item => item.path === "/common-functions")?.children?.[0]
    ?.meta?.btnRoles || [];

let ERPIconList = btnRoles
  .filter(item => item.title.startsWith("ERP"))
  .map((item, index) => {
    return {
      isHidden: false,
      title: item.title.substring(3),
      icon: icon[index],
      color: ["#ff7f32", "#ffb84d"],
      req: getStringBeforeColon(item.auths),
      systemType: "erp"
    };
  });

let MESIconList = btnRoles
  .filter(item => item.title.startsWith("MES"))
  .map((item, index) => {
    return {
      isHidden: false,
      title: item.title.substring(3),
      icon: icon[index],
      color: ["#ff7f32", "#ffb84d"],
      req: getStringBeforeColon(item.auths),
      systemType: "mes"
    };
  });

const handleHiddenCommonFunctions = (value, title) => {
  if (title === "ERP") {
    ERPIconList = value;
  } else if (title === "MES") {
    MESIconList = value;
  }
};
// 板块排序相关
const isEditing = ref(false);
const showHiddenDrawer = ref(false);
const sections = ref<Section[]>([
  {
    id: "cards",
    title: "数据卡片",
    component: "Card",
    isHidden: false,
    cardList: {
      zero: true,
      one: true,
      two: true,
      three: true,
      four: true,
      five: true
    }
  },
  {
    id: "abnormal",
    title: "异常情况",
    component: "AbnormalTable",
    isHidden: false
  },
  {
    id: "erp",
    title: "ERP功能",
    component: "CommonFunctions",
    isHidden: false,
    props: { iconList: ERPIconList, title: "ERP" }
  },
  {
    id: "mes",
    title: "MES功能",
    component: "CommonFunctions",
    isHidden: false,
    props: { iconList: MESIconList, title: "MES" }
  }
]);

// 切换编辑模式
const toggleEdit = () => {
  isEditing.value = !isEditing.value;
  if (isEditing.value) {
    nextTick(() => {
      initSortable();
    });
  } else {
    // 保存排序
    updateHomeSortRequest(JSON.stringify(sections.value));
  }
};

const getHomeSortRequest = () => {
  getHomeSort().then(res => {
    if (res.data) {
      sections.value = JSON.parse(res.data);
    }
  });
};

getHomeSortRequest();

const updateHomeSortRequest = data => {
  const params = {
    sort: data
  };
  updateHomeSort(params).then(res => {
    if (res.code === 200) {
      getHomeSortRequest();
    }
  });
};

// 切换板块显示状态
const toggleSectionVisibility = (section: Section) => {
  section.isHidden = !section.isHidden;
};

// 获取隐藏的板块
const hiddenSections = computed(() => {
  return sections.value.filter(section => section.isHidden);
});

// 初始化拖拽
const initSortable = () => {
  const el = document.querySelector(".sections-container") as HTMLElement;
  if (!el) return;
  Sortable.create(el, {
    animation: 150,
    handle: ".drag-handle",
    onEnd: evt => {
      nextTick(() => {
        // 深拷贝一份数据
        const cloneData = JSON.parse(JSON.stringify(sections.value));
        const changeData = cloneData.find(
          (item, index) => index === evt.oldIndex
        );
        sections.value.splice(evt.oldIndex, 1);
        sections.value.splice(evt.newIndex, 0, changeData);
      });
    }
  });
};

// 组件挂载时加载排序
onMounted(() => {
  getHomeSortRequest();
});
</script>

<template>
  <div class="home-container">
    <el-row v-show="isVisible" class="flex-end">
      <div class="flex items-center gap-4 mr-8">
        <div
          v-if="hiddenSections.length > 0 && isEditing"
          class="flex items-center text-blue-600 hover:text-blue-400 cursor-pointer"
          @click="showHiddenDrawer = true"
        >
          显示隐藏板块
          <el-icon class="ml-1"><View /></el-icon>
        </div>
        <div
          class="flex items-center text-blue-600 hover:text-blue-400 cursor-pointer"
          @click="toggleEdit"
        >
          {{ isEditing ? "完成编辑" : "编辑排版" }}
          <el-icon class="ml-1"><Edit /></el-icon>
        </div>
      </div>
      <DateTimePicker @updateDate="updateDate" />
    </el-row>

    <div class="sections-container">
      <transition-group name="el-fade-in">
        <div
          v-for="section in sections.filter(s => !s.isHidden)"
          :key="section.id"
          class="section-item"
          :class="{ 'is-editing': isEditing }"
        >
          <div v-if="isEditing" class="section-controls">
            <div class="drag-handle">
              <el-icon><Rank /></el-icon>
            </div>
            <div
              class="visibility-toggle"
              @click="toggleSectionVisibility(section)"
            >
              <el-icon><Hide /></el-icon>
            </div>
          </div>

          <el-row
            v-if="section.component === 'Card'"
            :gutter="50"
            class="card-row"
          >
            <Card
              v-show="section.cardList.zero || isEditing"
              class="card-item"
              :date="date"
              :index="0"
              :isEditing="isEditing"
              :isVisible="section.cardList.zero"
              @changeVisible="
                value => {
                  section.cardList.zero = value;
                }
              "
            />
            <Card
              v-show="section.cardList.one || isEditing"
              class="card-item"
              :date="date"
              :index="1"
              :isEditing="isEditing"
              :isVisible="section.cardList.one"
              @changeVisible="
                value => {
                  section.cardList.one = value;
                }
              "
            />
            <Card
              v-show="section.cardList.two || isEditing"
              class="card-item"
              :date="date"
              :index="2"
              :isEditing="isEditing"
              :isVisible="section.cardList.two"
              @changeVisible="
                value => {
                  section.cardList.two = value;
                }
              "
            />
            <Card
              v-show="section.cardList.three || isEditing"
              class="card-item"
              :date="date"
              :index="3"
              :isEditing="isEditing"
              :isVisible="section.cardList.three"
              @changeVisible="
                value => {
                  section.cardList.three = value;
                }
              "
            />
            <Card
              v-show="section.cardList.four || isEditing"
              class="card-item"
              :date="date"
              :index="4"
              :isEditing="isEditing"
              :isVisible="section.cardList.four"
              @changeVisible="
                value => {
                  section.cardList.four = value;
                }
              "
            />
            <Card
              v-show="section.cardList.five || isEditing"
              class="card-item"
              :date="date"
              :index="5"
              :isEditing="isEditing"
              :isVisible="section.cardList.five"
              @changeVisible="
                value => {
                  section.cardList.five = value;
                }
              "
            />
          </el-row>

          <el-row v-if="section.component === 'AbnormalTable'">
            <AbnormalTable
              :column="abnormalFields"
              :tableData="abnormalTableData"
            />
          </el-row>

          <el-row v-else-if="section.component === 'CommonFunctions'">
            <CommonFunctions
              :isEditing="isEditing"
              :iconList="section.props.iconList"
              :title="section.props.title"
              @handleHiddenCommonFunctions="handleHiddenCommonFunctions"
            />
          </el-row>
        </div>
      </transition-group>
    </div>

    <!-- 隐藏板块抽屉 -->
    <el-drawer
      v-model="showHiddenDrawer"
      title="隐藏的板块"
      direction="rtl"
      size="300px"
    >
      <div class="hidden-sections-list">
        <div
          v-for="section in hiddenSections"
          :key="section.id"
          class="hidden-section-item"
        >
          <span>{{ section.title }}</span>
          <el-button
            type="primary"
            link
            @click="toggleSectionVisibility(section)"
          >
            显示
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  --el-card-border-color: none;

  /* 解决概率进度条宽度 */
  .el-progress--line {
    width: 85%;
  }

  /* 解决概率进度条字体大小 */
  .el-progress-bar__innerText {
    font-size: 15px;
  }

  /* 隐藏 el-scrollbar 滚动条 */
  .el-scrollbar__bar {
    display: none;
  }

  /* el-timeline 每一项上下、左右边距 */
  .el-timeline-item {
    margin: 0 6px;
  }
}

.main-content {
  margin: 20px 20px 0 !important;
}

.flex-end {
  justify-content: flex-end;
  margin-bottom: 10px;
}

.card-row {
  /* 给前 3 个盒子添加下间距 */
  .card-item:nth-child(-n + 3) {
    margin-bottom: 20px;
  }
  @media (max-width: 768px) {
    .card-item:nth-child(-n + 5) {
      margin-bottom: 20px;
    }
  }
}

.home-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-container {
  flex: 1;
  min-height: 0; // 重要：防止flex子元素溢出
  overflow: hidden;
}

.sections-container {
  position: relative;
}

.section-item {
  position: relative;
  margin-bottom: 20px;
}

.is-editing {
  margin-top: 40px;
}

.section-controls {
  position: absolute;
  top: -30px;
  right: 0;
  z-index: 10;
  display: flex;
  gap: 8px;
}

.drag-handle,
.visibility-toggle {
  cursor: pointer;
  padding: 5px;
  color: #ffffff;
  background: var(--el-color-primary);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    background: var(--el-color-primary-light-4);
    color: var(--el-color-primary-light-9);
  }
}

.hidden-sections-list {
  padding: 16px;
}

.hidden-section-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-bg-color);
  }
}

.el-fade-in-move {
  transition: transform 0.3s ease;
}
</style>
