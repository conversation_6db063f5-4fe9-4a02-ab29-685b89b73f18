import { dayjs, cloneDeep, getRandomIntBetween } from "./utils";
import GroupLine from "@iconify-icons/ri/group-line";
import Smile from "@iconify-icons/ri/star-smile-line";
import IonMaleOutline from "@iconify-icons/ion/male-outline";
import IonFemaleOutline from "@iconify-icons/ion/female-outline";
import IonDiamondOutline from "@iconify-icons/ion/diamond-outline";
import { ref } from "vue";
const days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
/** 需求人数、提问数量、解决数量、用户满意度 */
const chartData = ref([
  {
    id: 1,
    icon: GroupLine,
    bgColor: "#effaff",
    color: "#41b6ff",
    duration: 2200,
    name: "用户人数",
    value: 36000,
    data: [99] // 平滑折线图数据
  },
  {
    id: 2,
    icon: IonMaleOutline,
    bgColor: "#fff5f4",
    color: "#e85f33",
    duration: 1600,
    name: "男性用户",
    value: 16580,
    data: [2216, 1148, 1255, 788, 4821, 1973, 4379]
  },
  {
    id: 3,
    icon: IonFemaleOutline,
    bgColor: "#eff8f4",
    color: "#26ce83",
    duration: 1500,
    name: "女性用户",
    value: 16499,
    data: [861, 1002, 3195, 1715, 3666, 2415, 3645]
  },
  {
    id: 4,
    icon: Smile,
    bgColor: "#f6f4fe",
    color: "#7846e5",
    duration: 100,
    name: "平均年龄",
    value: 100,
    data: "expired"
  },
  {
    id: 5,
    icon: IonDiamondOutline,
    bgColor: "#f6f4fe",
    color: "#7846e5",
    duration: 100,
    name: "营收数据",
    value: 99999,
    data: [89]
  },
  {
    id: 6,
    icon: IonDiamondOutline,
    bgColor: "#f6f4fe",
    color: "#7846e5",
    duration: 100,
    name: "营收数据",
    value: 99999,
    data: [89]
  }
]);

/** 分析概览 */
const barChartData = ref([
  {
    requireData: [2101, 5288, 4239, 4962, 6752, 5208, 7450],
    questionData: [2216, 1148, 1255, 1788, 4821, 1973, 4379]
  },
  {
    requireData: [
      2101, 3280, 4400, 4962, 5752, 6889, 323, 3232, 2321, 5454, 2344, 3232
    ],
    questionData: [2116, 3148, 3255, 3788, 4821, 4970, 5390]
  }
]);

/** 解决概率 */
const progressData = [
  {
    week: "周一",
    percentage: 85,
    duration: 110,
    color: "#41b6ff"
  },
  {
    week: "周二",
    percentage: 86,
    duration: 105,
    color: "#41b6ff"
  },
  {
    week: "周三",
    percentage: 88,
    duration: 100,
    color: "#41b6ff"
  },
  {
    week: "周四",
    percentage: 89,
    duration: 95,
    color: "#41b6ff"
  },
  {
    week: "周五",
    percentage: 94,
    duration: 90,
    color: "#26ce83"
  },
  {
    week: "周六",
    percentage: 96,
    duration: 85,
    color: "#26ce83"
  },
  {
    week: "周日",
    percentage: 100,
    duration: 80,
    color: "#26ce83"
  }
].reverse();

/** 数据统计 */
const tableData = Array.from({ length: 30 }).map((_, index) => {
  return {
    id: index + 1,
    requiredNumber: getRandomIntBetween(13500, 19999),
    questionNumber: getRandomIntBetween(12600, 16999),
    resolveNumber: getRandomIntBetween(13500, 17999),
    satisfaction: getRandomIntBetween(95, 100),
    date: dayjs().subtract(index, "day").format("YYYY-MM-DD")
  };
});

/** 最新动态 */
const latestNewsData = cloneDeep(tableData)
  .slice(0, 14)
  .map((item, index) => {
    return Object.assign(item, {
      date: `${dayjs().subtract(index, "day").format("YYYY-MM-DD")} ${
        days[dayjs().subtract(index, "day").day()]
      }`
    });
  });

export { chartData, barChartData, progressData, tableData, latestNewsData };
