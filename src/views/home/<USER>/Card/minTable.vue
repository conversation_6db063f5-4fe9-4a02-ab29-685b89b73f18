<template>
  <div class="flex justify-center items-center px-2 mt-1">
    <div class="flex flex-col w-full h-full text-center text-[12px]">
      <!-- 上部分 -->
      <div class="flex justify-between items-center space-x-1">
        <span
          v-for="(it, index) in props.item"
          :key="index"
          class="w-full text-[#9ca3af] truncate overflow-hidden whitespace-nowrap"
        >
          <el-tooltip :content="it[props.name]" placement="top" effect="light">
            {{ it[props.name] }}
          </el-tooltip>
        </span>
      </div>
      <!-- 分割线 -->
      <div class="border-t border-gray-300 my-2 mx-1" />
      <!-- 下部分 -->
      <div class="flex justify-between items-center space-x-1">
        <span v-for="(it, index) in props.item" :key="index" class="w-full">{{
          it[props.total]
        }}</span>
      </div>
      <!-- 分割线 -->
      <div class="border-t border-gray-300 my-2 mx-1" />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: ""
  },
  total: {
    type: String,
    default: ""
  }
});
</script>

<style lang="scss" scoped></style>
