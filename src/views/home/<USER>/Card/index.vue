<script setup lang="tsx">
import {
  ref,
  reactive,
  onMounted,
  watch,
  nextTick
} from "vue";
import { useDark, randomGradient } from "./utils";
import ReCol from "@/components/ReCol";
import { ChartLine, ChartRound, ChartBar } from "../charts";
import { ReNormalCountTo } from "@/components/ReCountTo";
import Order from "@iconify-icons/ri/order-play-fill";
import minTable from "./minTable.vue";
const { isDark } = useDark();
import {
  getSaleOrderOnTimeRateAPI,
  getFactoryProdYieldRateAPI,
  getPlanAchievementRateAPI,
  getSalesOrderCountByPeriodAPI,
  getSevenDayExpiringSaleOrderAPI,
  getCategorySalesNumberAPI
} from "@/api/home/<USER>";
import { CirclePlusFilled, RemoveFilled } from "@element-plus/icons-vue";

// 类型枚举
const chartType = [
  {
    index: 0,
    name: "销售订单准交率",
    com: Order,
    color: "#41b6ff",
    req: getSaleOrderOnTimeRateAPI,
    num: {
      totalCount: "应交付订单数",
      onTimeCount: "应交付且准时交付订单数",
      countSaleOrder: "总销售订单数"
    }
  },
  {
    index: 1,
    name: "工厂生产良品率",
    com: Order,
    color: "#41b6ff",
    req: getFactoryProdYieldRateAPI,
    numTitle: "工厂生产良品数",
    denTitle: "工厂生产总数"
  },
  {
    index: 2,
    name: "计划达成率",
    com: Order,
    color: "#41b6ff",
    req: getPlanAchievementRateAPI,
    num: {
      countPlanDispatch: "应完派工计划数",
      countFinishDispatch: "按期完成派工计划数",
      countDispatch: "总派工计划数"
    }
  },
  {
    index: 3,
    name: "商品销售数量",
    type: "salesQuantity",
    com: Order,
    color: "#41b6ff",
    req: getCategorySalesNumberAPI
  },
  {
    index: 4,
    name: "销售订单数量指标",
    com: Order,
    type: "orderCount",
    color: "#41b6ff",
    req: getSalesOrderCountByPeriodAPI
  },
  {
    index: 5,
    name: "临期销售订单数量指标",
    com: Order,
    type: "expiredOrderCount",
    color: "#41b6ff",
    req: getSevenDayExpiringSaleOrderAPI
  }
];

const props = defineProps({
  date: {
    type: Array,
    default: () => []
  },
  index: {
    type: Number
  },
  type: {
    type: String,
    default: ""
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  isVisible: {
    type: Boolean,
    default: true
  }
});

// 日期
const date = ref({ startDate: props.date[0], endDate: props.date[1] });

// 监听获取日期
watch(
  () => props.date,
  val => {
    console.log("更新");

    // 解构赋值
    const [startDate, endDate] = val;
    // 更新 date 对象的属性
    date.value = { startDate, endDate }; // 更新 date 对象
    getCardItem(props.index);
  },
  { deep: true } // 深度监听
);

const cardItem = reactive<any>({});
const num = ref({ num: 0, result: 0, den: 0 });
const numArray = ref([]);
const getCardItem = async index => {
  // 清空 cardItem 中的所有属性
  Object.keys(cardItem).forEach(key => delete cardItem[key]);
  // 将新属性赋值给 cardItem
  Object.assign(cardItem, chartType[index]);
  // console.log(cardItem, "cardItem");

  // 判断是否为选项卡类型
  if (cardItem.type) {
    // 异步请求数据并更新
    const res = await cardItem.req(date.value);

    numArray.value = [];

    for (let i = 0; i < res.data.length; i += cardItem.index == 4 ? 4 : 3) {
      numArray.value.push(res.data.slice(i, i + (cardItem.index == 4 ? 4 : 3))); // 每三个元素合并为一个子数组
    }
    console.log(numArray.value, "len");
  } else {
    // 异步请求数据并更新
    const res = await cardItem.req(date.value);
    num.value = res.data;
    // console.log(cardItem.num.result, "res");
  }
};

// 选择分类
const selectChange = async val => {
  await getCardItem(val);
};

// 编辑排版-是否显示
const visible = ref(props.isVisible);
const emit = defineEmits(["changeVisible"]);
const handleHiddenCard = bool => {
  visible.value = bool;
  emit("changeVisible", visible.value);
};

onMounted(() => {
  nextTick(() => {
    getCardItem(props.index);
  });
});
</script>

<template>
  <re-col
    v-motion
    :value="8"
    :md="12"
    :sm="12"
    :xs="24"
    :initial="{
      opacity: 0,
      y: 100
    }"
    :enter="{
      opacity: 1,
      y: 0
    }"
  >
    <el-card class="line-card relative" shadow="never">
      <div class="flex justify-between">
        <el-select
          v-model="cardItem.name"
          style="width: 60%"
          @change="selectChange"
        >
          <el-option
            v-for="(item, index) in chartType"
            :key="index"
            :label="item.name"
            :value="index"
          />
        </el-select>
        <div
          class="w-8 h-8 flex justify-center items-center rounded-md"
          :style="{
            backgroundColor: isDark ? 'transparent' : cardItem.bgColor
          }"
        >
          <IconifyIconOffline
            :icon="cardItem.com"
            :color="cardItem.color"
            width="18"
          />
        </div>
      </div>
      <div class="flex justify-between items-center items-end mt-3 card-bot">
        <template v-if="cardItem.type">
          <el-carousel
            :arrow="numArray.length > 1 ? 'hover' : 'never'"
            indicator-position="none"
            :interval="0"
            style="width: 100%; height: 70px"
          >
            <el-carousel-item v-for="(item, index) in numArray" :key="index">
              <template v-if="cardItem.type == 'salesQuantity'">
                <minTable
                  :item="item"
                  name="pinbPrdType"
                  total="totalQuantity"
                />
              </template>
              <template v-else-if="cardItem.type == 'orderCount'">
                <minTable :item="item" name="period" total="total" />
              </template>

              <minTable v-else :item="item" name="date" total="total" />
            </el-carousel-item>
          </el-carousel>
        </template>
        <template v-else>
          <div class="ml-1 flex flex-col items-center w-1/2 cardText">
            <template v-if="cardItem.index == 0">
              <div class="flex items-center justify-start w-full">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.totalCount }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.totalCount || 0"
                />
              </div>
              <div class="flex justify-start w-full items-center">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.onTimeCount }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.onTimeCount || 0"
                />
              </div>
              <div class="flex items-center justify-start w-full">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.countSaleOrder }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.countSaleOrder || 0"
                />
              </div>
            </template>
            <template v-else-if="cardItem.index == 2">
              <div class="flex items-center justify-start w-full">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.countPlanDispatch }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.countPlanDispatch || 0"
                />
              </div>
              <div class="flex justify-start w-full items-center">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.countFinishDispatch }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.countFinishDispatch || 0"
                />
              </div>
              <div class="flex items-center justify-start w-full">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.num.countDispatch }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.countDispatch || 0"
                />
              </div>
            </template>
            <template v-else>
              <div class="flex items-center justify-start w-full">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.numTitle }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.num || 0"
                />
              </div>
              <div class="flex justify-start w-full items-center">
                <div
                  class="text-gray-400 mr-2 truncate"
                  style="font-size: 13px"
                >
                  {{ cardItem.denTitle }}:
                </div>
                <ReNormalCountTo
                  :duration="800"
                  :fontSize="'1em'"
                  :startVal="0"
                  :endVal="num.den || 0"
                />
              </div>
            </template>
          </div>
          <ChartRound
            class="!w-1/2 round"
            :text="num.result ? Math.floor(num.result * 10000) / 100 : 0.0"
          />
        </template>
      </div>
      <div
        v-if="!props.isVisible && isEditing"
        class="absolute top-0 right-0 bg-black opacity-50 w-full h-full"
      />
      <div v-if="isEditing" class="absolute top-2 right-2">
        <el-icon
          v-if="props.isVisible"
          style="color: #f56c6c"
          @click="handleHiddenCard(false)"
          ><remove-filled
        /></el-icon>
        <el-icon v-else style="color: #79bbff" @click="handleHiddenCard(true)"
          ><circle-plus-filled
        /></el-icon>
      </div>
    </el-card>
  </re-col>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  --el-card-border-color: none;

  /* 解决概率进度条宽度 */
  .el-progress--line {
    width: 85%;
  }

  /* 解决概率进度条字体大小 */
  .el-progress-bar__innerText {
    font-size: 15px;
  }

  /* 隐藏 el-scrollbar 滚动条 */
  .el-scrollbar__bar {
    display: none;
  }

  /* el-timeline 每一项上下、左右边距 */
  .el-timeline-item {
    margin: 0 6px;
  }

  .cardText {
    flex: 1;
  }
}

.main-content {
  margin: 20px 20px 0 !important;
}

.flex-end {
  justify-content: flex-end;
  margin-bottom: 10px;
}

.card-bot {
  min-height: 60px;
}
// table 的label样式
::v-deep .el-table .card-table-label {
  font-size: 12px !important;

  .cell {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.line-card {
  box-shadow: 0 0 0 1px rgb(0 0 0 / 3%);

  @media (width <= 1400px) {
    // width: 120%;
    .el-select {
      width: 70% !important;
    }

    .cardText {
      width: 100%;
    }

    .round {
      // width: 0;
      visibility: hidden;
    }
  }
}
</style>
