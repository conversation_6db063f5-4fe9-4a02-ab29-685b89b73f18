<script setup lang="tsx">
import { ref, onMounted, defineEmits, nextTick } from "vue";
import dayjs, { type Dayjs } from "dayjs";
const dateValue = ref([]);

onMounted(async () => {
  dateValue.value = [
    dayjs().subtract(7, "days").startOf("day").format("YYYY-MM-DD"),
    dayjs().endOf("day").format("YYYY-MM-DD")
  ];
  emit("updateDate", dateValue.value);
});

// emit
const emit = defineEmits(["updateDate"]);

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

/**
 * 日期选择器变化
 */
const dateChange = () => {
  // 使用 dayjs 对象进行日期排序，按日期升序或降序排序
  dateValue.value.sort((a, b) => {
    return dayjs(b).isBefore(dayjs(a)) ? 1 : -1; // 按日期降序排列
  });
  console.log(dateValue.value, "date");
  emit("updateDate", dateValue.value);
};
</script>

<template>
  <div class="date-picker-container">
    <el-date-picker
      v-model="dateValue[0]"
      :disabled-date="disabledDate"
      type="date"
      class="date-picker"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      placeholder="开始日期"
      @change="dateChange"
    />
    -
    <el-date-picker
      v-model="dateValue[1]"
      :disabled-date="disabledDate"
      type="date"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      class="date-picker"
      placeholder="结束日期"
      @change="dateChange"
    />
  </div>
</template>

<style lang="scss" scoped>
::v-deep .date-picker {
  width: 140px !important;
  max-width: 150px !important;
}
</style>
