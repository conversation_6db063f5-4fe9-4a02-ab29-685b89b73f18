<script setup lang="tsx">
import IconItem from "@/views/common-functions/components/IconItem/index.vue";
import { CirclePlusFilled, Delete, RemoveFilled } from "@element-plus/icons-vue";

const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  iconList: {
    type: Array,
    default: () => []
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["handleHiddenCommonFunctions"]);

const handleHiddenCommonFunctions = (item: any) => {
  item.isHidden = !item.isHidden;
  emit("handleHiddenCommonFunctions", props.iconList, props.title);
};
</script>

<template>
  <div class="common-functions-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ title }}常用功能</span>
          <el-divider style="margin: 0" />
        </div>
      </template>
      <el-row class="icon-row">
        <div
          v-for="(item, index) in props.iconList"
          :key="index"
          class="relative"
        >
          <IconItem
            v-if="!item.isHidden"
            :name="item.icon"
            :title="item.title"
            :color="item.color"
            :req="item.req"
            :systemType="item.systemType"
            class="card-item"
          />
          <el-icon
            v-if="isEditing && !item.isHidden"
            style="position: absolute; color: #f56c6c"
            class="right-8 top-1"
            @click="handleHiddenCommonFunctions(item)"
            ><remove-filled
          /></el-icon>
        </div>

        <div
          v-for="(item, index) in props.iconList"
          :key="index"
          class="relative"
        >
          <IconItem
            v-if="isEditing && item.isHidden"
            :name="item.icon"
            :title="item.title"
            :color="['#232526', '#e4e5e6']"
            :req="item.req"
            :systemType="item.systemType"
            class="card-item"
          />
          <el-icon
            v-if="isEditing && item.isHidden"
            style="position: absolute; color: #79bbff"
            class="right-8 top-1"
            @click="handleHiddenCommonFunctions(item)"
            ><circle-plus-filled
          /></el-icon>
        </div>
      </el-row>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.common-functions-container {
  width: 100%;
  box-shadow: 0 0 0 1px rgb(0 0 0 / 3%);
}
.card-item {
  margin-right: 20px;
  margin-bottom: 20px;
}
</style>
