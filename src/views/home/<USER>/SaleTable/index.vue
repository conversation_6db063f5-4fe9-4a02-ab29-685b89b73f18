<template>
  <div class="sale-table-container">
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      height="auto"
      :showTotal="false"
      :fetchData="fetchTableData"
      :headerCellKeys="[]"
      :tableCellEditAble="[]"
      treeNodeKey="billNo"
      :mappingStatus="homeMap"
      :treeConfig="{
        indent: 0,
        expandAll: true,
        showIcon: false,
        rowField: 'billNo',
        parentField: 'parentBillNo'
      }"
      :tableRowWarnStatus="tableRowWarnStatus"
      :updateQueryDate="updateQueryDate"
      :queryData="queryData"
      :initialFilters="tableFilters"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted, computed, nextTick } from "vue";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getSaleOrderKeys,
  getSaleOrder,
  exportSaleOrderAPI
} from "@/api/home/<USER>";

import { useRouter } from "vue-router";
// 导入home初始id
import { homeId } from "@/router/columnIdList";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { homeMap } from "../utils";

defineOptions({
  name: "SalesOrders"
});
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20 },
  query: [],
  order: []
});

const tableQuery = ref([]);
const tableRef = ref(null);
const selectRows = ref([]);

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleEdit,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(homeId, tableRef, queryData, tableQuery, selectRows);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSaleOrderKeys, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSaleOrder,
  {
    states: homeMap,
    addPercentSigns: ["deliveryProgress", "prodOrderProgress"]
  },
  true,
  {},
  true
);

// 排序条件
const tableRowWarnStatus = reactive([
  // {
  //   columnKey: "completionStatus",
  //   operator: "==",
  //   threshold: "未开始",
  //   className: "row-pending"
  // },
  // {
  //   columnKey: "completionStatus",
  //   operator: "==",
  //   threshold: "进行中",
  //   className: "row-pending"
  // },
  // 4为红色
  {
    columnKey: "colorType",
    operator: "==",
    threshold: 4,
    className: "row-red"
  },
  //3为黄色
  {
    columnKey: "colorType",
    operator: "==",
    threshold: 3,
    className: "row-processing"
  },
  //2为蓝色，其余为无色
  {
    columnKey: "colorType",
    operator: "==",
    threshold: 2,
    className: "row-blue"
  }
]);

// 获取路由对象
const router = useRouter();
const nodeStore = useNodeStore();
// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.error(row, "某行被点击");
  nodeStore.setQueryId(row.sytText1);
  nodeStore.setSaleOrderId(row.billNo);
  router.push({
    path: "/full-lifecycle/purchase-order",
    query: {
      billNo: row.sytText1, //订单号
      sellOrderNo: row.billNo //销售订单号
    }
  });
};

// 定义初始过滤条件
import { useRoute } from "vue-router";
const route = useRoute();
const tableFilters = reactive({});
nextTick(() => {
  // 获取路由参数中的id
  const routeId = route.query.billNo;

  if (routeId) {
    // 设置过滤条件
    tableFilters["billNo"] = routeId;
  }
});

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSaleOrderAPI, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);
</script>

<style lang="scss" scoped>
.sale-table-container {
  height: 100%;
  overflow: auto;
}
</style>
