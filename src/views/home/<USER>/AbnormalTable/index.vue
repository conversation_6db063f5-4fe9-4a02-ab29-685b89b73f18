<script setup lang="tsx">
import router from "@/router";
import { ArrowRight } from "@element-plus/icons-vue";
import { ref, onMounted, nextTick } from "vue";

const props = defineProps({
  column: {
    type: Array,
    default: () => []
  },
  tableData: {
    type: Array,
    default: () => []
  }
});

// 表格引用
const singleTableRef = ref(null);

// 列宽数据
const columnWidths = ref({});

// 保存列宽设置
const saveColumnWidth = (field, newWidth) => {
  columnWidths.value[field] = newWidth;
  // 可以在这里添加保存到本地存储或发送到服务器的逻辑
  localStorage.setItem(
    "abnormal-table-widths",
    JSON.stringify(columnWidths.value)
  );
};

// 从本地存储加载列宽设置
const loadColumnWidths = () => {
  try {
    const savedWidths = localStorage.getItem("abnormal-table-widths");
    if (savedWidths) {
      columnWidths.value = JSON.parse(savedWidths);
    }
  } catch (error) {
    console.error("加载列宽设置失败:", error);
  }
};

// 获取列宽度，优先使用保存的宽度，其次使用默认宽度
const getColumnWidth = (field, defaultWidth) => {
  return columnWidths.value[field] || defaultWidth;
};

// 点击行
const handleCurrentChange = (val: any) => {
  console.log(`当前行:`, val);
  // 跳转到异常情况一览页面并传递id参数，实现高亮定位
  if (val && val.id) {
    router.push({
      path: "/abnormal-situation-overview",
      query: {
        id: val.id,
        errMsg: val.errMsg
      }
    });
  }
};

// 跳转页面
const handleToMore = () => {
  router.push("/abnormal-situation-overview");
};

// 处理列宽变化
const handleHeaderDragend = (newWidth, oldWidth, column) => {
  saveColumnWidth(column.property, newWidth);
};

// 组件挂载时加载保存的列宽
onMounted(() => {
  loadColumnWidths();
});
</script>

<template>
  <div class="abnormal-table-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <div class="flex justify-between">
            <div>异常情况一览</div>
            <el-button link @click="handleToMore">
              查看更多<el-icon class="el-icon--right"><ArrowRight /></el-icon>
            </el-button>
          </div>
          <el-divider style="margin: 0" />
        </div>
      </template>
      <div class="card-body">
        <el-table
          ref="singleTableRef"
          :data="tableData"
          highlight-current-row
          style="width: 100%"
          border
          @current-change="handleCurrentChange"
          @header-dragend="handleHeaderDragend"
        >
          <el-table-column
            v-for="item in props.column"
            :key="item.field"
            :property="item.field"
            :label="item.description"
            :width="getColumnWidth(item.field, item.width)"
            header-align="center"
            align="center"
            show-overflow-tooltip
            resizable
          />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.abnormal-table-container {
  width: 100%;
  box-shadow: 0 0 0 1px rgb(0 0 0 / 3%);
}

.card-body {
  margin-top: -30px;
}

:deep(.el-table) {
  /* 确保表格内容不会被截断 */
  table-layout: auto;

  /* 自定义拖动手柄样式 */
  .el-table__column-resize-proxy {
    background-color: var(--el-color-primary);
  }

  /* 表头单元格悬停时显示拖动指针 */
  th.is-resizable:hover {
    cursor: col-resize;
  }
}
</style>
