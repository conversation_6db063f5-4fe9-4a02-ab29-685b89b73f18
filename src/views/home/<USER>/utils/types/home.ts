/* 首页--是否期初单据*/
enum isInit {
  name = "isInit",
  false = "否",
  true = "是"
}
/* 首页--是否手工关闭 */
enum manualClose {
  false = "否",
  true = "是",
  name = "manualClose"
}
/* 首页--完成状态*/
const completionStatus = {
  1: "未开始",
  2: "进行中",
  3: "已完成",
  name: "completionStatus"
};

/* 销售订单-生成受托加工材料清单 */
enum isUseOemBomPush {
  false = "否",
  true = "是",
  name = "isUseOemBomPush"
}

/* 销售订单-生成分销采购订单 */
enum isUseDrpSalePoPush {
  false = "否",
  true = "是",
  name = "isUseDrpSalePoPush"
}

/* 销售订单-生成直运出入库 */
enum isCreateStraightOutIn {
  false = "否",
  true = "是",
  name = "isCreateStraightOutIn"
}

/* 销售订单-来自移动 */
enum isMobile1 {
  false = "否",
  true = "是",
  name = "isMobile1"
}

export const homeMap = [
  isInit,
  manualClose,
  completionStatus,
  isUseOemBomPush,
  isUseDrpSalePoPush,
  isCreateStraightOutIn,
  isMobile1
];
