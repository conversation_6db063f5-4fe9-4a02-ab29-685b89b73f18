<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { useDark, useECharts } from "@pureadmin/utils";

const { isDark } = useDark();

const theme = computed(() => (isDark.value ? "dark" : "light"));

const chartRef = ref();
const { setOptions } = useECharts(chartRef, {
  theme,
  renderer: "svg"
});
const props = defineProps({
  text: {
    type: Number,
    default: 0
  }
});
setOptions({
  container: ".line-card",
  title: {
    text: `${props.text}%`,
    left: "47%",
    top: "30%",
    textAlign: "center",
    textStyle: {
      fontSize: "14",
      fontWeight: 600
    }
  },
  polar: {
    radius: ["100%", "90%"],
    center: ["50%", "50%"]
  },
  angleAxis: {
    max: 100,
    show: false
  },
  radiusAxis: {
    type: "category",
    show: true,
    axisLabel: {
      show: false
    },
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    }
  },
  series: [
    {
      type: "bar",
      roundCap: true,
      barWidth: 2,
      showBackground: true,
      backgroundStyle: {
        color: "#dfe7ef"
      },
      // 动态更新加载条
      data: [props.text],
      coordinateSystem: "polar",
      color: "#7846e5",
      itemStyle: {
        shadowBlur: 2,
        shadowColor: "#7846e5",
        shadowOffsetX: 0,
        shadowOffsetY: 0
      }
    }
  ]
});
// 监听 props.value 的变化
watch(
  () => props.text,
  newValue => {
    // 执行你的图表更新操作
    nextTick(() => {
      setOptions({
        container: ".line-card",
        title: {
          text: `${props.text}%`,
          left: "47%",
          top: "30%",
          textAlign: "center",
          textStyle: {
            fontSize: "14",
            fontWeight: 600
          }
        },
        polar: {
          radius: ["100%", "90%"],
          center: ["50%", "50%"]
        },
        angleAxis: {
          max: 100,
          show: false
        },
        radiusAxis: {
          type: "category",
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            type: "bar",
            roundCap: true,
            barWidth: 2,
            showBackground: true,
            backgroundStyle: {
              color: "#dfe7ef"
            },
            // 动态更新加载条
            data: [props.text],
            coordinateSystem: "polar",
            color: "#7846e5",
            itemStyle: {
              shadowBlur: 2,
              shadowColor: "#7846e5",
              shadowOffsetX: 0,
              shadowOffsetY: 0
            }
          }
        ]
      });
    });
  },
  { deep: true }
);
</script>

<template>
  <div ref="chartRef" style="width: 100%; height: 70px" />
</template>
