<script setup lang="ts">
import { reactive, ref, onMounted, toRaw } from "vue";
import { formUpload } from "@/api/user";
import { message } from "@/utils/message";
import { type UserInfo, getMine } from "@/api/user";
import type { FormInstance, FormRules } from "element-plus";
import ReCropperPreview from "@/components/ReCropperPreview";
import { createFormData, deviceDetection } from "@pureadmin/utils";
import uploadLine from "@iconify-icons/ri/upload-line";

import { updateUserList } from "@/api/system/user";
import { type DataInfo, setToken, removeToken, setUserKey } from "@/utils/auth";
// 引入用户store
import { useUserStoreHook } from "@/store/modules/user";

defineOptions({
  name: "Profile"
});
const baseUrl = import.meta.env.VITE_BASE_URL;
const imgSrc = ref("");
const cropperBlob = ref();
const cropRef = ref();
const uploadRef = ref();
const isShow = ref(false);
const userInfoFormRef = ref<FormInstance>();

const useStore = useUserStoreHook();
const userInfos = ref({
  name: useStore.name || "",
  username: useStore.username || "",
  phone: useStore.phone || "",
  sex: useStore.sex || "",
  avatar: useStore.avatar || "",
  deptName: useStore?.deptName || ""
});

const sexOptions = [
  {
    value: 0,
    label: "男"
  },
  {
    value: 1,
    label: "女"
  }
];

const rules = reactive<FormRules<UserInfo>>({
  name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  sex: [{ required: true, message: "请选择性别", trigger: "change" }]
});

const onChange = uploadFile => {
  const reader = new FileReader();
  reader.onload = e => {
    imgSrc.value = e.target.result as string;
    isShow.value = true;
  };
  reader.readAsDataURL(uploadFile.raw);
};

const handleClose = () => {
  cropRef.value.hidePopover();
  uploadRef.value.clearFiles();
  isShow.value = false;
};

const onCropper = ({ blob }) => (cropperBlob.value = blob);

/** 图片处理成文件上传修改 */
const handleSubmitImage = () => {
  const formData = createFormData(
    {
      avatarFile: new File([cropperBlob.value], "avatar.png")
    },
    {
      fileKey: "file"
    }
  );
  formUpload(formData)
    .then(res => {
      if (res.code === 200) {
        message("更新头像成功", { type: "success" });
        useStore.avatar = res.data + "?time=" + new Date().getTime();
        userInfos.value.avatar = res.data + "?time=" + new Date().getTime();
        handleClose();
      } else {
        message("更新头像失败");
      }
    })
    .catch(error => {
      message(`提交异常 ${error}`, { type: "error" });
    });
};

// 更新信息
const onSubmit = async (formEl: FormInstance) => {
  if (!formEl) return;

  // 表单验证
  await formEl.validate(async valid => {
    if (valid) {
      // 调用更新接口
      await updateUserList(userInfos.value).then(res => {
        if (res.code === 200) {
          setUserKey(userInfos.value);
          message("用户信息更新成功", { type: "success" });
        } else {
          message("用户信息更新失败", { type: "error" });
        }
      });
    }
  });
};

const treeDept = ref([]);
// onMounted(async () => {
//   try {
//     // 获取部门列表数据
//     const { data } = await getDeptList({ pageSize: 999, pageNum: 1 });
//     treeDept.value = handleTree(data.resultList);
//   } catch (error) {
//     console.error("获取部门数据失败:", error);
//   }
// });
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">个人信息</h3>
    <el-form
      ref="userInfoFormRef"
      label-position="top"
      :rules="rules"
      :model="userInfos"
      hide-required-asterisk
    >
      <el-form-item label="头像">
        <el-avatar :size="80" :src="baseUrl + userInfos.avatar" />
        <el-upload
          ref="uploadRef"
          accept="image/*"
          action="#"
          :limit="1"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="onChange"
        >
          <el-button plain class="ml-4">
            <IconifyIconOffline :icon="uploadLine" />
            <span class="ml-2">更新头像</span>
          </el-button>
        </el-upload>
      </el-form-item>

      <el-form-item label="用户姓名" prop="name">
        <el-input v-model="userInfos.name" placeholder="请输入用户姓名" />
      </el-form-item>
      <el-form-item label="用户名称" prop="username">
        <el-input v-model="userInfos.username" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="userInfos.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="用户性别">
        <el-select
          v-model="userInfos.sex"
          placeholder="请选择用户性别"
          class="w-full"
          clearable
        >
          <el-option
            v-for="(item, index) in sexOptions"
            :key="index"
            :label="item.label"
            :value="item.label"
          />
        </el-select>
      </el-form-item>

      <el-button type="primary" @click="onSubmit(userInfoFormRef)">
        更新信息
      </el-button>
    </el-form>
    <el-dialog
      v-model="isShow"
      width="40%"
      title="编辑头像"
      destroy-on-close
      draggable
      :closeOnClickModal="false"
      :before-close="handleClose"
      :fullscreen="deviceDetection()"
    >
      <ReCropperPreview ref="cropRef" :imgSrc="imgSrc" @cropper="onCropper" />
      <template #footer>
        <div class="dialog-footer">
          <el-button bg text @click="handleClose">取消</el-button>
          <el-button bg text type="primary" @click="handleSubmitImage">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
