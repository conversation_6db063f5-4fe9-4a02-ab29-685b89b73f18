<script setup lang="ts">
import { ref } from "vue";
import { message } from "@/utils/message";
import { deviceDetection } from "@pureadmin/utils";
import { updatePassword } from "@/api/system/user";
defineOptions({
  name: "AccountManagement"
});

const list = ref([
  {
    title: "账户密码",
    illustrate: "当前密码强度：强",
    button: "修改"
  }
]);
// 弹窗状态
const dialogVisible = ref(false);

// 表单数据绑定
const form = ref({
  oldPassword: "", // 旧密码
  newPassword: "", // 新密码
  confirmPassword: "" // 确认新密码
});

// 表单校验规则
const rules = ref<Record<string, object>>({
  oldPassword: [
    { required: true, message: "旧密码不能为空", trigger: "blur" }
    // 可以根据安全需求添加旧密码复杂度校验
  ],
  newPassword: [
    { required: true, message: "新密码不能为空", trigger: "blur" },
    // { min: 6, max: 16, message: "密码长度必须在6到18位之间", trigger: "blur" },
    { validator: validatePassword, trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请确认新密码", trigger: "blur" },
    { validator: validateConfirmPassword, trigger: "blur" }
  ]
});

// 密码强度校验
function validatePassword(rule, value, callback) {
  // const regex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{6,18}$/;
  // if (!regex.test(value)) {
  //   callback(new Error("密码必须包含字母、数字和符号，长度在6到18位之间"));
  // } else {
  //   callback();
  // }

  // 检查密码长度是否在8到16位之间
  if (value.length < 8 || value.length > 16) {
    callback(new Error("密码长度8-16位"));
  } else {
    // 检查密码是否只包含允许的字符
    const allowedCharsRegex = /^[0-9a-zA-Z!@#$%^&*()_+]+$/;
    if (!allowedCharsRegex.test(value)) {
      callback(new Error("密码格式错误"));
    } else {
      callback();
    }
  }
}

// 确认密码校验
function validateConfirmPassword(rule, value, callback) {
  if (value !== form.value.newPassword) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
}

// 点击事件处理
function onClick(item) {
  if (item.title === "账户密码") {
    dialogVisible.value = true;
  }
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false;
  // 重置表单
  form.value = {
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  };
}
const passwordForm = ref(null);
// 提交密码修改
function submitPasswordChange() {
  passwordForm.value.validate(async valid => {
    if (valid) {
      // 模拟密码修改成功
      // 确保 updatePassword 函数返回一个 Promise，如果不是，需要修改该函数使其返回 Promise
      const res = await updatePassword(form.value);

      if (res.code === 200) {
        message("密码修改成功", { type: "success" });
        closeDialog();
      } else {
        message(res.msg, { type: "error" });
      }
    }
  });
}
</script>

<template>
  <div
    :class="[
      'min-w-[180px]',
      deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]"
  >
    <h3 class="my-8">账户管理</h3>
    <div v-for="(item, index) in list" :key="index">
      <div class="flex items-center">
        <div class="flex-1">
          <p>{{ item.title }}</p>
          <el-text class="mx-1" type="info">{{ item.illustrate }}</el-text>
        </div>
        <el-button type="primary" @click="onClick(item)">
          {{ item.button }}
        </el-button>
      </div>
      <el-divider />
    </div>

    <!-- 密码修改弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="修改密码"
      width="20%"
      draggable
      style="min-width: 300px"
    >
      <el-form ref="passwordForm" :model="form" :rules="rules">
        <el-form-item label="旧密码" prop="oldPassword">
          <!-- 添加 show-password 属性以显示密码切换按钮 -->
          <el-input v-model="form.oldPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="form.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitPasswordChange"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.el-divider--horizontal {
  border-top: 0.1px var(--el-border-color) var(--el-border-style);
}
</style>
