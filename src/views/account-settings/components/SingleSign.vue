<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance } from "element-plus";
import { TabsPaneContext } from "element-plus";
import { bindErpAccount, bindMesAccount } from "@/api/system/user";
import { message } from "@/utils/message";
import { storageLocal } from "@pureadmin/utils";
import { onMounted } from "vue";
import { getUserByUserName } from "@/api/system/user";

onMounted(async () => {
  await fetchUserInfo();
});

defineOptions({
  name: "SingleSign"
});

const userInfo = ref({
  erpToken: "",
  erpBindUsername: "",
  mesToken: "",
  mesBindUsername: ""
});

const activeName = ref("first");

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

const ruleFormRef = ref<FormInstance>();

const mesRuleFormRef = ref<FormInstance>();

const fetchUserInfo = async () => {
  try {
    const res = await getUserByUserName(
      storageLocal().getItem("user-info")?.username
    );
    if (res.code === 200) {
      userInfo.value = res.data;
    }
  } catch (error) {
    message("用户信息更新失败", { type: "error" });
  }
};

// 租户名称验证规则
const validateTenancyName = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入租户名称")); // 提示输入密码
  } else {
    callback();
  }
};

// 密码验证规则
const validatePass = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入密码")); // 提示输入密码
  } else {
    callback();
  }
};

// 账户验证规则
const validateAccount = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请输入账号")); // 如果为空，提示输入账户
  } else {
    callback();
  }
};

const ruleForm = reactive({
  username: "", // 账户字段
  password: "" // 密码字段
});

const mesRuleForm = reactive({
  tenancyName: "", // 租户名称
  username: "", // 账户字段
  password: "" // 密码字段
});

const rules = reactive({
  username: [{ validator: validateAccount, trigger: "blur" }],
  password: [{ validator: validatePass, trigger: "blur" }]
});

const mesRules = reactive({
  tenancyName: [{ validator: validateTenancyName, trigger: "blur" }],
  username: [{ validator: validateAccount, trigger: "blur" }],
  password: [{ validator: validatePass, trigger: "blur" }]
});

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const res = await bindErpAccount(ruleForm);
      // console.log(res, "res");
      message(res.msg, {
        type: res.code === 200 ? "success" : "error"
      });
      if (res.code === 200) {
        await fetchUserInfo();
      }

      console.log("Login successful!"); // 模拟登录成功
    } else {
      console.log("Login failed!"); // 模拟登录失败
    }
  });
};

const submitMesForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const res = await bindMesAccount(mesRuleForm);
      // console.log(res, "res");
      message(res.msg, {
        type: res.code === 200 ? "success" : "error"
      });
      if (res.code === 200) {
        await fetchUserInfo();
      }

      console.log("Login successful!"); // 模拟登录成功
    } else {
      console.log("Login failed!"); // 模拟登录失败
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields(); // 重置表单
};
</script>

<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <div class="mb-2">
      ERP账户绑定状态:
      <el-button
        :type="!userInfo?.erpToken ? 'danger' : 'success'"
        size="small"
        plain
      >
        {{ !userInfo?.erpToken ? "未绑定" : "已绑定" }}
      </el-button>
      || MES账户绑定状态:
      <el-button
        :type="!userInfo?.mesToken ? 'danger' : 'success'"
        size="small"
        plain
      >
        {{ !userInfo?.mesToken ? "未绑定" : "已绑定" }}
      </el-button>
    </div>
    <div class="mb-2">
      ERP当前绑定用户: {{ userInfo.erpBindUsername }} || MES当前绑定用户:
      {{ userInfo.mesBindUsername }}
    </div>
    <el-tab-pane label="ERP" name="first">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        status-icon
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
      >
        <el-form-item label="账号" prop="username">
          <el-input v-model="ruleForm.username" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="ruleForm.password"
            type="password"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            绑定
          </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="MES" name="second">
      <el-form
        ref="mesRuleFormRef"
        style="max-width: 600px"
        :model="mesRuleForm"
        status-icon
        :rules="mesRules"
        label-width="auto"
        class="demo-ruleForm"
      >
        <el-form-item label="租户名称" prop="tenancyName">
          <el-input v-model="mesRuleForm.tenancyName" autocomplete="off" />
        </el-form-item>
        <el-form-item label="账号" prop="username">
          <el-input v-model="mesRuleForm.username" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="mesRuleForm.password"
            type="password"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitMesForm(mesRuleFormRef)">
            绑定
          </el-button>
          <el-button @click="resetForm(mesRuleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  font-size: 32px;
  font-weight: 600;
  color: #6b778c;
}
</style>
