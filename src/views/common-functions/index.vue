<script setup lang="ts">
defineOptions({
  name: "common-functions"
});
import IconCard from "./components/IconCard/index.vue";
import file from "@iconify-icons/ri/file-list-2-fill";
import file1 from "@iconify-icons/ri/file-list-3-fill";
import file2 from "@iconify-icons/ri/folder-chart-fill";
import file3 from "@iconify-icons/ri/file-list-fill";
import file4 from "@iconify-icons/ri/file-copy-2-fill";
import file5 from "@iconify-icons/ri/file-text-fill";
import file6 from "@iconify-icons/ri/file-upload-fill";
import file7 from "@iconify-icons/ri/layout-top-fill";
import file8 from "@iconify-icons/ri/layout-top-2-fill";
import file9 from "@iconify-icons/ri/archive-fill";
import file10 from "@iconify-icons/ri/article-fill";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useI18n } from "vue-i18n";
import { useButtonPermission } from "@/utils/hooks/PermissionHooks";
import { useRoute } from "vue-router";
const route = useRoute();

const { getStringBeforeColon, btnRoles } = useButtonPermission(route);
const { t } = useI18n(); // 解构出t方法

const icon = [
  file,
  file1,
  file2,
  file3,
  file4,
  file5,
  file6,
  file7,
  file8,
  file9,
  file10
];

const iconList = btnRoles
  .filter(item => item.title.startsWith("ERP"))
  .map((item, index) => {
    return {
      title: item.title.substring(3),
      icon: icon[index],
      color: ["#ff7f32", "#ffb84d"],
      req: getStringBeforeColon(item.auths),
      systemType: "erp"
    };
  });

const iconList2 = btnRoles
  .filter(item => item.title.startsWith("MES"))
  .map((item, index) => {
    return {
      title: item.title.substring(3),
      icon: icon[index],
      color: ["#ff7f32", "#ffb84d"],
      req: getStringBeforeColon(item.auths),
      systemType: "mes"
    };
  });
</script>

<template>
  <div>
    <IconCard
      :title="t('common.pureCommonCardTitleERP')"
      :iconList="iconList"
    />
    <IconCard
      :title="t('common.pureCommonCardTitleMES')"
      :iconList="iconList2"
    />
  </div>
</template>

<style lang="scss" scoped></style>
