<script setup lang="ts">
import IconItem from "../IconItem/index.vue";

import { useButtonPermission } from "@/utils/hooks/PermissionHooks";
import { useRoute } from "vue-router";
const route = useRoute();
const { hasPermission } = useButtonPermission(route);

const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  iconList: {
    type: Array,
    default: () => []
  }
});

// const btnList = route.meta.

function parseAuths(auths) {
  return auths.split(":");
}
</script>

<template>
  <div>
    <el-card class="mb-10">
      <template #header>
        <div class="card-header">
          <span class="font-bold">{{ title }}</span>
        </div>
      </template>
      <el-row class="icon-row">
        <IconItem
          v-for="(item, index) in props.iconList"
          :key="index"
          :name="item.icon"
          :title="item.title"
          :color="item.color"
          :req="item.req"
          :systemType="item.systemType"
        />
      </el-row>
      <!-- <el-button v-if="hasPermission('add')" @click="add('add')"
        >新增</el-button
      >
      <el-button v-if="hasPermission('edit:clas')" @click="add('add')"
        >修改</el-button
      > -->
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
// 去除Card下划线
::v-deep .el-card__header {
  border-bottom: none !important;
}

.icon-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: start;
  margin-left: 2%;
}
</style>
