<script setup lang="tsx">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { getCommonFunctionsAPI } from "@/api/common-functions";
const { t } = useI18n(); // 解构出t方法

const props = defineProps({
  name: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: () => ""
  },
  color: {
    type: Array,
    default: () => []
  },
  req: {
    type: String,
    default: () => ""
  },
  systemType: {
    type: String,
    default: () => ""
  },
  formid: {
    type: String,
    default: () => ""
  }
});

// 渲染传入的color
const gradientStyle = computed(() => {
  return {
    background: `linear-gradient(to bottom right, ${props.color[0]}, ${props.color[1]}`
  };
});

// 图标点击
const iconClick = async () => {
  const res = await getCommonFunctionsAPI(props.req, props.systemType);
  if (res.code === 200) {
    window.open(res.data, "_blank");
  }
  // console.log(`点击了${t(`${props.title}`)}图标,接口为${props.req}`);
};
</script>

<template>
  <div class="icon-item" @click="iconClick">
    <div class="icon" :style="gradientStyle">
      <IconifyIconOffline :icon="name" width="36" style="color: #fff" />
    </div>
    <!-- 添加样式以实现单行显示和超长省略 -->
    <span class="single-line-ellipsis text-center">{{ t(`${title}`) }}</span>
  </div>
</template>

<style lang="scss" scoped>


@media screen and (width <= 768px) {
  .icon-item {
    width: 50px;
    height: 65px;

    span {
      width: 60px;
      overflow: hidden;
      font-size: 12px;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .icon {
      /* Adjust the icon's size in the smaller screen */
      padding: 3px; /* Smaller padding */
      font-size: 20px; /* If the icon uses font-size for size */
    }
  }
}

.icon-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 100px;
  font-size: 14px;
  background-color: #fff;
  border-radius: 10px;

  .icon {
    padding: 5px;
    background: linear-gradient(to bottom right, #ff7f32, #ffb84d);
    border-radius: 10px;
  }

  &:active {
    box-shadow: 0 0 10px rgb(0 0 0 / 20%); // 按下时添加阴影效果
    transform: scale(0.9); // 点击时缩小图标
  }
}

// 添加新样式类
.single-line-ellipsis {
  width: 100%; // 可以根据需要调整宽度
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
