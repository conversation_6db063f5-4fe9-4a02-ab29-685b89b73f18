// 引入 js-base64 库
import { Base64 } from "js-base64";

/**
 * @function base64加密
 * @param {string} str 要加密的字符串
 * @returns {string} 加密后的字符串
 */
const base64Encode = (str: string) => {
  if (str) {
    return Base64.encode(str);
  }
};

/**
 * @function base64解密
 * @param {string} str 要解密的字符串
 * @returns {string} 解密后的字符串
 */
const base64Decode = (str: string) => {
  if (str) {
    return Base64.decode(str);
  }
};

/** @function 存Cookie方法
 * @param {string} name cookie名称
 * @param {string} value cookie值
 * @param {number} days cookie过期时间，默认为7天
 * @param {string} path cookie路径，默认为"/"
 * @param {string} domain cookie域名，默认为当前域
 * @param {boolean} secure 是否仅在HTTPS下发送，默认为true
 *  @param {string} sameSite SameSite属性，默认为"Strict"
 */
const setSecureCookie = (
  name,
  value,
  days = 7,
  path = "/",
  domain = "",
  secure = true,
  sameSite = "Strict"
) => {
  // 计算过期时间
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000); // 默认为7天

  // 编码Cookie值，防止特殊字符导致的问题
  const encodedValue = encodeURIComponent(value);

  // 构建Cookie字符串
  let cookie = `${name}=${encodedValue}; expires=${expires.toUTCString()}; path=${path}`;

  // 设置域名，默认为当前域
  if (domain) {
    cookie += `; domain=${domain}`;
  }

  // 设置安全性选项
  if (secure) {
    cookie += "; Secure"; // 仅在 HTTPS 下发送
  }

  // 设置 HttpOnly 防止 JavaScript 访问 Cookie
  // cookie += "; HttpOnly";

  // 设置 SameSite 防止 CSRF 攻击
  cookie += `; SameSite=${sameSite}`;

  // 设置 Cookie
  document.cookie = cookie;
};

/** @function 获取Cookie并判断是否过期
 * @param {string} name cookie名称
 * @returns {string} cookie值 || 过期返回null
 */
const getCookie = name => {
  // 获取所有cookie
  const cookies = document.cookie;

  // 构建正则表达式查找特定cookie
  const regex = new RegExp("(^| )" + encodeURIComponent(name) + "=([^;]+)");

  // 匹配并返回cookie值
  const match = cookies.match(regex);

  // 如果找到，返回解码后的cookie值
  return match ? decodeURIComponent(match[2]) : null;
};

export { setSecureCookie, getCookie, base64Encode, base64Decode };
