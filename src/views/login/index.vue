<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref, reactive, toRaw, onMounted, onBeforeUnmount } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { ReImageVerify } from "@/components/ReImageVerify";
import { getCaptcha, getMenuRoutes } from "@/api/user";
import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import { ArrowDown } from "@element-plus/icons-vue";
/** 导入解码方法 */
import {
  setSecureCookie,
  getCookie,
  base64Encode,
  base64Decode
} from "./utils";
import { nextTick } from "process";

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const ruleFormRef = ref<FormInstance>();
const baseURL = import.meta.env.VITE_BASE_URL;
const { initStorage } = useLayout();
initStorage();
const imgCodeURL = ref(null);
const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  username: "",
  password: "",
  code: "",
  rememberMe: false
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername(ruleForm)
        .then(async res => {
          if (res?.code === 200) {
            // 获取后端路由;
            await initRouter();
            if (ruleForm.rememberMe) {
              /** 存入账户，加密密码后存入cookie */
              // setSecureCookie("password", base64Encode(ruleForm.password));
              // setSecureCookie("username", ruleForm.username);
            } else {
              /** 清除cookie */
              setSecureCookie("password", "");
              setSecureCookie("username", "");
            }
            router.push("/home").then(() => {
              message("登录成功", { type: "success" });
            });
          } else {
            refreshImgCode();
            // message(res?.msg || "登录失败", { type: "error" });
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    onLogin(ruleFormRef.value);
  }
}

onMounted(() => {
  /** 查询是否有存入的cookie */
  const password = getCookie("password");
  if (password) {
    ruleForm.username = getCookie("username");
    ruleForm.password = base64Decode(password);
  }
  window.document.addEventListener("keypress", onkeypress);
  refreshImgCode();
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});

const setCodeUrl = () => {
  refreshImgCode();
};

/** 图形验证码刷新 */
const refreshImgCode = () => {
  getCaptcha().then(res => {
    imgCodeURL.value = URL.createObjectURL(res);
    ruleForm.code = "";
  });
};
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <!-- <avatar class="avatar" /> -->
          <img src="/logo.png" alt="" srcset="" style="width: 70px" />
          <Motion>
            <h2 class="outline-none">{{ title }}</h2>
          </Motion>
          <!-- :rules="loginRules" -->
          <el-form ref="ruleFormRef" :model="ruleForm" size="large">
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  type="text"
                  placeholder="账号"
                  autocomplete="username"
                  :prefix-icon="useRenderIcon(User)"
                >
                  <template #suffix>
                    <!-- 下拉按钮 -->
                    <el-dropdown v-if="false" @command="handleDropdownCommand">
                      <el-icon class="el-icon--right">
                        <arrow-down />
                      </el-icon>
                      <template v-slot:dropdown>
                        <el-dropdown-menu class="pass-dropdown">
                          <el-dropdown-item command="option1"
                            >选项 1</el-dropdown-item
                          >
                          <el-dropdown-item command="option2"
                            >选项 2</el-dropdown-item
                          >
                          <el-dropdown-item command="option3"
                            >选项 3</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-input>
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入密码',
                    trigger: 'blur'
                  },
                  {
                    validator: (rule, value, callback) => {
                      // 检查密码长度是否在8到16位之间
                      if (value.length < 8 || value.length > 16) {
                        callback(new Error('密码长度8-16位'));
                      } else {
                        // 检查密码是否只包含允许的字符
                        const allowedCharsRegex = /^[0-9a-zA-Z!@#$%^&*()_+]+$/;
                        if (!allowedCharsRegex.test(value)) {
                          callback(new Error('密码格式错误'));
                        } else {
                          callback();
                        }
                      }
                    },
                    trigger: 'blur'
                  }
                ]"
                prop="password"
              >
                <el-input
                  v-model="ruleForm.password"
                  :rules="[
                    {
                      required: true,
                      message: '请输入密码',
                      trigger: 'blur'
                    }
                  ]"
                  clearable
                  show-password
                  type="password"
                  placeholder="密码"
                  autocomplete="password"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="code">
                <el-input
                  v-model="ruleForm.code"
                  clearable
                  placeholder="请输入验证码"
                  :prefix-icon="useRenderIcon('ri:shield-keyhole-line')"
                >
                  <template v-slot:append>
                    <!-- <ReImageVerify v-model:code="imgCodeURL" /> -->
                    <img
                      :src="imgCodeURL"
                      class="code-img"
                      @click="setCodeUrl"
                    />
                  </template>
                </el-input>
              </el-form-item>
            </Motion>
            <Motion :delay="150">
              <el-form-item prop="rememberMe">
                <el-checkbox v-model="ruleForm.rememberMe">记住我</el-checkbox>
              </el-form-item>
            </Motion>
            <Motion :delay="250">
              <el-button
                class="w-full mt-4"
                size="default"
                type="primary"
                :loading="loading"
                @click="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
  background-color: transparent;
  box-shadow: none;
}

.code-img {
  // width: 120px;
  height: 40px;
  margin-left: 10px;
}
</style>
