<template>
  <div>
    <!--    <el-row class="flex justify-end w-full">-->
    <!--      <DateTimePicker :section="30" @updateDate="updateDate" />-->
    <!--    </el-row>-->
    <ReTable
      ref="tableRef"
      :mappingStatus="warehousePreparationDetail"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      height="auto"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { materialDeliveriesFluctuate } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

import {
  getMaterialDeliveriesFluctuateAPI,
  getMaterialDeliveriesFluctuateFieldAPI,
  exportMaterialDeliveriesFluctuateAPI,
  getMaterialDeliveriesFluctuateAPINoDate
} from "@/api/statistical-analysis/purchase/materialDeliveriesFluctuate";

/** 表格状态数据 */
import { warehousePreparationDetail } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import dayjs from "dayjs";

// 日期
const date = ref([]);
date.value = [
  dayjs(1970).format("YYYY-MM-DD"),
  dayjs().endOf("day").format("YYYY-MM-DD")
];
const updateDate = time => {
  date.value = time;
  // console.log(date.value, "time");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};
defineOptions({
  name: "MaterialDeliveriesFluctuate"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: []
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  materialDeliveriesFluctuate,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getMaterialDeliveriesFluctuateFieldAPI, columns);

const sumList = ref(["totalPurchaseQty", "deliveryFluctuationDays"]);

const getTotal = () => {
  return sumList.value;
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getMaterialDeliveriesFluctuateAPINoDate,
  {
    states: []
  },
  false
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportMaterialDeliveriesFluctuateAPI, {
        date: date
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {};
</script>
