<template>
  <div>
    <el-row class="flex justify-end w-full">
      <DateTimePicker :section="30" @updateDate="updateDate" />
    </el-row>
    <ReTable
      ref="tableRef"
      :mappingStatus="warehousePreparationDetail"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :showTotal="false"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      height="auto"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { vendorMaterialOnTimeRateId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

import {
  getPurchaseDetailsOnTimeRateAPI,
  getPurchaseDetailsOnTimeRateFieldAPI
} from "@/api/statistical-analysis/purchase/purchase-details-on-time-rate";

/** 表格状态数据 */
import { warehousePreparationDetail } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useRouter } from "vue-router";
import { useNodeStore } from "@/store/modules/fullLifecycle";

// 日期
const date = ref([]);
const updateDate = time => {
  date.value = time;
  // console.log(date.value, "time");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};
defineOptions({
  name: "purchaseDetailsOnTimeRate"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const nodeStore = useNodeStore();
const router = useRouter();
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "onTimeDeliveryRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  vendorMaterialOnTimeRateId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getPurchaseDetailsOnTimeRateFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPurchaseDetailsOnTimeRateAPI,
  {
    states: []
  },
  false,
  { date: date }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel();
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log("row----", row);
  console.log("row----", row);

  console.log(row, column, cell, "某行被点击");
  if (column.property === "purchaseOrderCode") {
    nodeStore.setSonPurchaseOrderDetailFinalMaterialSourceNo(
      row.purchaseOrderCode
    );
    console.log("122121212");
    router.push({
      path: "/full-lifecycle/preparation/purchase-order-detail"
    });
  }
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 跳转到《某个生产任务单号的车间生产明细数据》
</script>
