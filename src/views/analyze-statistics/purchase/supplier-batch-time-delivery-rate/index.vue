<template>
  <div>
    <!--    <el-row class="flex justify-end w-full">-->
    <!--      <DateTimePicker :section="30" @updateDate="updateDate" />-->
    <!--    </el-row>-->
    <ReTable
      ref="tableRef"
      :mappingStatus="null"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { vendorBatchOnTimeRateId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

import {
  getVendorMaterialOnTimeRateAPI,
  getOnTimeRateFieldsByVendorAndMaterialAPI
} from "@/api/statistical-analysis/purchase/supplierDelivery";

/** 表格状态数据 */
import {
  getBatchOnTimeRateFieldAPI,
  getBatchOnTimeRateAPI,
  exportBatchOnTimeAPI,
  getBatchOnTimeRateAPINoDate
} from "@/api/statistical-analysis/purchase/vendorBatchOnTimeRate";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import dayjs from "dayjs";

// 日期
const date = ref([]);
date.value = [
  dayjs(1970).format("YYYY-MM-DD"),
  dayjs().endOf("day").format("YYYY-MM-DD")
];
const updateDate = time => {
  date.value = time;
  // console.log(date.value, "time");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};

defineOptions({
  name: "supplierBatchTimeDeliveryRate"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [
    {
      disabled: true,
      name: "deliveryDate",
      condition: "ge",
      query: dayjs().subtract(30, "days").startOf("day").format("YYYY-MM-DD"),
      logic: "and"
    }
  ],
  order: [
    {
      name: "purchaseDate",
      sort: "desc"
    },
    {
      name: "purchaseOrderCode",
      sort: "asc"
    },
    {
      name: "purchaseDetailLineNo",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  vendorBatchOnTimeRateId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getBatchOnTimeRateFieldAPI, columns);

const sumList = ref([
  "purchaseQuantity",
  "onTimeQualifiedQty",
  "productionLossRate",
  "stockSendQuantity",
  "supplementsNumber",
  "stockQuantity",
  "unissuedQuantity",
  "createdPlannedIssuanceQuantity",
  "uncreatedPlannedIssuanceQuantity",
  "createdPickingOrderQuantity",
  "uncreatedPickingOrderQuantity",
  "scrapNumbers"
]);

const getTotal = () => {
  return sumList.value;
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getBatchOnTimeRateAPINoDate,
  {
    states: [],
    addPercentSigns: ["onTimeDeliveryRate"]
  },
  false
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportBatchOnTimeAPI, {
        date: date
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {};
</script>
