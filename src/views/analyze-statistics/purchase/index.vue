<template>
  <div>
    <el-row class="flex justify-end w-full">
      <DateTimePicker :section="30" @updateDate="updateDate" />
    </el-row>
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      height="auto"
      :showTotal="false"
      :fetchData="fetchTableData"
      :headerCellKeys="[]"
      :tableCellEditAble="[]"
      treeNodeKey="billNo"
      :mappingStatus="homeMap"
      :treeConfig="threeConfig"
      :tableRowWarnStatus="tableRowWarnStatus"
      :updateQueryDate="updateQueryDate"
      :queryData="queryData"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, reactive } from "vue";
import ReTable from "@/components/ReTable/Table.vue";

import { useRouter } from "vue-router";
// 导入home初始id
import { homeId } from "@/router/columnIdList";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法

import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

// 导入获取billNo相关hook
import { useFetchDBTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { homeMap } from "@/views/home/<USER>/utils";
import {
  getPassMaterialIncomingAPI,
  getQualifiedMaterialsSupplierAPI,
  getQualifiedMaterialsSupplierFieldsAPI,
  getPassMaterialIncomingFieldsAPI
} from "@/api/statistical-analysis/index";

defineOptions({
  name: "Purchase"
});
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20 },
  query: [],
  order: []
});

const tableQuery = ref([]);
const tableRef = ref(null);
const selectRows = ref([]);
const threeConfig = ref({
  indent: 0,
  showIcon: true,
  rowField: "materialCode",
  parentField: "parentCode"
});
const isExpand = ref(false);

// 日期
const date = ref([]);
const updateDate = time => {
  date.value = time;
  // console.log(date.value, "time");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedDBColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleEdit,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns,
  hiddenColumns
} = useFetchDBTableData(
  homeId,
  tableRef,
  queryData,
  tableQuery,
  selectRows,
  date
);

//获取销售订单表头字段排序信息
getSortedDBColumnHeaders(
  getPassMaterialIncomingFieldsAPI,
  getQualifiedMaterialsSupplierFieldsAPI,
  columns
);

// 获取子数据
const handleExtractIds = async (giftList, dateQuery) => {
  console.log(giftList, "giftList");

  // 二次处理成query'
  const queryGiftList = giftList.map(item => {
    return {
      name: "materialCode",
      logic: "",
      query: item,
      condition: "in"
    };
  });

  /** 判断是用于父查子数据(物料代码数组)还是用于条件查询，条件查询使用分页，父查子数据使用全部数据 */
  const res1 = await getQualifiedMaterialsSupplierAPI(
    {
      page: {
        current: queryData.value.page.current,
        size: giftList.length > 0 ? -1 : queryData.value.page.size
      },
      query: [...queryData.value.query, ...queryGiftList],
      order: []
    },
    dateQuery
  );

  console.log(res1, "1010");
  // const res2 = await getPassMaterialIncomingAPI(queryData.value);
  // console.log(res2, "1010");

  return {
    saleOrderArrResult: [...res1.data.records],
    saleOrderArrResultTotal: res1.data.total
  };
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPassMaterialIncomingAPI,
  {
    states: homeMap,
    addPercentSigns: ["deliveryProgress"],
    isExpand: true,
    handleExtractIds
  }
  // true,
  // {},
  // true
);

// 排序条件
const tableRowWarnStatus = reactive([
  {
    columnKey: "completionStatus",
    operator: "==",
    threshold: "未开始",
    className: "row-pending"
  },
  {
    columnKey: "completionStatus",
    operator: "==",
    threshold: "进行中",
    className: "row-pending"
  }
]);

// 获取路由对象
const router = useRouter();
const nodeStore = useNodeStore();
// 某行被点击
const handleRowClick = (row, column, cell) => {
  nodeStore.setQueryId(row.billNo);

  router.push({
    path: "/full-lifecycle/purchase-order",
    query: {
      billNo: row.billNo
    }
  });
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureEdit",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit();
    }
  },
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel();
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  },
  {
    label: "buttons.unfold",
    type: "primary",
    size: "small",
    action: () => {
      // 用于展示隐藏展开的字段
      isExpand.value = !isExpand.value;
      columns.value = columns.value.map(item => {
        // 如果 item.field 存在于 hiddenColumns 中，则设置 visible 属性
        if (hiddenColumns.value.some(it => it.field == item.prop)) {
          item.visible = !item.visible; // 设置可见性
        }
        return item; // 返回修改后的对象
      });
      // console.log(hiddenColumns.value, columns.value, "hidd");
      tableRef.value?.handleIsExtract();
    }
  }
]);
</script>
