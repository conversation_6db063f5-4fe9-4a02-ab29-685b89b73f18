<template>
  <div>
    <el-row class="flex justify-end w-full fend">
      <DateTimePicker :section="30" @updateDate="updateDate" />
    </el-row>

    <el-row v-show="isVisible" class="flex">
      <Card
        v-for="(item, index) in cardList"
        :key="index"
        class="flex-1"
        :title="item.title"
        :data="item.data"
      />
    </el-row>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <ReTable
      ref="tableRef"
      :mappingStatus="salesDeliveryOrderMonitoringMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity',
        'saleOutBoundCode'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      height="94%"
      :queryData="queryData"
      showTotal
      :getTotal="getTotal"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, watch, onMounted, toRaw } from "vue";
import { materialsArePutOnShelf } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import { salesDeliveryOrderMonitoringMap } from "@/views/sales-delivery-order-monitoring/utils";
import {
  getMaterialsArePutOnShelfFields,
  getMaterialsArePutOnShelfAPI,
  exportMaterialsArePutOnShelfExcel,
  getOverdueStockingBatches
} from "@/api/statistical-analysis/storage";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import Card from "@/views/home/<USER>/Card/saleCard.vue";
import { useRouter } from "vue-router";
import {
  getPlannedStockingBatchCount,
  getStockingAchievementRate,
  getUnstockedBatches
} from "@/api/statistical-analysis/storage";
import { useCardData } from "@/utils/hooks";

defineOptions({
  name: "MaterialsArePutOnShelf"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const router = useRouter();
const { isVisible } = useVisibility();
const date = ref(null);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    // {
    //   name: "whetherCompleted",
    //   sort: "asc"
    // },
    // {
    //   name: "countdownArrival",
    //   sort: "asc"
    // }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  handleEdit,
  columns
} = useFetchTableData(
  materialsArePutOnShelf,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getMaterialsArePutOnShelfFields, columns);

const sumList = ref(["inventoryOrderNumber"]);

const getTotal = () => {
  return sumList.value;
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getMaterialsArePutOnShelfAPI, {
  states: salesDeliveryOrderMonitoringMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "countdownArrival",
    operator: "<=",
    threshold: 3,
    className: "row-red",
    relation: "and",
    children: [
      {
        columnKey: "whetherCompleted",
        operator: "==",
        threshold: "否",
        className: "row-red",
        sift: "and"
      }
    ]
  },
  {
    columnKey: "countdownArrival",
    operator: ">",
    threshold: 3,
    className: "row-pending",
    relation: "and",
    children: [
      {
        columnKey: "countdownArrival",
        operator: "<=",
        threshold: 7,
        className: "row-pending",
        sift: "and"
      }
    ]
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportMaterialsArePutOnShelfExcel, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

const updateDate = time => {
  date.value = time;
  fetchCardData();
};
// 定义卡片配置
const cardConfig = [
  {
    title: "入库上架达成率",
    api: () => getStockingAchievementRate(date.value),
    formatter: data => data + "%"
  },
  {
    title: "应入库上架批次数",
    api: () => getPlannedStockingBatchCount(date.value)
  },
  {
    title: "未入库上架批次数",
    api: () => getUnstockedBatches(date.value)
  },
  {
    title: "超期入库上架批次数",
    api: () => getOverdueStockingBatches(date.value)
  }
];
const { cardList, fetchCardData } = useCardData(cardConfig);
</script>
<style lang="scss" scoped>
.fend {
  margin-bottom: 10px;
}
</style>
