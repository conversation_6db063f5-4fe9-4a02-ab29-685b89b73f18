<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="[]"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      height="auto"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { inventoryTrackingAndShortages } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import {
  getInventoryTrackingShortagesAPI,
  getInventoryTrackingShortagesFields,
  exportInventoryTrackingShortagesExcel
} from "@/api/statistical-analysis/storage/index";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";

defineOptions({
  name: "InventoryTrackingShortages"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: []
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  inventoryTrackingAndShortages,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getInventoryTrackingShortagesFields, columns);

const sumList = ref([
  "instantInventoryQty",
  "cumulativeDemands",
  "cumulativeSent",
  "cumulativeUnsent",
  "cumulativeSupplements",
  "purQuantity7Days",
  "purQuantity15Days",
  "purQuantity30Days",
  "prodQty7Days",
  "prodQty15Days",
  "prodQty30Days"
]);

const getTotal = () => {
  return sumList.value;
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getInventoryTrackingShortagesAPI,
  {
    states: []
  },
  false
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportInventoryTrackingShortagesExcel, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {};
</script>
