<template>
  <div>
    <el-row class="flex justify-end w-full">
      <DateTimePicker :section="30" @updateDate="updateDate" />
    </el-row>
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      showTotal
      :getTotal="getTotal"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { materialQualityAnalysisItemId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

import {
  getWPDetailAPI,
  getWPDetailFieldAPI,
  getWPDetailAPITotalAPI
} from "@/api/full-lifecycle/warehouse-preparation/wp-detail";
import {
  getMaterialPassFailRateStatisticsAPI,
  getMaterialQualityRatesAPI,
  exportMaterialQualityRatesAPI
} from "@/api/statistical-analysis/qualityInspection/material-pass-rate";
import DetailList from "@/components/ReTable/TableDetail.vue";

// 求和hook
import { useColumnsTotal, useParentColumnTag } from "@/utils/hooks";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

// 日期
const date = ref([]);
const updateDate = time => {
  date.value = time;
  // console.log(date.value, "time");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};
const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();
const route = useRoute();
const router = useRouter();
defineOptions({
  name: "materialPassRate"
});
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);

const routeTitle = ref("");

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "unqualifiedRate",
      sort: "desc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  materialQualityAnalysisItemId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getMaterialQualityRatesAPI, columns);

const sumList = ref(["totalProduction", "totalQualified", "totalUnqualified"]);

const getTotal = () => {
  return sumList.value;
};

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getMaterialPassFailRateStatisticsAPI,
  {
    states: []
  },
  false,
  { date: date }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportMaterialQualityRatesAPI, { date: date });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
