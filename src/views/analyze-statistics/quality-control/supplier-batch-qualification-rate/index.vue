<template>
  <div>
    <el-row class="flex justify-end w-full">
      <DateTimePicker :section="30" @updateDate="updateDate" />
    </el-row>
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :getTotal="getTotal"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { supplierBatchPassId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DateTimePicker from "@/components/ReDateTimePicker/index.vue";

import {
  getWPDetailAPI,
  getWPDetailFieldAPI,
  getWPDetailAPITotalAPI
} from "@/api/full-lifecycle/warehouse-preparation/wp-detail";
import {
  getSupplierBatchPassAPI,
  getSupplierBatchPassRatesAPI,
  exportSupplierBatchPassRatesAPI
} from "@/api/statistical-analysis/qualityInspection/supplier-batch-qualification-rate";
import DetailList from "@/components/ReTable/TableDetail.vue";

// 求和hook
import { useColumnsTotal, useParentColumnTag } from "@/utils/hooks";

/** 表格状态数据 */
import { warehousePreparationDetail } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();
const route = useRoute();
const router = useRouter();
defineOptions({
  name: "supplierBatchQualificationRate"
});
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);

const routeTitle = ref("");

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "onTimeDeliveryRate",
      sort: "asc"
    }
  ]
});

// 日期
const date = ref([]);
const updateDate = time => {
  date.value = time;
  console.log(date.value, "time123");
  tableRef.value.fetchTableData();
  // localStorage.setItem("date", JSON.stringify(time));
};

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  supplierBatchPassId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSupplierBatchPassRatesAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSupplierBatchPassAPI,
  {
    states: []
  },
  false,
  { date: date }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSupplierBatchPassRatesAPI, {
        date: date
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);
// 需要求和字段数组
const sumList = ref(["single"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getWPDetailAPITotalAPI, {
  queryId: nodeStore.sonPurchaseOrder.productionOrderNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
