<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="abnormalsituationoverviewMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="['orderNo']"
      :tableRowWarnStatus="tableRowWarnStatus"
      height="auto"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      :initialFilters="tableFilters"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
      @cell-dblclick="idDbClick"
    />

    <!-- 异常权限控制弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="异常权限控制"
      width="70%"
      draggable
      :close-on-click-modal="false"
    >
      <div class="min-h-[300px]">
        <div class="grid grid-cols-2 gap-5">
          <!-- 左侧部门异常权限 -->
          <div>
            <h3 class="text-lg font-bold mb-4">部门异常权限</h3>
            <el-card class="min-h-[200px]">
              <div class="p-4">
                <div class="mb-5">
                  <h4 class="text-sm text-gray-600 mb-3">异常类型</h4>
                  <el-select
                    v-model="selectedExceptionType"
                    placeholder="请选择异常类型"
                    class="w-full"
                    @change="handleExceptionTypeChange"
                  >
                    <el-option
                      v-for="item in exceptionTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div>
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm text-gray-600">关联部门</h4>
                    <div class="flex gap-2">
                      <el-button size="small" @click="toggleDeptSelect">
                        {{ isDeptAllSelected ? "取消全选" : "全选" }}
                      </el-button>
                      <el-button size="small" @click="toggleDeptExpand">
                        {{ isDeptExpanded ? "折叠" : "展开" }}
                      </el-button>
                    </div>
                  </div>
                  <el-tree
                    ref="deptTree"
                    :data="departmentTree"
                    :props="{
                      label: 'name',
                      children: 'children'
                    }"
                    show-checkbox
                    node-key="id"
                    @check="handleDeptCheck"
                  />
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧角色异常权限 -->
          <div>
            <h3 class="text-lg font-bold mb-4">角色异常权限</h3>
            <el-card class="min-h-[200px]">
              <div class="p-4">
                <div class="mb-5">
                  <h4 class="text-sm text-gray-600 mb-3">异常类型</h4>
                  <el-select
                    v-model="selectedRoleExceptionType"
                    placeholder="请选择异常类型"
                    class="w-full"
                    @change="handleRoleExceptionTypeChange"
                  >
                    <el-option
                      v-for="item in exceptionTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div>
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm text-gray-600">关联角色</h4>
                    <div class="flex gap-2">
                      <el-button size="small" @click="toggleRoleSelect">
                        {{ isRoleAllSelected ? "取消全选" : "全选" }}
                      </el-button>
                      <el-button size="small" @click="toggleRoleExpand">
                        {{ isRoleExpanded ? "折叠" : "展开" }}
                      </el-button>
                    </div>
                  </div>
                  <el-tree
                    ref="roleTreeRef"
                    :data="roleTree"
                    :props="{
                      label: 'name',
                      children: 'children'
                    }"
                    show-checkbox
                    node-key="id"
                    @check="handleRoleCheck"
                  />
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="handlePermissionCancel">取消</el-button>
          <el-button type="primary" @click="handlePermissionConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { abnormalSituationOverviewId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useUserStore } from "@/store/modules/user";
import { useRouter, useRoute } from "vue-router";
import { abnormalsituationoverviewMap } from "./utils/types/abnormal-situation-overview";
import {
  getAbnormalSituationOverviewFieldAPI,
  exportAbnormalSituationOverviewAPI,
  getAbnormalSituationOverview,
  updateAbnormalSituationStatusAPI
} from "@/api/abnormal-situation-overview";
import { useColumnsTotal, useParentColumnTag } from "@/utils/hooks";
// import {
//   getAbnormalSituationOverview,

// } from "@/api/system";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { getDeptList } from "@/api/system/dept";
import { getAllRoleList } from "@/api/system/role";
import {
  getErrType,
  getDeptExceptionRelation,
  getRoleExceptionRelation,
  updateDeptExceptionRelation,
  updateRoleExceptionRelation
} from "@/api/abnormal-situation-overview";
import { nextTick } from "process";
import { ElMessage, ElMessageBox } from "element-plus";

defineOptions({
  name: "abnormalSituationOverview"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "issuanceStatus",
      sort: "desc"
    }
  ]
});

const nodeStore = useNodeStore();
const router = useRouter();
const route = useRoute();
const useStore = useUserStore();

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  handleEdit,
  columns
} = useFetchTableData(
  abnormalSituationOverviewId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getAbnormalSituationOverviewFieldAPI, columns);

// 使用nextTick确保数据更新
nextTick(() => {
  console.debug("columns初始数据:", columns.value);
});

// 监听columns变化
watch(
  () => columns.value,
  newColumns => {
    if (newColumns && newColumns.length > 0) {
      console.debug("columns数据已获取:", newColumns);
    }
  },
  { deep: true }
);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getAbnormalSituationOverview, {
  states: abnormalsituationoverviewMap
});

const tableRowWarnStatus = reactive([
  // {
  //   columnKey: "issuanceStatus",
  //   operator: "==",
  //   threshold: "未发",
  //   className: "row-pending"
  // },
  // {
  //   columnKey: "issuanceStatus",
  //   operator: "==",
  //   threshold: "未发完",
  //   className: "row-pending"
  // },
  // // 添加根据处理状态设置行颜色的规则
  // {
  //   columnKey: "resolved",
  //   operator: "==",
  //   threshold: "待处理",
  //   className: "row-pending-gray"
  // },
  // {
  //   columnKey: "resolved",
  //   operator: "==",
  //   threshold: "已分配",
  //   className: "row-brown"
  // },
  // {
  //   columnKey: "resolved",
  //   operator: "==",
  //   threshold: "处理中",
  //   className: "row-processing-yellow"
  // },
  // {
  //   columnKey: "resolved",
  //   operator: "==",
  //   threshold: "已处理",
  //   className: "row-processed-green"
  // }
]);

// 异常消息跳转处理

import { getHeaderPage } from "@/api/system/common";

// 定义高亮行的样式类
const highlightRowClass = "row-highlight";

// 处理跳转定位和高亮行
const handleAbnormalJump = async () => {
  if (!route.query.id) return;

  try {
    // 请求所有异常数据
    const response = await getAbnormalSituationOverview({
      page: { current: 1, size: 99999999, default: 20 },
      query: [],
      order: [
        {
          name: "issuanceStatus",
          sort: "desc"
        }
      ]
    });

    if (response.data) {
      const allAbnormalData = response.data.records;
      const locationIndex = allAbnormalData.findIndex(
        item => item.id == route.query.id
      );

      if (locationIndex === -1) {
        ElMessage.warning("未找到对应的异常记录");
        return;
      }

      // 根据locationIndex判断数据在第几页
      // 从数据库中获取分页大小
      let currentPageSize = queryData.value.page.size;
      const currentMenu = router.currentRoute.value.path;
      const pageSizeRecord = await getHeaderPage(
        currentMenu.replaceAll("/", "")
      );

      // 获取当前页
      if (pageSizeRecord.data) {
        currentPageSize = pageSizeRecord.data.pageSize;
      }

      // 计算并设置当前页码
      if (locationIndex >= 0) {
        const targetPage = Math.ceil((locationIndex + 1) / currentPageSize);
        console.log(`定位到第 ${targetPage} 页，索引位置: ${locationIndex}`);

        // 判断目标页是否与当前页不同
        if (targetPage !== queryData.value.page.current) {
          // 如果不在当前页，则设置页码并重新加载数据
          queryData.value.page.current = targetPage;

          // 重新加载数据
          await fetchTableData();

          console.log(`页面已跳转到第 ${targetPage} 页`);
        } else {
          console.log("数据在当前页，无需跳转");
        }
      }

      // 添加高亮行样式
      tableRowWarnStatus.push({
        columnKey: "id",
        operator: "==",
        threshold: route.query.id,
        className: highlightRowClass
      });

      // 等待表格渲染完成后滚动到高亮行
      nextTick(() => {
        const rowHeight = 45;
        const targetIndex = parseInt(locationIndex - 2);
        const scrollPosition = targetIndex * rowHeight;
        // 获取表格内部的vxe-table实例
        const vxeTableInstance = tableRef.value.$refs.tableRef;
        // 获取表格滚动容器
        const bodyElem = vxeTableInstance.$el.querySelector(
          ".vxe-table--body-wrapper"
        );
        bodyElem.scrollTop = scrollPosition;
      });
    }
  } catch (error) {
    console.error("获取异常数据失败:", error);
    ElMessage.error("获取异常数据失败");
  }
};

// 执行跳转处理
if (route.query.id) {
  handleAbnormalJump();
}

// 添加弹窗控制变量
const permissionDialogVisible = ref(false);

const roleRelatitonQueryData = {
  pageNum: 1,
  pageSize: 10,
  deptException: {
    roleId: "",
    errType: ""
  }
};

const deptRelatitonQueryData = {
  pageNum: 1,
  pageSize: 10,
  deptException: {
    deptId: "",
    errType: ""
  }
};

// 异常权限控制相关数据
const selectedExceptionType = ref<{ label: string; value: number } | null>(
  null
);
const selectedRoleExceptionType = ref<{ label: string; value: number } | null>(
  null
);

// 树形组件ref
const deptTree = ref();
const roleTreeRef = ref();

// 模拟数据，实际项目中应该从接口获取
const exceptionTypes = ref<{ label: string; value: number }[]>([]);

// 部门树形数据
const departmentTree = ref([]);

// 构建部门树形结构
const buildDepartmentTree = departments => {
  const map = {};
  const tree = [];

  // 首先将所有部门存入map
  departments.forEach(dept => {
    map[dept.id] = { ...dept, children: [] };
  });

  // 构建树形结构
  departments.forEach(dept => {
    const node = map[dept.id];
    if (dept.parentId === 0) {
      tree.push(node);
    } else {
      const parent = map[dept.parentId];
      if (parent) {
        parent.children.push(node);
      }
    }
  });

  return tree;
};

// 处理部门选中事件
const handleDeptCheck = (data, checked) => {
  if (!selectedExceptionType.value) {
    ElMessage.warning("请先选择异常类型");
    return;
  }
  updateDeptExceptionRelation({
    deptId: checked.checkedKeys,
    errType: selectedExceptionType.value.label
  })
    .then(res => {
      ElMessage.success("部门异常权限更新成功");
    })
    .catch(error => {
      ElMessage.error("部门异常权限更新失败");
    });
};

// 角色树形数据
const roleTree = ref([]);

// 构建角色树形结构
const buildRoleTree = roles => {
  const map = {};
  const tree = [];

  // 首先将所有角色存入map
  roles.forEach(role => {
    map[role.id] = { ...role, children: [] };
  });

  // 构建树形结构
  roles.forEach(role => {
    const node = map[role.id];
    if (role.parentId === null) {
      tree.push(node);
    } else {
      const parent = map[role.parentId];
      if (parent) {
        parent.children.push(node);
      }
    }
  });

  return tree;
};

// 处理角色选中事件
const handleRoleCheck = (data, checked) => {
  if (!selectedRoleExceptionType.value) {
    ElMessage.warning("请先选择异常类型");
    return;
  }

  const selectedType =
    exceptionTypes.value[selectedRoleExceptionType.value.value];
  updateRoleExceptionRelation({
    roleId: checked.checkedKeys,
    errType: selectedType.label
  })
    .then(res => {
      ElMessage.success("角色异常权限更新成功");
    })
    .catch(error => {
      ElMessage.error("角色异常权限更新失败");
    });
};

// 定义初始过滤条件
const tableFilters = reactive({});

onMounted(async () => {
  if (useStore.roles.includes("admin")) {
    try {
      // 并行请求角色列表和部门列表
      const [errTypeRes, roleRes, deptRes] = await Promise.all([
        getErrType(),
        getAllRoleList(),
        getDeptList({ pageSize: 999, pageNum: 1 })
      ]);

      // 将异常类型列表转换为对象数组
      exceptionTypes.value = errTypeRes.data.map((item, index) => ({
        label: item,
        value: index
      }));

      // 构建部门树形结构
      departmentTree.value = buildDepartmentTree(deptRes.data.resultList);

      // 构建角色树形结构
      roleTree.value = buildRoleTree(roleRes.data);
    } catch (error) {
      console.error("获取列表数据失败:", error);
      ElMessage.error("获取列表数据失败");
    }
  }
});

// 修改按钮配置
const topButtons = ref([
  ...(useStore.roles.includes("admin")
    ? [
        {
          label: "buttons.pureAbnormalPermissionControl",
          type: "primary",
          size: "small",
          action: () => {
            permissionDialogVisible.value = true;
          }
        }
      ]
    : []),
  {
    label: "buttons.pureCases",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit("status", updateAbnormalSituationStatusAPI, "id");
    }
  },
  {
    label: "buttons.pureStatus",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit("handle");
    }
  },
  {
    label: "buttons.pureDistribution",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit("distribution");
    }
  },
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportAbnormalSituationOverviewAPI, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 添加确认按钮处理函数
const handlePermissionConfirm = () => {
  // 这里可以添加权限控制的确认逻辑
  permissionDialogVisible.value = false;
};

const handlePermissionCancel = () => {
  permissionDialogVisible.value = false;
};

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");

  // 如果是从跳转过来的，并且点击的行就是高亮行，则清除URL中的查询参数
  if (route.query.id && row.id == route.query.id) {
    // 使用替换当前路由的方式，不会触发页面重新加载
    router.replace({ path: route.path });
  }
};
// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  if (column.property === "orderNo") {
    if (row.orderNo?.includes("CG")) {
      const columnList = ["orderNo"];
      setParentColumnTag(
        row,
        columns.value,
        columnList,
        "PurchaseColumnFields"
      );
      console.log("采购异常");
      nodeStore.setSonPurchaseOrderDetailFinalMaterialSourceNo(row.orderNo);
      router.push({
        path: "/full-lifecycle/preparation/purchase-order-detail"
      });
    } else if (row.errMsg?.includes("MO")) {
      console.log("退料异常-车间退补料/车间生产明细");
      const errMsg = row.errMsg;
      const extractedInfo = extractInfoFromErrMsg(errMsg);
      console.log(extractedInfo);
      if (row.errMsg?.includes("退料")) {
        console.log("退料异常-车间退补料明细");
        router.push({
          path: "/analyze-statistics/storage/return-process-total",
          query: {
            productionOrderNo: extractedInfo.moNumber,
            materialCode: extractedInfo.materialCode
          }
        });
      } else {
        console.log("跳转车间生产明细");
        const columnList = ["orderNo"];
        setParentColumnTag(
          row,
          columns.value,
          columnList,
          "filteredColumnFields"
        );
        router.push({
          path: "/full-lifecycle/workshop-production/workshop-production-detail",
          query: {
            billNo: row.orderNo
          }
        });
      }
    } else {
      console.log("交期异常-跳转销售");
      router.push({
        path: "/sales-order/list",
        query: {
          billNo: row.orderNo
        }
      });
    }
  }
};

/**
 * 提取生产任务单号和物料代码
 * @param {string} errMsg - 错误消息
 * @returns {Object} - 包含生产任务单号和物料代码的对象
 */
function extractInfoFromErrMsg(errMsg) {
  // 使用正则表达式匹配生产任务单号和物料代码
  const moRegex = /生产任务单号([^\s物料]+)/; // 匹配"生产任务单号"后面不是空格或"物料"的字符
  const materialRegex = /物料代码为([\d.]+)/; // 匹配"物料代码为"后面的数字和点

  const moMatch = errMsg.match(moRegex);
  const materialMatch = errMsg.match(materialRegex);

  return {
    moNumber: moMatch ? moMatch[1] : "",
    materialCode: materialMatch ? materialMatch[1] : ""
  };
}

// 单元格id被双击
const idDbClick = (row, column, cell, event) => {
  if (column.label == "异常表id") {
    console.log(row, column, cell, event, "单元格id被双击");
  }
  console.log(row, column, cell, event);
};

// 部门异常类型变化处理函数
const handleExceptionTypeChange = (value: number) => {
  // 清空部门树的选中状态
  nextTick(() => {
    deptTree.value?.setCheckedKeys([]);
  });

  const selectedType = exceptionTypes.value.find(type => type.value === value);

  selectedExceptionType.value = selectedType || null;

  // 更新查询参数
  if (selectedType) {
    deptRelatitonQueryData.deptException.errType = selectedType.label;
  }

  // 获取该异常类型关联的部门
  getDeptExceptionRelation(deptRelatitonQueryData).then(res => {
    // 获取需要选中的部门ID列表，只选中与当前异常类型匹配的数据
    const checkedDeptIds = res.data.resultList
      .filter(item => item.errType === selectedType?.label)
      .map(item => item.deptId);

    // 设置树形组件的选中状态
    nextTick(() => {
      deptTree.value?.setCheckedKeys(checkedDeptIds);
    });
  });
};

// 角色部门异常类型变化处理函数
const handleRoleExceptionTypeChange = (value: number) => {
  // 清空角色树的选中状态
  nextTick(() => {
    roleTreeRef.value?.setCheckedKeys([]);
  });

  const selectedType = exceptionTypes.value.find(type => type.value === value);

  selectedRoleExceptionType.value = selectedType || null;

  // 更新查询参数
  if (selectedType) {
    roleRelatitonQueryData.deptException.errType = selectedType.label;
  }

  // 获取该异常类型关联的角色
  getRoleExceptionRelation(roleRelatitonQueryData).then(res => {
    // 获取需要选中的角色ID列表，只选中与当前异常类型匹配的数据
    const checkedRoleIds = res.data.resultList
      .filter(item => item.errType === selectedType?.label)
      .map(item => item.roleId);

    // 设置树形组件的选中状态
    nextTick(() => {
      roleTreeRef.value?.setCheckedKeys(checkedRoleIds);
    });
  });
};

// 添加状态变量
const isDeptAllSelected = ref(false);
const isDeptExpanded = ref(false);
const isRoleAllSelected = ref(false);
const isRoleExpanded = ref(false);

// 添加部门树操作方法
const toggleDeptSelect = () => {
  if (!selectedExceptionType.value) {
    ElMessage.warning("请先选择异常类型");
    return;
  }

  ElMessageBox.confirm(
    `是否${isDeptAllSelected.value ? "取消" : ""}选中所有部门？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      isDeptAllSelected.value = !isDeptAllSelected.value;
      if (isDeptAllSelected.value) {
        // 获取所有部门ID
        const getAllDeptIds = nodes => {
          let ids = [];
          nodes.forEach(node => {
            ids.push(node.id);
            if (node.children && node.children.length) {
              ids = ids.concat(getAllDeptIds(node.children));
            }
          });
          return ids;
        };
        const allDeptIds = getAllDeptIds(departmentTree.value);
        deptTree.value?.setCheckedKeys(allDeptIds);
        // 触发选中事件
        handleDeptCheck(null, { checkedKeys: allDeptIds });
      } else {
        deptTree.value?.setCheckedKeys([]);
        // 触发选中事件
        handleDeptCheck(null, { checkedKeys: [] });
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

const toggleDeptExpand = () => {
  isDeptExpanded.value = !isDeptExpanded.value;
  const nodes = deptTree.value?.store.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = isDeptExpanded.value;
    });
  }
};

// 添加角色树操作方法
const toggleRoleSelect = () => {
  if (!selectedRoleExceptionType.value) {
    ElMessage.warning("请先选择异常类型");
    return;
  }

  ElMessageBox.confirm(
    `是否${isRoleAllSelected.value ? "取消" : ""}选中所有角色？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      isRoleAllSelected.value = !isRoleAllSelected.value;
      if (isRoleAllSelected.value) {
        // 获取所有角色ID
        const getAllRoleIds = nodes => {
          let ids = [];
          nodes.forEach(node => {
            ids.push(node.id);
            if (node.children && node.children.length) {
              ids = ids.concat(getAllRoleIds(node.children));
            }
          });
          return ids;
        };
        const allRoleIds = getAllRoleIds(roleTree.value);
        roleTreeRef.value?.setCheckedKeys(allRoleIds);
        // 触发选中事件
        handleRoleCheck(null, { checkedKeys: allRoleIds });
      } else {
        roleTreeRef.value?.setCheckedKeys([]);
        // 触发选中事件
        handleRoleCheck(null, { checkedKeys: [] });
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

const toggleRoleExpand = () => {
  isRoleExpanded.value = !isRoleExpanded.value;
  const nodes = roleTreeRef.value?.store.nodesMap;
  if (nodes) {
    Object.values(nodes).forEach(node => {
      node.expanded = isRoleExpanded.value;
    });
  }
};
</script>
<style>
/* 闪烁动画 */
@keyframes highlight-pulse {
  0% {
    background-color: #fef0f0;
  }

  50% {
    background-color: #ffd54f;
  }

  100% {
    background-color: #fef0f0;
  }
}

.row-pending-gray {
  background-color: #f2f2f2; /* 灰色 */
}

.row-processing-yellow {
  background-color: #ffc; /* 黄色 */
}

.row-processed-green {
  background-color: #e6ffe6; /* 绿色 */
}

.row-brown {
  background-color: #d2b48c; /* 棕色 */
}

.row-processed-blue {
  background-color: #e6f0ff !important; /* 蓝色 */
}

/* 高亮行样式 - 用于跳转定位 */
.row-highlight {
  background-color: #fef0f0 !important; /* 浅红色高亮 */
  animation: highlight-pulse 2s ease-in-out 3; /* 闪烁动画效果，重复3次 */
}
</style>

<style scoped>
/* 移除原有的样式，使用 Tailwind CSS 替代 */
</style>
