<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="null"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script setup lang="tsx">
import { useRoute } from "vue-router";
import ReTable from "@/components/ReTable/Table.vue";

import { useFetchTableData } from "@/utils/hooks";
import { reactive, ref } from "vue";
import { getPurchaseDetailAPI } from "@/api/shortage-materials";

defineOptions({
  name: "ShortageMaterialsDetails"
});

const route = useRoute();
const { materialId } = route.query;

const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "completionRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(null, tableRef, queryData, tableQuery, selectRows);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(null, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPurchaseDetailAPI,
  {
    states: null
  },
  false,
  {
    queryId: materialId
  }
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
// 需要求和字段数组
const sumList = ref([]);
const getTotal = getTotalHook(sumList, null, {
  queryId: materialId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>

<style scoped lang="scss"></style>
