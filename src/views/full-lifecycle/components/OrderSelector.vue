<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { getSaleOrder, getSaleOrderSelect } from "@/api/home/<USER>";
import { useRouter } from "vue-router";
import { useVisibility } from "@/utils/useVisibility";

const nodeStore = useNodeStore();
const router = useRouter();

const selectedOption = ref(nodeStore.queryId || "");
const selectedSaleOrder = ref(nodeStore.saleOrderId || "");
const loading = ref(false);
const saleOrderLoading = ref(false);
const filteredOrderNumbers = ref([]);
const filteredSaleOrders = ref([]);
const saleOrders = ref([]);
const { isVisible } = useVisibility();

onMounted(() => {
  getOrderList();
});

const queryData = ref({
  page: { current: 1, size: 20 },
  query: [],
  order: [],
  records: []
});

const getOrderList = async () => {
  try {
    const response = await getSaleOrderSelect();
    queryData.value.records = response.data.records;
  } catch (error) {
    console.debug("获取订单列表失败:", error);
  }
};

// 处理值变化
const orderNoChange = (value: string, index: number) => {
  if (!value) return;
  nodeStore.setQueryId(value);
  nodeStore.setSaleOrderId(saleOrders.value[index]);
  router.go(0);
};

const saleOrderNoChange = (value: string, index: number) => {
  if (!value) return;
  nodeStore.setSaleOrderId(value);
  nodeStore.setQueryId(filteredOrderNumbers.value[index]);
  router.go(0);
};

// 订单号远程搜索方法
const remoteMethod = async (query: string) => {
  // debugger;
  if (!query) {
    filteredOrderNumbers.value = [];
    return;
  }

  loading.value = true;
  try {
    // 从本地records中进行模糊查询
    const records = queryData.value.records || [];
    const filteredRecords = records.filter(
      record => record.sytText1?.toLowerCase().includes(query.toLowerCase())
      // ||
      // record.billNo?.toLowerCase().includes(query.toLowerCase())
    );

    filteredOrderNumbers.value = filteredRecords.map(record => record.sytText1);
    saleOrders.value = filteredRecords.map(record => record.billNo);
  } catch (error) {
    console.error("搜索失败:", error);
    filteredOrderNumbers.value = [];
    saleOrders.value = [];
  } finally {
    loading.value = false;
  }
};

// 销售订单号远程搜索方法
const saleOrderRemoteMethod = async (query: string) => {
  // debugger;
  if (!query) {
    filteredSaleOrders.value = [];
    return;
  }

  saleOrderLoading.value = true;
  try {
    // 从本地records中进行模糊查询
    const records = queryData.value.records || [];
    const filteredRecords = records.filter(
      record => record.billNo?.toLowerCase().includes(query.toLowerCase())
      // ||
      // record.sytText1?.toLowerCase().includes(query.toLowerCase())
    );

    filteredSaleOrders.value = filteredRecords.map(record => record.billNo);
    filteredOrderNumbers.value = filteredRecords.map(record => record.sytText1);
  } catch (error) {
    console.error("搜索失败:", error);
    filteredSaleOrders.value = [];
    filteredOrderNumbers.value = [];
  } finally {
    saleOrderLoading.value = false;
  }
};
</script>

<template>
  <div v-if="isVisible" class="flex items-center gap-2 h-[32px]">
    <span class="leading-[32px]">订单号：</span>
    <el-select
      v-model="selectedOption"
      placeholder="输入订单号"
      filterable
      remote
      :remote-method="remoteMethod"
      :loading="loading"
      class="filter-element"
      @change="
        value => orderNoChange(value, filteredOrderNumbers.indexOf(value))
      "
    >
      <el-option
        v-for="billNoItem in filteredOrderNumbers"
        :key="billNoItem + '_2'"
        :label="billNoItem"
        :value="billNoItem"
      />
    </el-select>
    <span class="leading-[32px]">销售订单号：</span>
    <el-select
      v-model="selectedSaleOrder"
      placeholder="输入销售订单号"
      filterable
      remote
      :remote-method="saleOrderRemoteMethod"
      :loading="saleOrderLoading"
      class="filter-element"
      @change="
        value => saleOrderNoChange(value, filteredSaleOrders.indexOf(value))
      "
    >
      <el-option
        v-for="saleOrderItem in filteredSaleOrders"
        :key="saleOrderItem + '_1'"
        :label="saleOrderItem"
        :value="saleOrderItem"
      />
    </el-select>
  </div>
</template>

<style scoped>
.filter-element {
  width: 200px;
  margin-bottom: 0;
}
</style>
