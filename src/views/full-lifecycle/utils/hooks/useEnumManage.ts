/**
 * @function 获取枚举值，指定无下标时返回值
 * @param enumObj 枚举对象
 * @param otherValue 其他下标返回值
 * @param index 枚举下标
 * @returns 枚举值
 */
export const useCheckEnum = (enumObj: any, otherValue: any, index: any) => {
  return enumObj[index] ? enumObj[index] : otherValue;
};

/**
 * @function 根据传入的枚举获取对应枚举对象数组
 * @param enumObj 枚举对象
 * @returns 枚举对象数组
 */

export const GetEnumArray = ({ ...enumArray }: any[]) => {
  return enumArray;
};
