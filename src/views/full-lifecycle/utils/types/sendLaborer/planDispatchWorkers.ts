/* 派工子流程--计划派工 - 是否删除 */
const isDeleted = {
  name: "isDeleted",
  0: "否",
  1: "是"
};
/* 派工子流程--计划派工 - 是否完结 */
const isFinished = {
  name: "isFinished",
  false: "否",
  true: "是"
};
/* 派工子流程--计划派工 - 结束工序 */
const isLastProcess = {
  name: "isLastProcess",
  false: "否",
  true: "是"
};
/* 派工子流程--计划派工 - 是否派工 */
const isOperators = {
  name: "isOperators",
  0: "否",
  1: "是"
};
/* 派工子流程--计划派工 - 工序是否完结 */
const processIsFinished = {
  name: "processIsFinished",
  false: "未完结",
  true: "完结"
};
/* 派工子流程--计划派工 - 状态 */
const productionOrderState = {
  name: "productionOrderState",
  1: "计划",
  2: "下达",
  3: "部分完成",
  4: "全部完成",
  5: "完结"
};
/* 派工子流程--计划派工 - 工序类型 */
const workingProcedureType = {
  name: "workingProcedureType",
  0: "普通工序类型",
  1: "进度类型工序类型（单次）",
  2: "进度类型工序类型（累计）"
};
export const planDispatchWorkersMap = [
  isDeleted,
  isFinished,
  isLastProcess,
  isOperators,
  processIsFinished,
  productionOrderState,
  workingProcedureType
];
