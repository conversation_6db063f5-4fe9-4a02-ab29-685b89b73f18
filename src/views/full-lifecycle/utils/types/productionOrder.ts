/** 生产订单 - 入库状态 */
const inboundState = {
  0: "未入库",
  1: "部分入库",
  2: "全部入库",
  name: "inboundState"
};

/** 生产订单 - 是否流转流程 */
enum isTurnOrder {
  true = "是",
  false = "否",
  name = "isTurnOrder"
}

/** 生产订单 - 急单 */
const urgentSingle = {
  0: "普通",
  1: "急单",
  name: "urgentSingle"
};

/** 生产订单 - 状态 */
const productionOrderState = {
  1: "计划",
  2: "下达",
  3: "部分完成",
  4: "全部完成",
  5: "完结",
  name: "productionOrderState"
};

export const productionOrderMap = [
  inboundState,
  isTurnOrder,
  urgentSingle,
  productionOrderState
];
