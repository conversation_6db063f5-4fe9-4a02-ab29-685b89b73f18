/** 仓库备料-采购订单-总体-来料检验-表头状态
 * @enum  0: 待检, 1: 部分检验, 2: 全部检验, 3: 完结状态
 */
const headState = {
  0: "待检",
  1: "部分检验",
  2: "全部检验",
  3: "完结状态",
  name: "headState"
};

/** 仓库备料-采购订单-总体-来料检验-状态
 *  @enum  0: 待检, 1: 已检, 2: 完结
 * */
const detailState = {
  0: "待检",
  1: "已检",
  2: "完结",
  name: "detailState"
};
/** 仓库备料-采购订单-总体-来料检验-急单
 *  @enum  0: 急单, 1: 普通
 * */
const urgentSingle = {
  0: "急单",
  1: "普通",
  name: "urgentSingle"
};

/** 仓库备料-采购订单-总体-采购送货-是否全部拒收
 *  @enum  false: 否, true: 是
 * */
const mlotStatus = {
  0: "计划",
  1: "下达",
  2: "完结",
  name: "mlotStatus"
};

/** 仓库备料-采购订单-总体-采购送货-是否全部拒收
 *  @enum  false: 否, true: 是
 * */
const isIncomingInspection = {
  false: "否",
  true: "是",
  name: "isIncomingInspection"
};
export const outorderProcurementDeliveryMap = [
  headState,
  detailState,
  urgentSingle,
  mlotStatus,
  isIncomingInspection
];
