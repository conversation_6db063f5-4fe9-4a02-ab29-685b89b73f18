/** 委外总体-来料检验-表头状态
 * @enum  0: 待检, 1: 部分检验, 2: 全部检验, 3: 完结状态
 */
const headState = {
  1: "待检",
  2: "部分检验",
  3: "全部检验",
  4: "完结状态",
  name: "headState"
};

/** 委外总体-来料检验-急单
 *  @enum  0: 急单, 1: 普通
 * */
const urgentSingle = {
  0: "急单",
  1: "普通",
  name: "urgentSingle"
};
/** 委外总体-来料检验-是否全部拒收
 *  @enum  false: 否, true: 是
 * */
const isAllRejected = {
  false: "否",
  true: "是",
  name: "isAllRejected"
};
/** 委外总体-来料检验-状态
 *  @enum  false: 否, true: 是
 * */
const detailState = {
  1: "待检",
  2: "已检",
  3: "完结",
  name: "detailState"
};

/** 委外总体-来料检验-状态
 *  @enum  false: 否, true: 是
 * */
const inspectionType = {
  1: "抽检",
  2: "全检",
  name: "inspectionType"
};

export const outorderIncomingInspectionMap = [
  headState,
  urgentSingle,
  isAllRejected,
  detailState,
  inspectionType
];
