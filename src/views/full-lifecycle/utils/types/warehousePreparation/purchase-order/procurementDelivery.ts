/** 仓库备料-采购订单-总体-采购送货-是否来料检验
 * @enum  true: 是, false: 否
 */
enum isIncomingInspection {
  name = "isIncomingInspection",
  false = "否",
  true = "是"
}

/** 仓库备料-采购订单-总体-采购送货-是否删除
 *  @enum  0: 否, 1: 是
 * */
const isDeleted = {
  0: "否",
  1: "是",
  name: "isDeleted"
};
/** 仓库备料-采购订单-总体-采购送货-状态
 *  @enum  0: 急单, 1: 普通
 * */
const mlotStatus = {
  0: "计划",
  1: "下达",
  2: "完结",
  name: "mlotStatus"
};

const inspectionType = {
  2: "全检",
  1: "抽检",
  name: "inspectionType"
};

const isAllRejected = {
  0: "否",
  1: "是",
  name: "isAllRejected"
};

export const purchaseOrderProcurementDeliveryMap = [
  isIncomingInspection,
  isDeleted,
  mlotStatus,
  inspectionType,
  isAllRejected
];
