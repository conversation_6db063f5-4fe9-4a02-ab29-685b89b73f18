/* 仓库备料-入库状态 */
const inboundState = {
  0: "未入库",
  1: "部分入库",
  2: "全部入库",
  name: "inboundState"
};
/* 仓库备料-是否转流程 */
enum isTurnOrder {
  false = "否",
  true = "是",
  name = "isTurnOrder"
}
/**
 * 仓库备料-
 * @急单/普通
 */
const urgentSingle = {
  0: "普通",
  1: "急单",
  name: "urgentSingle"
};
/* 仓库备料-是否删除 */
const preparationisDeleted = {
  0: "否",
  1: "是",
  name: "preparationisDeleted"
};

/** 仓库备料 - 状态 */
const productionOrderState = {
  1: "计划",
  2: "下达",
  3: "部分完成",
  4: "全部完成",
  5: "完结",
  name: "productionOrderState"
};

export const warehousePreparationMap = [
  inboundState,
  isTurnOrder,
  urgentSingle,
  preparationisDeleted,
  productionOrderState
];
