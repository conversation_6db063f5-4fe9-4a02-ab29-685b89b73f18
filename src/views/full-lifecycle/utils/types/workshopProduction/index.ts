/* 车间生产-是否检验工序 */
enum isCheckProcedure {
  name = "isCheckProcedure",
  false = "否",
  true = "是"
}
/* 车间生产-是否派工 */
const isOperators = {
  0: "否",
  1: "是",
  name: "isOperators"
};
/* 车间生产-是否删除 */
enum productionIsDeleted {
  name = "productionIsDeleted",
  false = "否",
  true = "是"
}
/* 车间生产-是否完结 */
enum isFinished {
  name = "isFinished",
  false = "否",
  true = "是"
}
/* 车间生产-结束工序 */
enum isLastProcess {
  name = "isLastProcess",
  false = "否",
  true = "是"
}
/* 车间生产-工序是否完结 */
enum processIsFinished {
  name = "processIsFinished",
  false = "未完结",
  true = "完结"
}
/* 车间生产-状态 */
const productionOrderState = {
  1: "计划",
  2: "下达",
  3: "部分完成",
  4: "全部完成",
  5: "完结",
  name: "productionOrderState"
};
/* 车间生产-工序类型 */
const workingProcedureType = {
  0: "普通工序类型",
  1: "进度类型工序类型（单次）",
  2: "进度类型工序类型（累计）",
  name: "workingProcedureType"
};

export const workshopProductionMap = [
  isCheckProcedure,
  isOperators,
  productionIsDeleted,
  isFinished,
  isLastProcess,
  processIsFinished,
  productionOrderState,
  workingProcedureType
];

export * from "./workshopReturn";
export * from "./workshopFeeding";
export * from "./replenishment";
export * from "./workshopProductionDetail";
