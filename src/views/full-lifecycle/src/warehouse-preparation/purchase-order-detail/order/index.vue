<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />

    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :getTotal="getTotal"
      showTotal
      :headerCellKeys="[
        'stockInQty',
        // 'remainStockInQty',
        'unstockedQualifiedQuantity',
        'totalInspectionQuantity',
        'inspectedQuantity',
        'pendingInspectionQuantity',
        'totalQualifiedQuantity',
        'totalUnqualifiedQuantity',
        'totalRejectedQuantity',
        'totalRefusalReentryQuantity',
        'totalPlanDeliveryNumber',
        'totalDeliveryNumber',
        'undeliveredNumber'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted, toRaw } from "vue";
import { fullLifecyclePurchaseOrderDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/saleOrder";
import {
  getPurchaseOrderDetailAPI,
  getPurchaseOrderDetailFieldAPI,
  getPurchaseOrderDetailTotalAPI,
  exportPurchaseOrderDetailsAPI
} from "@/api/full-lifecycle/warehouse-preparation/purchase-order-detail";
// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils/hooks";
import DetailList from "@/components/ReTable/TableDetail.vue";
/** 表格状态数据 */
import { salesOrderStateMap, GetEnumArray } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const { getParentColumnTag, setParentColumnTag } = useParentColumnTag();
const router = useRouter();
const route = useRoute();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const historyColumnsKeys = ref([]);

const routeTitle = ref("");

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    // {
    //   name: "date",
    //   sort: "asc"
    // }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecyclePurchaseOrderDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getPurchaseOrderDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPurchaseOrderDetailAPI,
  {
    states: salesOrderStateMap
  },
  false,
  {
    queryId: nodeStore.sonPurchaseOrderDetail.finalMaterialSourceNo
  }
);

// 高亮哪个状态情况下的列
// 表示某列字段的columnKey的键值等于 status 的值时，该列的单元格会应用 className 指定的样式类名
// greater（大于）、less（小于）、amount（等于）三种状态  还有做————————正在构思
const tableRowWarnStatus = reactive([
  {
    columnKey: "colorTye",
    operator: "==",
    threshold: "3",
    className: "row-red"
  },

  {
    columnKey: "colorTye",
    operator: "==",
    threshold: "2",
    className: "row-processing"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportPurchaseOrderDetailsAPI, {
        queryId: nodeStore.sonPurchaseOrderDetail.finalMaterialSourceNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "qty",
  "totalPlanDeliveryNumber",
  "totalDeliveryNumber",
  "undeliveredNumber",
  "totalInspectionQuantity",
  "inspectedQuantity",
  "pendingInspectionQuantity",
  "totalQualifiedQuantity",
  "totalRejectedQuantity",
  "totalRefusalReentryQuantity",
  "unstockedQualifiedQuantity",
  "onTimeDeliveryNumber",
  "timeOutDeliveryNumber",
  "onTimeInspectionNumber",
  "timeOutInspectionNumber"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getPurchaseOrderDetailTotalAPI, {
  queryId: nodeStore.sonPurchaseOrderDetail.finalMaterialSourceNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  const columnList = ["billNo", "orderNumber"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  // 跳转到《某个生产任务单号的车间入库明细数据》
  if (
    column.property === "stockInQty" ||
    column.property === "unstockedQualifiedQuantity"
  ) {
    console.log("row。orderNumber", row.orderNumber);

    nodeStore.setPurchaseOrderDetailLineNumber(row.orderNumber);
    router.push("/full-lifecycle/warehousingShelves/warehousingShelvesDetail");
  }
  if (
    column.property === "totalInspectionQuantity" ||
    column.property === "inspectedQuantity" ||
    column.property === "pendingInspectionQuantity" ||
    column.property === "totalQualifiedQuantity" ||
    column.property === "totalUnqualifiedQuantity" ||
    column.property === "totalRejectedQuantity" ||
    column.property === "totalRefusalReentryQuantity"
  ) {
    console.log("row。orderNumber", row.orderNumber);

    nodeStore.setPurchaseOrderDetailLineNumber(row.orderNumber);
    router.push("/full-lifecycle/incomingInspection/incomingInspectiondetail");
  }
  if (
    column.property === "totalPlanDeliveryNumber" ||
    column.property === "totalDeliveryNumber" ||
    column.property === "undeliveredNumber"
  ) {
    console.log("row。orderNumber", row.orderNumber);
    nodeStore.setPurchaseOrderDetailLineNumber(row.orderNumber);
    router.push(
      "/full-lifecycle/procurementDelivery/procurementDeliveryDetail"
    );
  }
};

// 获取存储的tag
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = "采购订单明细表";
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("PurchaseColumnFields");
otherField.value[0].label = "采购订单号";
console.log("采购123", otherField.value);
</script>
