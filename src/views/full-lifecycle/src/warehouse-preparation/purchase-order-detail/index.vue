<script lang="ts" setup>
import Order from "./order/index.vue";
import WarehousingShelves from "./warehousingShelves/index.vue";
import IncomingInspection from "./incomingInspection/index.vue";
import ProcurementDelivery from "./procurementDelivery/index.vue";
import VueFlow from "@/components/ReVueFlow/warehouse-preparation/purchase-order-detail/index.vue";
import { ref } from "vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import OrderSelector from "@/views/full-lifecycle/components/OrderSelector.vue";
const { isVisible } = useVisibility();
defineOptions({
  name: "preparationPurchaseOrderDetail"
});
// 节点切换
const nodeName = ref("");
const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<template>
  <div class="relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <!-- 采购订单总体-采购订单 -->
    <Order v-if="nodeName === '采购订单'" />
    <!-- 采购订单总体-入库上架 -->
    <WarehousingShelves v-if="nodeName === '入库上架'" />
    <!-- 采购订单总体-来料检验 -->
    <IncomingInspection v-if="nodeName === '来料检验'" />
    <!-- 采购订单总体-采购送货 -->
    <ProcurementDelivery v-if="nodeName === '采购送货'" />
  </div>
</template>

<style lang="scss" scoped></style>
