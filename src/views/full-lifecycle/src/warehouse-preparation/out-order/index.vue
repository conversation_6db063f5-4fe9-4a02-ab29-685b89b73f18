<template>
  <div class="relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <IncomingInspection v-if="nodeName === '来料检验'" />
    <ProcurementDelivery v-if="nodeName === '采购送货'" />
    <OutsourcedShipment v-if="nodeName === '委外出库'" />
    <OutOrder v-if="nodeName == '委外订单'" />
    <!-- 采购订单总体-入库上架 -->
    <WarehousingShelves v-if="nodeName === '入库上架'" />
  </div>
</template>

<script setup lang="ts">
import VueFlow from "@/components/ReVueFlow/warehouse-preparation/out-order/index.vue";
import WarehousingShelves from "./warehousingShelves/index.vue";
import OutOrder from "./out-order/index.vue";
import { ref } from "vue";
import { useVisibility } from "@/utils/useVisibility";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import ProcurementDelivery from "./procurement-delivery/index.vue";
import OutsourcedShipment from "./outsourced-shipment/index.vue";
import IncomingInspection from "./incoming-inspection/index.vue";
import OrderSelector from "../../../components/OrderSelector.vue";

const { isVisible } = useVisibility();
defineOptions({
  name: "preparationOutOrder"
});

// 节点切换
const nodeName = ref("");
const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<style lang="scss" scoped></style>
