<template>
  <div class="relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <!-- 委外订单节点 -->
    <OutOrderDetail v-if="nodeName === '委外订单'" />
    <!-- 入库上架节点 -->
    <WarehousingShelves v-if="nodeName === '入库上架'" />
    <!-- 来料检验节点 -->
    <IncomingInspection v-if="nodeName === '来料检验'" />
    <!-- 采购送货节点 -->
    <ProcurementDelivery v-if="nodeName === '采购送货'" />
    <!-- 委外出库节点 -->
    <OutOfOfficeOutsourcing v-if="nodeName === '委外出库'" />
  </div>
</template>

<script setup lang="ts">
import VueFlow from "@/components/ReVueFlow/warehouse-preparation/out-order-detail/index.vue";
import { ref } from "vue";
import { useVisibility } from "@/utils/useVisibility";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import IncomingInspection from "./incoming-inspection/index.vue";
import OutOfOfficeOutsourcing from "./out-of-office-outsourcing/index.vue";
import OutOrderDetail from "./out-order-detail/index.vue";
import ProcurementDelivery from "./procurement-delivery/index.vue";
import WarehousingShelves from "./warehousing-shelves/index.vue";
import OrderSelector from "@/views/full-lifecycle/components/OrderSelector.vue";

const { isVisible } = useVisibility();
defineOptions({
  name: "outsourcingOrderDetail"
});

// 节点切换
const nodeName = ref("");
const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<style lang="scss" scoped></style>
