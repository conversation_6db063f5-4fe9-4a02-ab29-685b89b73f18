<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :mappingStatus="salesOrderStateMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'stockInQty',
        'remainStockInQty',
        'unstockedQualifiedQuantity',
        'totalInspectionQuantity',
        'inspectedQuantity',
        'pendingInspectionQuantity',
        'totalQualifiedQuantity',
        'totalUnqualifiedQuantity',
        'totalRejectedQuantity',
        'totalRefusalReentryQuantity',
        'totalPlanDeliveryNumber',
        'totalDeliveryNumber',
        'undeliveredNumber',
        'pickMtrlStatus'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleOutOrderDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DetailList from "@/components/ReTable/TableDetail.vue";
import {
  getOutOrderDetailAPI,
  getOutOrderDetailFieldAPI,
  getOutOrderDetailTotalAPI,
  exportOutOrderAPI
} from "@/api/full-lifecycle/warehouse-preparation/out-order-detail";
// 求和hook

/** 表格状态数据 */
import { salesOrderStateMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const routeTitle = ref("");
const route = useRoute();
const { getParentColumnTag, setParentColumnTag } = useParentColumnTag();
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "closeStatusCaption",
      sort: "asc"
    },
    {
      name: "inboundCompletionRate",
      sort: "asc"
    },
    {
      name: "inspectionCompletionRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleOutOrderDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getOutOrderDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getOutOrderDetailAPI,
  {
    states: salesOrderStateMap,
    addPercentSigns: [
      "inboundCompletionRate",
      "inspectionCompletionRate",
      "deliveryCompletionRate"
    ]
  },
  false,
  nodeStore.sonOutOrderDetail.billNo
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportOutOrderAPI, {
        queryId: nodeStore.sonOutOrderDetail.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "qty",
  "yieldQty",
  "purSelQty",
  "purQty",
  "stockInQty",
  "stockReadyQty",
  "baseStockReadyQty",
  "noStockInQty",
  "pickMtlQty",
  "totalInspectionQuantity",
  "qualifiedQuantity",
  "unqualifiedQuantity",
  "rejectedQuantity",
  "sumRefusalQuantity",
  "totalPlanDeliverydNumber",
  "totalDeliveryNumber",
  "inspectedQty",
  "uninspectedQty",
  "totalUndeliveryNumber",
  "qualifiedUninventedQuantity"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getOutOrderDetailTotalAPI, {
  queryId: nodeStore.sonOutOrderDetail.billNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  // const columnList = ["billNo"];
  // setParentColumnTag(row, columns.value, columnList, "PurchaseColumnFields");
  // nodeStore.setSonPurchaseOrderDetailFinalMaterialSourceNo(row.billNo);
  // router.push({
  //   path: "/full-lifecycle/out-order-detail"
  // });
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  nodeStore.setSonOutOrderDetailPurOrderNo(row.billNo);
  console.log(row, column, cell, event, "某单元格被点击");
  const columnList = ["billNo", "orderNumber"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "OutOrderColumnFields");
  // 跳转到《某个生产任务单号的车间入库明细数据》
  if (
    column.property === "stockInQty" ||
    column.property === "remainStockInQty" ||
    column.property === "unstockedQualifiedQuantity"
  ) {
    console.log("row。orderNumber", row.orderNumber);

    nodeStore.setSonOutOrderDetailMaterialNo(row.orderNumber);
    router.push(
      "/full-lifecycle/out-order-detail/warehousingShelves/warehousingShelvesDetail"
    );
  }
  if (
    column.property === "totalInspectionQuantity" ||
    column.property === "inspectedQuantity" ||
    column.property === "pendingInspectionQuantity" ||
    column.property === "totalQualifiedQuantity" ||
    column.property === "totalUnqualifiedQuantity" ||
    column.property === "totalRejectedQuantity" ||
    column.property === "totalRefusalReentryQuantity"
  ) {
    console.log("row。orderNumber", row.orderNumber);

    nodeStore.setSonOutOrderDetailMaterialNo(row.orderNumber);
    router.push(
      "/full-lifecycle/out-order-detail/incomingInspection/incomingInspectiondetail"
    );
  }
  if (
    column.property === "totalPlanDeliveryNumber" ||
    column.property === "totalDeliveryNumber" ||
    column.property === "undeliveredNumber"
  ) {
    console.log("row。orderNumber", row.orderNumber);
    nodeStore.setSonOutOrderDetailMaterialNo(row.orderNumber);
    router.push(
      "/full-lifecye/out-order/procurementDelivery/procurementDeliveryDetail"
    );
  }

  if (column.property === "pickMtrlStatus") {
    nodeStore.setSonOutOrderDetailMaterialNo(row.orderNumber);
    router.push(
      "/full-lifecycle/out-order-detail/out-of-office-outsourcing/out-of-office-outsourcingDetail"
    );
  }
};

// 获取tag
onMounted(() => {
  // console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  // console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("outOrderDetailColumnFields");
// otherField.value[0].label = "采购订单号";
</script>
