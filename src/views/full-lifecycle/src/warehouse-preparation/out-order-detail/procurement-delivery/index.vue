<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :mappingStatus="outorderProcurementDeliveryDetailMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleOutOrderId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DetailList from "@/components/ReTable/TableDetail.vue";

import {
  getProcurementDeliveryDetailTotalAPI,
  getProcurementDeliveryDetailTableAPI,
  getProcurementDeliveryDetailFieldAPI,
  exportIncomingInspectionAPI
} from "@/api/full-lifecycle/warehouse-preparation/out-order-detail/procurement-delivery-detail";
// 求和hook

/** 表格状态数据 */
import { outorderProcurementDeliveryDetailMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const { getParentColumnTag } = useParentColumnTag();
const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const routeTitle = ref("");
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "closeStatusCaption",
      sort: "asc"
    },
    {
      name: "inboundCompletionRate",
      sort: "asc"
    },
    {
      name: "inspectionCompletionRate",
      sort: "asc"
    }
  ]
});

const route = useRoute();
/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleOutOrderId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getProcurementDeliveryDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getProcurementDeliveryDetailTableAPI,
  {
    states: outorderProcurementDeliveryDetailMap,
    addPercentSigns: [
      "inboundCompletionRate",
      "inspectionCompletionRate",
      "deliveryCompletionRate"
    ]
  },
  false,
  { queryId: nodeStore.sonOutOrderDetail.billNo }
);
console.log("route.query.billNo", route.query.billNo);

const tableRowWarnStatus = reactive([
  {
    columnKey: "inboundCompletionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%",
    relation: "and",
    children: [
      {
        columnKey: "closeStatusCaption",
        operator: "==",
        threshold: "未关闭",
        className: "row-pending",
        sift: "and"
      }
    ]
  },
  {}
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportIncomingInspectionAPI, {
        queryId: nodeStore.sonOutOrderDetail.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "surplusDeliveryNumber",
  "planDeliverydNumber",
  "batchNumber",
  "deliveryNumber",
  "unInspectQuantity"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getProcurementDeliveryDetailTotalAPI, {
  queryId: nodeStore.sonOutOrderDetail.billNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  const columnList = ["billNo"];
  nodeStore.setSonPurchaseOrderDetailFinalMaterialSourceNo(row.billNo);
  router.push({
    path: "/full-lifecycle/out-order-detail"
  });
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
// 获取tag
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("outOrderDetailColumnFields");
</script>
