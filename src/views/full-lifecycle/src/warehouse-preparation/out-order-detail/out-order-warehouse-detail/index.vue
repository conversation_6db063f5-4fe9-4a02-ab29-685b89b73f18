<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :mappingStatus="outsourcedShipmentDetailMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'stockInQty',
        'remainStockInQty',
        'unstockedQualifiedQuantity',
        'totalInspectionQuantity',
        'inspectedQuantity',
        'pendingInspectionQuantity',
        'totalQualifiedQuantity',
        'totalUnqualifiedQuantity',
        'totalRejectedQuantity',
        'totalRefusalReentryQuantity',
        'totalPlanDeliveryNumber',
        'totalDeliveryNumber',
        'undeliveredNumber',
        'pickMtrlStatus'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
defineOptions({ name: "OutsourcingOrderWarehouseDetails" });
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleOutOrderDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DetailList from "@/components/ReTable/TableDetail.vue";
import {
  getOutOrderDetailAPI,
  getOutOrderDetailFieldAPI,
  getOutOrderDetailTotalAPI,
  exportOutOrderAPI,
  getOutsourcingDetailAPI,
  getWarehouseSumPutAwayAPI,
  getOutsourcingDetailTotalAPI,
  getOutsourcingDetailFieldAPI
} from "@/api/full-lifecycle/warehouse-preparation/out-order-detail";
// 求和hook

/** 表格状态数据 */
import { outsourcedShipmentDetailMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const routeTitle = ref("");
const route = useRoute();
const { getParentColumnTag, setParentColumnTag } = useParentColumnTag();
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "closeStatusCaption",
      sort: "asc"
    },
    {
      name: "inboundCompletionRate",
      sort: "asc"
    },
    {
      name: "inspectionCompletionRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleOutOrderDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getOutsourcingDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getOutsourcingDetailAPI,
  {
    states: outsourcedShipmentDetailMap,
    addPercentSigns: [
      "inboundCompletionRate",
      "inspectionCompletionRate",
      "deliveryCompletionRate"
    ]
  },
  false,
  nodeStore.outsourcingWorkOrderCode
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportOutOrderAPI, {
        queryId: nodeStore.outsourcingWorkOrderCode
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "pickedQty",
  "mustQty",
  "planOutStockQuantity",
  "outStockQuantity"
  // "singleMachineQuantity",
  // "executeFeedingNumber",
  // "planFeedingNumber"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getWarehouseSumPutAwayAPI, {
  queryId: nodeStore.outsourcingWorkOrderCode
  // queryId: route.query.productionOrderNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 获取tag
onMounted(() => {
  // console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  // console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("outsourcingWorkOrderCode");
// otherField.value[0].label = "采购订单号";
</script>
