<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :mappingStatus="salesOrderStateMap"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleWarehousingShelvesDetailsId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import DetailList from "@/components/ReTable/TableDetail.vue";

import {
  getWarehousingShelvesAPI,
  getWarehousingShelvesFieldAPI,
  getWarehousingShelvesAPITotalAPI,
  exportWarehousingShelvesAPI
} from "@/api/full-lifecycle/warehouse-preparation/out-order-detail/warehousingShelves";
/** 表格状态数据 */
import { purchaseOrderWarehousingShelvesMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useParentColumnTag } from "@/utils/hooks";
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const historyColumnsKeys = ref([]);
defineOptions({
  name: "PurchaseDetailsShelving"
});
const { getParentColumnTag } = useParentColumnTag();
const routeTitle = ref("");

const route = useRoute();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [
    {
      name: "orderNumber", //送货
      query: nodeStore.sonOutOrderDetail.purOrderEntrySeq,
      logic: "and",
      condition: "eq"
    }
  ],
  order: [
    {
      name: "date",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleWarehousingShelvesDetailsId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWarehousingShelvesFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getWarehousingShelvesAPI,
  {
    states: purchaseOrderWarehousingShelvesMap
  },
  false,
  nodeStore.sonOutOrderDetail.billNo
);

// 高亮哪个状态情况下的列
// 表示某列字段的columnKey的键值等于 status 的值时，该列的单元格会应用 className 指定的样式类名
// greater（大于）、less（小于）、amount（等于）三种状态  还有做————————正在构思
const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportWarehousingShelvesAPI, {
        queryId: nodeStore.sonOutOrderDetail.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref(["executeQuantity"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getWarehousingShelvesAPITotalAPI, {
  queryId: nodeStore.sonOutOrderDetail.billNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 获取tag
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", "委外入库明细");
});
const otherField = getParentColumnTag("OutOrderColumnFields");
// otherField.value[0].label = "采购订单号";
</script>
