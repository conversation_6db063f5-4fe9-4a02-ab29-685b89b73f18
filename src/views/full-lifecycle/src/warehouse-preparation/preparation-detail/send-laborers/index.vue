<template>
  <div>
    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :getSummaries="getSummaries"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleSalesOrdersId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import {
  getSaleOrderSequenceDetail,
  getSaleOrderDetailKeys,
  getSaleOrderSequence,
  getSaleOrder,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/saleOrder";

defineOptions({
  name: "SendLaborers"
});
const route = useRoute();
const tableRef = ref(null);
const selectRows = ref([]);
// 定义表格配置
const columns = ref([]);
const columnsKeys = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 10, default: 0 },
  query: [
    {
      name: "billNo",
      query: route.query.billNo,
      logic: "",
      condition: "eq"
    }
  ]
});

//获取销售订单表头字段排序信息
getSaleOrderSequence(fullLifecycleSalesOrdersId).then(async res => {
  columnsKeys.value = await res.data;
  if (res.code != 200) {
    const fieldRes = await getSaleOrderDetailKeys();
    // console.log("获取后端定义的字段:", fieldRes.data);
    let columnsDefault = await mapFields(fieldRes.data);
    columns.value = await columnsDefault;
    // console.log("前端处理映射后11:", columns.value);
  } else {
    // // 执行初始化
    await initializeColumns().then(updatedColumns => {
      if (columnsKeys.value.length > 0) {
        // console.log(updatedColumns, "更新后的列");

        // return (columns.value = columnsKeys.value);
        return (columns.value = updatedColumns);
      }
      columns.value = updatedColumns;
      // console.log("Final Columns:", updatedColumns);
    });
  }
});

// 列设置更新
const updateColumns = async columns_ => {
  columns.value = columns_;
  // console.log("列设置更新:", columns);
  updateSaleOrderSequence({
    tableId: fullLifecycleSalesOrdersId,
    tableColumns: columns_
  });
};

// 更新字段信息的函数
async function initializeColumns() {
  try {
    // 获取销售订单字段信息并映射
    const fieldRes = await getSaleOrderDetailKeys();
    // console.log("获取后端定义的字段:", fieldRes.data);

    let columns = await mapFields(fieldRes.data);
    // console.log("前端处理映射后:", columns);

    // 确保 columnsKeys.value 是有序的并包含所有需要的字段
    const updatedColumns = columnsKeys.value
      .map(sortedCol => {
        const column = columns.find(col => col.prop === sortedCol.prop);
        if (column) {
          return { ...column, ...sortedCol };
        }
        return null;
      })
      .filter(Boolean); // 过滤掉 null 值

    // console.log("列设置字段信息:", columnsKeys.value);

    return updatedColumns;
  } catch (error) {
    console.error("Error initializing columns:", error);
  }
}

const tableData = ref([]);

// 高亮哪个状态情况下的列
// 表示某列字段的columnKey的键值等于 status 的值时，该列的单元格会应用 className 指定的样式类名
// greater（大于）、less（小于）、amount（等于）三种状态  还有做————————正在构思
const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel();
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 判断用户是勾选导出还是全部导出
const handleExportExcel = () => {
  exportToExcel(columns.value, selectRows.value, "表格数据");
};

const handleClickButton = row => {
  console.log(row);
};

// 外部定义的 fetchTableData 函数
const fetchTableData = async (currentPage, pageSize) => {
  tableRef.value?.loading(true);
  try {
    const datas = {
      page: { current: currentPage, size: pageSize },
      query: [
        {
          name: "billNo",
          query: route.query.billNo, //后期加一个默认值渲染
          logic: "",
          condition: "eq"
        }
      ]
    };
    const response = await getSaleOrderSequenceDetail(queryData.value);
    // console.log("销售列表节点", response.data.records);
    tableData.value = response.data.records;
    return {
      records: response.data.records,
      total: response.data.total,
      code: 200
    };
  } catch (error) {
    console.error("Error fetching table data:", error);
    return { records: [], total: 0, code: 500 };
  }
};

// 外部定义的 getSummaries 函数
const getSummaries = param => {
  const { columns, data } = param;
  const sums = [];
  sums[0] = h("div", { style: { fontWeight: "bold", fontSize: "0.875rem" } }, [
    "总计"
  ]); // 在最左边添加一个“总计”标签
  columns.forEach((column, index) => {
    if (index === 0) {
      return;
    }

    if (column.property === "closeStatus") {
      // 统计 closeStatus 状态为 "关闭" 和 "正常" 的数量
      const closeStatusCount = data.reduce(
        (count, row) => {
          if (row.closeStatus === "已关闭") {
            count.closed += 1;
          } else if (row.closeStatus === "正常") {
            count.normal += 1;
          }
          return count;
        },
        { closed: 0, normal: 0 }
      );
      sums[index] =
        `关闭: ${closeStatusCount.closed}, 正常: ${closeStatusCount.normal}`;
    } else if (column.property === "status") {
      const statusCount = data.reduce((count, row) => {
        count[row.status] = (count[row.status] || 0) + 1;
        return count;
      }, {});
      sums[index] = Object.entries(statusCount)
        .map(([status, count]) => `${status}: ${count}`)
        .join(", "); // 统计状态的数量
    } else {
      sums[index] = ""; // 其他列不做统计
    }
  });
  return sums;
}; // 返回汇总结果

// 勾选的数据
const handleSelectedRows = rows => {
  selectRows.value = rows;
};

// 筛选条件
const InputSearchUpdateFilter = filter => {
  queryData.value.query = filter;
};

// 重置筛选条件后分页重置
const InputSearchResetFilter = () => {
  // 搜素内容
  queryData.value = {
    page: { current: 1, size: 10, default: 0 },
    query: [
      {
        name: "billNo",
        query: route.query.billNo,
        logic: "",
        condition: "eq"
      }
    ]
  };
  // console.log("筛选重置");
};

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 封装后端字段映射关系
async function mapFields(data) {
  return await data.map(item => ({
    prop: item.field,
    label: item.description,
    visible: true,
    width: 200,
    fixed: false,
    type: item.type
  }));
}

// 调用分页选择
const handleCurrentPageChange = val => {
  queryData.value.page.current = val;
  // console.log(`${val} items per page`);
};
const handlePageSizeChange = val => {
  queryData.value.page.size = val;
  // console.log(`${val} items per size`);
};
</script>
