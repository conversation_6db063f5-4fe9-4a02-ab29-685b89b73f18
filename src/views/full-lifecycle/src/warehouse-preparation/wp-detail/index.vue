<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />

    <ReTable
      ref="tableRef"
      :mappingStatus="warehousePreparationDetail"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { WarehouseMaterialPreparationDetail } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getWPDetailAPI,
  getWPDetailFieldAPI,
  getWPDetailAPITotalAPI
} from "@/api/full-lifecycle/warehouse-preparation/wp-detail";
import DetailList from "@/components/ReTable/TableDetail.vue";

// 求和hook
import { useColumnsTotal, useParentColumnTag } from "@/utils/hooks";

/** 表格状态数据 */
import { warehousePreparationDetail } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";

const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();
const route = useRoute();
const router = useRouter();
defineOptions({
  name: "WarehousePreparationDetail"
});
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);

const routeTitle = ref("");

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "issuanceStatus",
      sort: "desc"
    }
  ]
});
/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  WarehouseMaterialPreparationDetail,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWPDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getWPDetailAPI,
  {
    states: warehousePreparationDetail
  },
  false,
  { queryId: nodeStore.sonPurchaseOrder.productionOrderNo }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel();
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);
// 需要求和字段数组
const sumList = ref(["single"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getWPDetailAPITotalAPI, {
  queryId: nodeStore.sonPurchaseOrder.productionOrderNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  /** 计划发料单明细 */
  const materialIssue = [
    "createdPlannedIssuanceQuantity",
    "uncreatedPlannedIssuanceQuantity"
  ];
  /** 拣选单 */
  const pickList = [
    "createdPickingOrderQuantity",
    "uncreatedPickingOrderQuantity"
  ];

  if (materialIssue.includes(column.property)) {
    // setParentColumnTag(row, columns.value, columnList);
    nodeStore.setWarehousePreparationPlanProductionOrderNoMaterialCode(
      row.productionOrderNo,
      row.materialCode
    );
    const columnList = ["productionOrderNo", "materialCode"];
    setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
    router.push({
      path: "/full-lifecycle/plan-material-order-item-detail"
      // query: {
      //   billNo: row.productionOrderNo,
      //   materialCode: row.materialCode
      // }
    });
  } else if (pickList.includes(column.property)) {
    // setParentColumnTag(row, columns.value, columnList);
    nodeStore.setWarehousePreparationSelectProductionOrderNoMaterialCode(
      row.productionOrderNo,
      row.materialCode
    );
    const columnList = ["productionOrderNo", "materialCode"];
    setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
    router.push({
      path: "/full-lifecycle/order-picking-sheet-item-detail"
      // query: {
      //   billNo: row.productionOrderNo,
      //   materialCode: row.materialCode
      // }
    });
  } else if (
    /** 采购子流程明细 */
    column.property == "sourceType" ||
    column.property == "finalMaterialSourceNo"
  ) {
    if (row.sourceType === "采购订单") {
      nodeStore.setSonPurchaseOrderDetailFinalMaterialSourceNo(
        row.finalMaterialSourceNo
      );
      const columnList = ["finalMaterialSourceNo"];
      setParentColumnTag(
        row,
        columns.value,
        columnList,
        "filteredColumnFields"
      );
      console.log("跳转--------------------");
      setParentColumnTag(
        row,
        columns.value,
        columnList,
        "PurchaseColumnFields"
      );

      router.push({
        path: "/full-lifecycle/preparation/purchase-order-detail"
      });
    } else if (row.sourceType === "委外订单") {
      const columnList = ["id", "productionOrderNo"];
      setParentColumnTag(
        row,
        columns.value,
        columnList,
        "filteredColumnFields"
      );
      router.push({
        path: "/full-lifecycle/warehouse-preparation/out-order"
      });
    } else if (row.sourceType === "生产订单") {
      const columnList = ["finalMaterialSourceNo"];
      setParentColumnTag(
        row,
        columns.value,
        columnList,
        "filteredColumnFields"
      );
      router.push({
        path: "/full-lifecycle/monad-dispatch-situation",
        query: {
          billNo: row.finalMaterialSourceNo
        }
      });
    }
  }

  // 跳转到《某个生产任务单号的车间生产明细数据》
};

// 获取
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("DetailColumnFields");
</script>
