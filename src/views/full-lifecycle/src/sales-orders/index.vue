<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="salesOrderStateMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>
<!--      :tableRowWarnStatus="tableRowWarnStatus"-->
<script lang="tsx" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleSalesOrdersId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getSaleOrderSequenceDetail,
  getSaleOrderDetailKeys,
  getSaleOrderTotalAPI,
  exportSaleOrderDetailAPI
} from "@/api/full-lifecycle/saleOrder";
// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法
/** 表格状态数据 */
import { salesOrderStateMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
defineOptions({
  name: "SalesOrders"
});

const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "completionRate",
      sort: "asc"
    }
  ]
});
// 定义表格配置

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleSalesOrdersId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSaleOrderDetailKeys, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getSaleOrderSequenceDetail, {
  states: salesOrderStateMap,
  addPercentSigns: ["completionRate"]
});

// 当销售订单的进度小于1的
const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%",
    relation: "and",
    children: [
      {
        columnKey: "salesOrderState",
        operator: "!=",
        threshold: "完结",
        className: "row-pending",
        sift: "and"
      }
    ]
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSaleOrderDetailAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "actualProductionQuantity",
  "noTaxSaleAmount",
  "noTaxUnitPrice",
  "outStockBoatQuantity",
  "outWarehouseNumber",
  "saleAmount",
  "salesOrderNumber",
  "salesOrderUnitPrice",
  "saleTotal",
  "taxUnitPrice",
  "undeliveredQuantity",
  "totalOutBoundNumber",
  "totalInboundNumber",
  "inboundNotOutboundNumber"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getSaleOrderTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>

<style>
.row-pending {
  background-color: #fdf6ec; /* 淡黄色 */
}

.row-gray {
  background-color: #f4f4f5; /* 淡黄色 */
}

.row-processing {
  background-color: #fdf6ec; /* 淡黄色 */
}

.row-red {
  background-color: #fef0f0;
}
</style>
