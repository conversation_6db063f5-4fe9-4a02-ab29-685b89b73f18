<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="planOrderMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive } from "vue";
import { fullLifecyclePlanId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/saleOrder";

import {
  getPlainOrderTotalAPI,
  getPlanOrdersList,
  getPlanOrdersTableList,
  exportPlanOrdersAPI
} from "@/api/full-lifecycle/programOrders";

// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";
import { useFetchTableData } from "@/utils/hooks";
import { planOrderMap } from "@/views/full-lifecycle/utils";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法
defineOptions({
  name: "ProgramOrders"
});
const nodeStore = useNodeStore();
const route = useRoute();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

const historyColumnsKeys = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [
    {
      name: "tylhText",
      query: nodeStore.queryId,
      logic: "and",
      condition: "eq"
    }
  ],
  order: [
    {
      name: "releaseStatus",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecyclePlanId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getPlanOrdersTableList, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getPlanOrdersList, {
  states: planOrderMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportPlanOrdersAPI, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref(["firmQty", "demandQty", "orderQty", "sugQty"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getPlainOrderTotalAPI, {
  // queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
