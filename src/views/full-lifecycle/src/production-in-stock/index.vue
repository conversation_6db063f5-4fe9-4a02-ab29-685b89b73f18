<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="productionStockMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive } from "vue";
import { fullLifecycleProductionStockId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getProductionStockTotalAPI,
  getProductionStockListAPI,
  getProductionStockFieldsAPI,
  exportProductionStockAPI
} from "@/api/full-lifecycle/production-stock/productionStock";

// 求和hook
import { useFetchTableData } from "@/utils/hooks";
import { useI18n } from "vue-i18n";
import { useNodeStore } from "@/store/modules/fullLifecycle";
// 状态枚举
import { productionStockMap } from "@/views/full-lifecycle/utils";
const { t } = useI18n(); // 解构出t方法
defineOptions({
  name: "ProductionInStock"
});
const nodeStore = useNodeStore();
const route = useRoute();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "creationTime",
      sort: "desc"
    }
  ]
});
/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleProductionStockId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getProductionStockFieldsAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getProductionStockListAPI, {
  states: productionStockMap
});

// 高亮哪个状态情况下的列
// 表示某列字段的columnKey的键值等于 status 的值时，该列的单元格会应用 className 指定的样式类名
// greater（大于）、less（小于）、amount（等于）三种状态  还有做————————正在构思
const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportProductionStockAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref(["quantity"]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getProductionStockTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
