<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="productionOrderMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'unInventoryOrderNumber',
        'inventoryOrderNumber',
        'endNumber',
        'outstandingQuantity',
        'totalWorkReportQuantity',
        'unreportedQuantity',
        'totalInspectionQuantity',
        'issuanceStatus',
        'inspectedQuantity',
        'pendingInspectionQuantity',
        'totalQualifiedQuantity',
        'totalUnqualifiedQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive } from "vue";
import { fullLifecycleProductionOrderId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getProductionOrderListAPI,
  getProductionOrderTotalAPI,
  getProductionOrderFieldsAPI,
  exportProductionOrderAPI
} from "@/api/full-lifecycle/productionOrder";
import { useParentColumnTag } from "@/utils/hooks";
import { useI18n } from "vue-i18n";

// 求和hook
import { useFetchTableData } from "@/utils/hooks";
// 状态枚举
import { productionOrderMap } from "@/views/full-lifecycle/utils";
import { useNodeStore } from "@/store/modules/fullLifecycle";
defineOptions({
  name: "ProductionOrder"
});
const { t } = useI18n(); // 解构出t方法
const nodeStore = useNodeStore();
const route = useRoute();
const router = useRouter();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

const { setParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [
    // {
    //   name: "billNo",
    //   query: route.query.billNo,
    //   logic: "and",
    //   condition: "eq"
    // }
  ],
  order: [
    // {
    //   name: "completionRate",
    //   sort: "asc"
    // }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleProductionOrderId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getProductionOrderFieldsAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getProductionOrderListAPI, {
  states: productionOrderMap,
  addPercentSigns: ["warehousingRate"]
});

// 当销售订单的进度小于1的
const tableRowWarnStatus = reactive([
  {
    columnKey: "completionColor",
    operator: "==",
    threshold: 1,
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportProductionOrderAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "productionOrderNumber",
  "endNumber",
  "outstandingQuantity",
  "inventoryOrderNumber",
  "unInventoryOrderNumber",
  "issueDecreasing",
  "totalInspectionQuantity",
  "inspectedQuantity",
  "pendingInspectionQuantity",
  "totalQualifiedQuantity",
  "totalUnqualifiedQuantity",
  "totalWorkReportQuantity",
  "unreportedQuantity"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getProductionOrderTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  // 跳转到《某个生产任务单号的车间入库明细数据》
  if (
    column.property === "inventoryOrderNumber" ||
    column.property === "unInventoryOrderNumber"
    // 以下注释是返回字段没有的
    // column.property === "完工未入库数量"
  ) {
    router.push({
      path: "/full-lifecycle/production-in-stock/production-in-stock-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《某个生产任务单号的车间生产明细数据》
  if (
    column.property === "endNumber" ||
    column.property === "outstandingQuantity" ||
    column.property === "totalWorkReportQuantity" ||
    column.property === "unreportedQuantity"
  ) {
    router.push({
      path: "/full-lifecycle/workshop-production/workshop-production-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《某个生产任务单号的完工报检明细数据》
  if (
    column.property === "totalInspectionQuantity" ||
    column.property === "inspectedQuantity" ||
    column.property === "pendingInspectionQuantity" ||
    column.property === "totalQualifiedQuantity" ||
    column.property === "totalUnqualifiedQuantity"
  ) {
    router.push({
      path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《仓库备料明细数据》
  if (column.property === "issuanceStatus") {
    router.push({
      path: "/full-lifecycle/warehouse-preparation/detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  // 筛选字段列表
  const columnList = ["productionOrderNo"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  // 跳转到《某个生产任务单号的车间入库明细数据》
  if (
    column.property === "inventoryOrderNumber" ||
    column.property === "unInventoryOrderNumber"
    // 以下注释是返回字段没有的
    // column.property === "完工未入库数量"
  ) {
    router.push({
      path: "/full-lifecycle/production-in-stock/production-in-stock-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《某个生产任务单号的车间生产明细数据》
  if (
    column.property === "endNumber" ||
    column.property === "outstandingQuantity" ||
    column.property === "totalWorkReportQuantity" ||
    column.property === "unreportedQuantity"
  ) {
    router.push({
      path: "/full-lifecycle/workshop-production/workshop-production-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《某个生产任务单号的完工报检明细数据》
  if (
    column.property === "totalInspectionQuantity" ||
    column.property === "inspectedQuantity" ||
    column.property === "pendingInspectionQuantity" ||
    column.property === "totalQualifiedQuantity" ||
    column.property === "totalUnqualifiedQuantity"
  ) {
    router.push({
      path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  // 跳转到《仓库备料明细数据》
  if (column.property === "issuanceStatus") {
    setParentColumnTag(row, columns.value, columnList, "DetailColumnFields");
    nodeStore.setSonPurchaseOrderProductionOrderNo(row.productionOrderNo);
    router.push({
      path: "/full-lifecycle/warehouse-preparation/detail"
    });
  }
};
</script>
