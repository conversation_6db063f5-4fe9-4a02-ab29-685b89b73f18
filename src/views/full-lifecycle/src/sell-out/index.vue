<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="sellOutMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive } from "vue";
import { fullLifecycleSellOutId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";

import {
  getSellOutTotalAPI,
  getSellOutList,
  getSellOutTableList,
  exportSellOutAPI
} from "@/api/full-lifecycle/sellOut";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法
import { useFetchTableData } from "@/utils/hooks";
// 状态枚举
import { sellOutMap } from "@/views/full-lifecycle/utils";
import { useNodeStore } from "@/store/modules/fullLifecycle";
defineOptions({
  name: "SalesOrders"
});
const nodeStore = useNodeStore();
const route = useRoute();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "completionRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleSellOutId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSellOutTableList, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getSellOutList, {
  states: sellOutMap,
  addPercentSigns: ["completionRate"]
});

// 当销售订单的进度小于1的
const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSellOutAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "planOutBoundNumber",
  "outBoundNumber",
  "inventoryQuantity",
  "actualQuantityReceived",
  "batchNum",
  "changePickQuantity",
  "inThePurchasePreStockPlanQuantity",
  "notOutBoundNumber",
  "outStockAmount",
  "pickQuantity",
  "saleOutAmount",
  "salesOrderNumber",
  "totalOutBoundNumber",
  "totalInboundNumber",
  "inboundNotOutboundNumber"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getSellOutTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
