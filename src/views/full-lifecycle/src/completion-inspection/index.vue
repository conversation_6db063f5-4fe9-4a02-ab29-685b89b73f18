<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="completionInspectionMap"
      showTotal
      :getTotal="getTotal"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted, nextTick } from "vue";
import { fullLifecycleInspectionCompletionsId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getInspectionCompletionAPI,
  getInspectionCompletionTableAPI,
  getInspectionCompletionsTotalAPI,
  exportInspectionCompletionAPI
} from "@/api/full-lifecycle/completion-Inspection/completionInspection";
// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";
// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { completionInspectionMap } from "@/views/full-lifecycle/utils";
import { useParentColumnTag } from "@/utils/hooks";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法

defineOptions({
  name: "CompletionInspection"
});
const nodeStore = useNodeStore();
const router = useRouter();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();

// 定义表格配置

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "status",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleInspectionCompletionsId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getInspectionCompletionTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getInspectionCompletionAPI, {
  states: completionInspectionMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "status",
    operator: "==",
    threshold: "未检",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportInspectionCompletionAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "endNumber",
  "inspectionDeclarationNumber",
  "qualifiedNumbers",
  "unfinishedQuantity",
  // "status",
  "unQualifiedNumber",
  "scrapNumber"
]);

// 获取所有总计信息
const getTotal = async () => {
  // 调用hook(求和字段数组，请求，查询参数)
  const list = useColumnsTotal(
    sumList.value,
    getInspectionCompletionsTotalAPI,
    [...queryData.value.query, ...tableQuery.value],
    nodeStore.queryId
  );
  return list;
};

// 某行被点击
const handleRowClick = (row, column, cell) => {
  const columnList = ["productionOrderNo"];
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  console.log(row, column, cell, "某行被点击");
  router.push({
    path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
    query: {
      billNo: row.productionOrderNo
    }
  });
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
