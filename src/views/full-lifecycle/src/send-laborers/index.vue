<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="sendLaborerMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'progress',
        'plannedIssuanceOrderCreateProgress',
        'pickOrderCreateProgress'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
defineOptions({
  name: "路由bug"
});
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleDispatchWorkerId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import { useCheckBillNoCache } from "@/utils/hooks";

import {
  getSendLaborersTotalAPI,
  addSendLaborersAPI,
  getDispatchWorkersTableList,
  exportDispatchWorkersAPI
} from "@/api/full-lifecycle/sendLaborers";

/** 表格状态数据 */
import { sendLaborerMap, GetEnumArray } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useParentColumnTag } from "@/utils/hooks";
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法

const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);

// 定义表格配置

const historyColumnsKeys = ref([]);

const { setParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: []
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleDispatchWorkerId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getDispatchWorkersTableList, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(addSendLaborersAPI, {
  states: sendLaborerMap,
  addPercentSigns: [
    "warehousingRate",
    "progress",
    "plannedIssuanceOrderCreateProgress",
    "pickOrderCreateProgress"
  ]
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "isDone",
    operator: "==",
    threshold: "否",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportDispatchWorkersAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "productionOrderNumber",
  "endNumber",
  "outstandingQuantity",
  "inventoryOrderNumber",
  "unInventoryOrderNumber",
  "issueDecreasing"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getSendLaborersTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  if (column.property === "progress") {
    router.push({
      path: "/full-lifecycle/monad-dispatch-situation",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  if (column.property === "plannedIssuanceOrderCreateProgress") {
    console.log("row.productionOrderNo", row.productionOrderNo);

    nodeStore.setSonPurchaseOrderProductionOrderNo(row.productionOrderNo);
    router.push({
      path: "/full-lifecycle/warehouse-preparation/detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
  if (column.property === "pickOrderCreateProgress") {
    console.log("row.productionOrderNo", row.productionOrderNo);

    nodeStore.setSonPurchaseOrderProductionOrderNo(row.productionOrderNo);
    router.push({
      path: "/full-lifecycle/warehouse-preparation/detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  // 筛选字段列表
  const columnList = ["productionOrderNo"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  if (column.property === "progress") {
    router.push({
      path: "/full-lifecycle/monad-dispatch-situation",
      query: {
        billNo: row.productionOrderNo
      }
    });
  } else if (
    column.property === "plannedIssuanceOrderCreateProgress" ||
    column.property === "pickOrderCreateProgress"
  ) {
    setParentColumnTag(row, columns.value, columnList, "DetailColumnFields");
    nodeStore.setSonPurchaseOrderProductionOrderNo(row.productionOrderNo);
    router.push({
      path: "/full-lifecycle/warehouse-preparation/detail",
      query: {
        billNo: row.productionOrderNo
      }
    });
  }
};
</script>
