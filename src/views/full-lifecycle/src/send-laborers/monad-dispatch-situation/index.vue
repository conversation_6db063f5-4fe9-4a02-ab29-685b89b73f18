<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />

    <ReTable
      ref="tableRef"
      :mappingStatus="sendLaborerDetailMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import DetailList from "@/components/ReTable/TableDetail.vue";
import ButtonGroup from "@/components/ReTable/ButtonGroup.vue";
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleDispatchWorkersDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import { sendLaborerDetailMap } from "@/views/full-lifecycle/utils";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getSendLaborersDetailTotalAPI,
  getSendLaborersDetailAPI,
  getSendLaborersDetailTableAPI,
  exportSendLaborersDetailAPI
} from "@/api/full-lifecycle/sendLaborers/sendLaborersDetail";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";
// 求和hook
import { useColumnsTotal } from "../../../../../utils/hooks/tableDataHooks/useColumnsTotal";

/** 表格状态数据 */
import { orderPickingSheetMap, GetEnumArray } from "../../../utils";
// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useRouter } from "vue-router";
import { useParentColumnTag } from "@/utils/hooks";
defineOptions({
  name: "MonadDispatchSituation"
});
const route = useRoute();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

const historyColumnsKeys = ref([]);

const { getParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "completionRate",
      sort: "asc"
    },
    {
      name: "workingProcedureCode",
      sort: "asc"
    }
  ]
});
// 获取路由对象
const router = useRouter();

const routeTitle = ref("");

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleDispatchWorkersDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSendLaborersDetailTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSendLaborersDetailAPI,
  {
    states: sendLaborerDetailMap
  },
  false,
  { queryId: route.query.billNo }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSendLaborersDetailAPI, {
        queryId: route.query.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "plannedCompletionNumber",
  "completionNumber",
  "unqualifiedQuantitys",
  "ableSendWorkersNumbers",
  "outstandingQuantity",
  "scrapNumbers",
  "unInspectionQuantity"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getSendLaborersDetailTotalAPI, {
  queryId: route.query.billNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  nodeStore.setQueryId(row.pickingOrderCode);
  console.log("row.pickingOrderCode---------", row.pickingOrderCode);
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});

const otherField = getParentColumnTag("filteredColumnFields");
console.log("otherField.value仓库备料", otherField.value);
otherField.value[0].label = "生产任务单号";
</script>
