<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />

    <ReTable
      ref="tableRef"
      :mappingStatus="PlanMaterialOrderMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecyclePlanMaterialOrderDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import DetailList from "@/components/ReTable/TableDetail.vue";
import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getPlanMaterialOrderTotalDetailAPI,
  getPlanMaterialOrderDetailAPI,
  getPlanMaterialOrderTableDetailAPI,
  exportPlanMaterialOrderTableDetailAPI
} from "@/api/full-lifecycle/sendLaborers/planMaterialOrder";
import {
  getSomePlanMaterialOrderTotalAPI,
  getSomePlanMaterialOrderDetailAPI,
  getSomePlanMaterialOrderTableAPI
} from "@/api/full-lifecycle/sendLaborers/planMaterialOrderDetail";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 求和hook
import { useColumnsTotal } from "../../../../../../utils/hooks/tableDataHooks/useColumnsTotal";

/** 表格状态数据 */
import { PlanMaterialOrderMap, GetEnumArray } from "../../../../utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useRouter } from "vue-router";
import { useParentColumnTag } from "@/utils/hooks";
defineOptions({
  name: "PlanMaterialOrderDetail"
});
const route = useRoute();
// 获取路由对象
const router = useRouter();

const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const orderData = ref([]);
const historyColumnsKeys = ref([]);
const { getParentColumnTag } = useParentColumnTag();

const routeTitle = ref("");
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "date",
      sort: "asc"
    }
  ]
});
/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecyclePlanMaterialOrderDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getPlanMaterialOrderTableDetailAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPlanMaterialOrderDetailAPI,
  {
    states: PlanMaterialOrderMap
  },
  false,
  { queryId: nodeStore.sonPlanMaterialOrder.workOrdersNumber }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportPlanMaterialOrderTableDetailAPI, {
        queryId: nodeStore.sonPlanMaterialOrder.workOrdersNumber
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);
// 需要求和字段数组
const sumList = ref([
  "realQuantity",
  "realQuantity",
  "demandTotoalNumber",
  "stockQuantity",
  "unPerformQuantity",
  "amount",
  "price",
  "totalRealQuantity"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getPlanMaterialOrderTotalDetailAPI, {
  queryId: nodeStore.sonPlanMaterialOrder.workOrdersNumber
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");

  console.log("row.pickingOrderCode---------", row.pickingOrderCode);
  // router.push({
  //   path: "/full-lifecycle/send-laborers-son/order-picking-sheet/order-picking-sheet-detail",
  //   query: {
  //     billNo: row.billNo
  //   }
  // });
  // nodeStore.setSonPlanMaterialOrderWorkOrdersNumber(row.workOrdersNumber);
  if (column.property === "workOrdersNumber") {
    // router.push("/full-lifecycle/order-picking-sheet-item-detail");
    // router.push({
    //   path: "/full-lifecycle/plan-material-order-item-detail"
    //   // query: {
    //   //   billNo: row.workOrdersNumber
    //   // }
    // });
  }
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

// 获取存储的tag
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("filteredColumnFields");
</script>
