<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="PlanMaterialOrderMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecyclePlanMaterialOrderId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getPlanMaterialOrderTotalAPI,
  getPlanMaterialOrderAPI,
  getPlanMaterialOrderTableAPI,
  exportPlanMaterialOrderAPI
} from "@/api/full-lifecycle/sendLaborers/planMaterialOrder";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 求和hook
import { useColumnsTotal } from "../../../../../../utils/hooks/tableDataHooks/useColumnsTotal";

/** 表格状态数据 */
import { PlanMaterialOrderMap, GetEnumArray } from "../../../../utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useRouter } from "vue-router";
import { useParentColumnTag } from "@/utils/hooks";
defineOptions({
  name: "PlanMaterialOrder"
});
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);
const { setParentColumnTag } = useParentColumnTag();
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "status",
      sort: "asc"
    }
  ]
});
// 获取路由对象
const router = useRouter();
const route = useRoute();

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecyclePlanMaterialOrderId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getPlanMaterialOrderTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getPlanMaterialOrderAPI, {
  states: PlanMaterialOrderMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "status",
    operator: "==",
    threshold: "新建",
    className: "row-pending"
  },
  {
    columnKey: "status",
    operator: "==",
    threshold: "部分执行",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportPlanMaterialOrderAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref(["issueDecreasing"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getPlanMaterialOrderTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  const columnList = ["workOrdersNumber"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  nodeStore.setSonPlanMaterialOrderWorkOrdersNumber(row.workOrdersNumber);
  router.push({
    path: "/full-lifecycle/plan-material-order-detail"
  });
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
