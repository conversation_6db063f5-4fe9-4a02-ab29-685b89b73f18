<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />

    <ReTable
      ref="tableRef"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[, 'pickingOrderCode']"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleOrderPickingSheetItemDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import DetailList from "@/components/ReTable/TableDetail.vue";
import {
  getOrderPickingSheetDetailTotalAPI,
  getOrderPickingSheetDetailAPI,
  getOrderPickingSheetTableDetailAPI
} from "@/api/full-lifecycle/sendLaborers/orderPickingSheet";
import {
  getSomeOrderPickingSheetTotalAPI,
  getSomeOrderPickingSheetDetailAPI,
  getSomeOrderPickingSheetTableAPI,
  exportSomeOrderPickingSheetAPI
} from "@/api/full-lifecycle/sendLaborers/orderPickingSheetDetail";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 求和hook
import { useColumnsTotal } from "@/utils/hooks/tableDataHooks/useColumnsTotal";
import { useParentColumnTag } from "@/utils/hooks";

/** 表格状态数据 */
import {
  orderPickingSheetMap,
  GetEnumArray
} from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useRouter } from "vue-router";
defineOptions({
  name: "OrderPickingSheetItemDetail"
});
const route = useRoute();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);

const { getParentColumnTag, setParentColumnTag } = useParentColumnTag();
const routeTitle = ref("");
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "date",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleOrderPickingSheetItemDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getSomeOrderPickingSheetTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSomeOrderPickingSheetDetailAPI,
  {
    states: orderPickingSheetMap
  },
  false,
  {
    queryId: nodeStore.sonWarehousePreparationSelectDetail.productionOrderNo,
    materialCode: nodeStore.sonWarehousePreparationSelectDetail.materialCode
  }
);

// 高亮哪个状态情况下的列
// 表示某列字段的columnKey的键值等于 status 的值时，该列的单元格会应用 className 指定的样式类名
// greater（大于）、less（小于）、amount（等于）三种状态  还有做————————正在构思
const tableRowWarnStatus = reactive([]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSomeOrderPickingSheetAPI, {
        queryId:
          nodeStore.sonWarehousePreparationSelectDetail.productionOrderNo,
        materialCode: nodeStore.sonWarehousePreparationSelectDetail.materialCode
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "planPickingQuantity",
  "planReturnMaterialQuantity",
  "pickedQuantity"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getSomeOrderPickingSheetTotalAPI, {
  queryId: nodeStore.sonWarehousePreparationSelectDetail.productionOrderNo,
  materialCode: nodeStore.sonWarehousePreparationSelectDetail.materialCode
});
// 获取路由对象
const router = useRouter();
// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  console.log("row.pickingOrderCode---------", row.pickingOrderCode);
  // router.push({
  //   path: "/full-lifecycle/send-laborers-son/order-picking-sheet/order-picking-sheet-detail",
  //   query: {
  //     billNo: row.billNo
  //   }
  // });
  nodeStore.setSonOrderPickingSheetPickingOrderCode(row.pickingOrderCode);
  if (column.property === "pickingOrderCode") {
    router.push({
      path: "/full-lifecycle/order-picking-sheet-detail"
    });
  }
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  nodeStore.setSonOrderPickingSheetPickingOrderCode(row.pickingOrderCode);
  if (column.property === "pickingOrderCode") {
    const columnList = ["pickingOrderCode"]; // 客户指定的字段
    setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
    router.push({
      path: "/full-lifecycle/order-picking-sheet-detail"
    });
  }
};

// 获取tag
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("filteredColumnFields");
</script>
