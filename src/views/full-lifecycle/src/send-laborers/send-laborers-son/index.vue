<script setup lang="ts">
import { ref, computed } from "vue";
import VueFlow from "@/components/ReVueFlow/send-laborers-flow/index.vue";
import PlanMaterialOrder from "./plan-material-order/index.vue";
import PlanDispatchWorkers from "./plan-dispatch-workers/index.vue";
import OrderPickingSheet from "./order-picking-sheet/index.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import OrderSelector from "../../../components/OrderSelector.vue";
const { isVisible } = useVisibility();
defineOptions({
  name: "SendLaborersSon"
});
const nodeName = ref("");

// 进入页面获取所有节点进度

const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<template>
  <div class="full-lifecycle relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <div class="resizable-content">
      <!-- 派工 -->
      <!-- <FillDetail v-if="nodeName === '派工'" /> -->
      <!-- 计划派工 -->
      <PlanDispatchWorkers v-if="nodeName === '计划派工'" />
      <!-- 创建计划发料单 -->
      <PlanMaterialOrder v-if="nodeName === '创建计划发料单'" />
      <!-- 创建拣选单 -->
      <OrderPickingSheet v-if="nodeName === '创建拣选单'" />
    </div>
  </div>
</template>
