<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="returnStatusMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleWorkshopReturnMaterialsId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getWorkshopReturnMaterialTotalAPI,
  getWorkshopReturnMaterialAPI,
  getWorkshopReturnMaterialTableAPI,
  exportWorkshopReturnMaterialAPI
} from "@/api/full-lifecycle/workshop-production/peoduction-son/workshopreturnmaterial";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";

/** 表格状态数据 */
import { returnStatusMap, GetEnumArray } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const historyColumnsKeys = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "status",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleWorkshopReturnMaterialsId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWorkshopReturnMaterialTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getWorkshopReturnMaterialAPI, {
  states: returnStatusMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "status",
    operator: "==",
    threshold: "新建",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportWorkshopReturnMaterialAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref(["returnMaterialQuantity", "planReturnMaterialQuantity"]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getWorkshopReturnMaterialTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
