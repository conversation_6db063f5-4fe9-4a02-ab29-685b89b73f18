<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="replenishmentFeedingStatusMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[
        'workRejectQty',
        'incomingRejectQty',
        'goodRejectQty',
        'replenishQtyAfterReject',
        'workReplenishQty'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleReplenishmentWorkshopsId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getFeedBackMaterialAPI,
  getFeedBackMaterialTotalAPI,
  getFeedBackMaterialTableAPI,
  exportFeedBackMaterialAPI
} from "@/api/full-lifecycle/workshop-production/peoduction-son/workshopProductionSubProcess";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";

/** 表格状态数据 */
import { replenishmentFeedingStatusMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useParentColumnTag } from "@/utils/hooks";
defineOptions({
  name: "ReturnProcess"
});
const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置
const historyColumnsKeys = ref([]);
const { setParentColumnTag } = useParentColumnTag();
// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "feedStatus",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleReplenishmentWorkshopsId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getFeedBackMaterialTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getFeedBackMaterialAPI, {
  states: replenishmentFeedingStatusMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "feedStatus",
    operator: "==",
    threshold: "",
    className: "row-pending"
  },
  {
    columnKey: "feedStatus",
    operator: "==",
    threshold: "未补料",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportFeedBackMaterialAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "workRejectQty",
  "incomingRejectQty",
  "goodRejectQty",
  "replenishQtyAfterReject",
  "workReplenishQty",
  "pendingReplenishQtyAfterReject",
  "workPendingReplenishQty"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getFeedBackMaterialTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  const returnList = ["workRejectQty", "incomingRejectQty", "goodRejectQty"];
  const fillList = ["replenishQtyAfterReject", "workReplenishQty"];
  const columnList = ["productionOrderNo", "materialCode"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  if (returnList.includes(column.property)) {
    // 跳转至退料页面
    router.push({
      path: "/full-lifecycle/workshop-production/return-detail",
      query: {
        productionOrderNo: row.productionOrderNo,
        materialCode: row.materialCode
      }
    });
  } else if (fillList.includes(column.property)) {
    // 跳转至补料页面
    router.push({
      path: "/full-lifecycle/workshop-production/fill-detail",
      query: {
        productionOrderNo: row.productionOrderNo,
        materialCode: row.materialCode
      }
    });
  }
};
</script>
