<script setup lang="ts">
import { ref, computed } from "vue";
import VueFlow from "@/components/ReVueFlow/workshop-production-flow/index.vue";
import FillDetail from "../fill/index.vue";
import ReturnDetail from "../return/index.vue";
import ReturnProcess from "./return-process-total.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import OrderSelector from "../../../components/OrderSelector.vue";
const { isVisible } = useVisibility();
defineOptions({
  name: "ReturnProcess"
});
const nodeName = ref("");

// 进入页面获取所有节点进度

const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};
</script>

<template>
  <div class="full-lifecycle relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <div class="resizable-content">
      <!-- 车间补料详情 -->
      <FillDetail v-if="nodeName === '车间补料详情'" />
      <!-- 车间退料详情 -->
      <ReturnDetail v-if="nodeName === '车间退料详情'" />
      <!-- 车间退补料情况汇总 -->
      <ReturnProcess v-if="nodeName === '车间退补料情况汇总'" />
    </div>
  </div>
</template>
