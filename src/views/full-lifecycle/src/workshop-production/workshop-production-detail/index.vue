<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :mappingStatus="workshopProductionDetailMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted } from "vue";
import { fullLifecycleWorkshopProductionDetailId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import exportToExcel from "@/utils/exportXlsx";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import {
  getWorkshopProductionsDetailTotalAPI,
  getWorkshopProductionDetailAPI,
  getWorkshopProductionDetailTableAPI,
  exportWorkshopProductionDetailAPI
} from "@/api/full-lifecycle/workshop-production/workshopProductionDetail";
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";
import DetailList from "@/components/ReTable/TableDetail.vue";

// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";

/** 表格状态数据 */
import { workshopProductionDetailMap } from "@/views/full-lifecycle/utils";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useRouter } from "vue-router";

import { useParentColumnTag } from "@/utils/hooks";

defineOptions({
  name: "WorkshopProductionDetail"
});
const route = useRoute();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
// 定义表格配置

const routeTitle = ref("");

const { getParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "completionRate",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleWorkshopProductionDetailId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWorkshopProductionDetailTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getWorkshopProductionDetailAPI,
  {
    states: workshopProductionDetailMap
  },
  false,
  {
    queryId: route.query.billNo
  }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "completionRate",
    operator: "<",
    threshold: 100,
    className: "row-pending",
    sift: "%"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportWorkshopProductionDetailAPI, {
        queryId: route.query.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "completionNumber",
  "plannedCompletionNumber",
  "unqualifiedQuantitys",
  "ableSendWorkersNumbers",
  "outstandingQuantity",
  "scrapNumbers",
  "transferInQuantity",
  "singlePrice",
  "unInspectionQuantity",
  "reportedQuantity",
  "unreportedQuantity"
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
const getTotal = getTotalHook(sumList, getWorkshopProductionsDetailTotalAPI, {
  queryId: route.query.billNo
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("filteredColumnFields");
otherField.value[0].label = "生产任务单号";
</script>
