<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import VueFlow from "@/components/ReVueFlow/full-life-cycle/index.vue";
import SalesOrder from "./src/sales-orders/index.vue";
import SellOut from "./src/sell-out/index.vue";
import ProductionOrder from "./src/production-order/index.vue";
import ProductionInStock from "./src/production-in-stock/index.vue";
import CompletionInspection from "./src/completion-inspection/index.vue";
import WorkshopProduction from "./src/workshop-production/index.vue";
import WarehousePreparation from "./src/warehouse-preparation/index.vue";
import SendLaborers from "./src/send-laborers/index.vue";
import ProductionOrderReview from "./src/production-order-review/index.vue";
import ProgramOrders from "./src/program-orders/index.vue";
import Mrp from "./src/mrp/index.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import OrderSelector from "./components/OrderSelector.vue";

const { isVisible } = useVisibility();
defineOptions({
  name: "FullLifecycle"
});
const nodeName = ref("");

const clickNodes = node => {
  console.log(node, "点击的节点");
  nodeName.value = node;
};

</script>

<template>
  <div class="full-lifecycle relative">
    <div class="absolute top-1 left-2 z-[9]">
      <OrderSelector />
    </div>
    <transition name="fade">
      <VueFlow v-show="isVisible" @click-node="clickNodes" />
    </transition>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <div class="resizable-content">
      <!-- 销售订单列表 -->
      <SalesOrder v-if="nodeName === '销售订单'" />
      <!-- 销售出库 -->
      <SellOut v-if="nodeName === '销售出库'" />
      <!-- 生产订单 -->
      <ProductionOrder v-if="nodeName === '生产订单'" />
      <!-- 生产入库 -->
      <ProductionInStock v-if="nodeName === '生产入库'" />
      <!-- 完工检验 -->
      <CompletionInspection v-if="nodeName === '完工检验'" />
      <!-- 车间生产 -->
      <WorkshopProduction v-if="nodeName === '车间生产'" />
      <!-- 仓库备料 -->
      <WarehousePreparation v-if="nodeName === '仓库备料'" />
      <!-- 派工 -->
      <SendLaborers v-if="nodeName === '派工'" />
      <!-- 审核生产订单及用料清单 -->
      <ProductionOrderReview v-if="nodeName === '审核生产订单及用料清单'" />
      <!-- 计划订单 -->
      <ProgramOrders v-if="nodeName === '计划订单'" />
      <!-- MRP运算 -->
      <Mrp v-if="nodeName === 'MRP运算'" />
    </div>
  </div>
</template>

<style scoped>
.filter-element {
  width: 200px;
  margin-bottom: 0;
}
</style>
