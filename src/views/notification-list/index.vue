<template>
  <div class="notification-list-page">
    <div class="list-header">
      <div class="list-tabs">
        <div
          v-for="tab in tabs"
          :key="tab.value"
          :class="['tab-item', { active: activeTab === tab.value }]"
          @click="activeTab = tab.value"
        >
          {{ tab.label }} ({{ tab.count }})
        </div>
      </div>
    </div>

    <vxe-table
      ref="tableRef"
      :data="filteredNotifications"
      height="500"
      border
      stripe
      highlight-hover-row
      resizable
      show-overflow
    >
      <vxe-column type="seq" title="序号" width="60" fixed="left" />
      <vxe-column field="errType" title="标题" width="200" resizable />
      <vxe-column field="errMsg" title="内容" width="300" resizable />
      <vxe-column field="status" title="状态" width="120" resizable>
        <template #default="{ row }">
          <el-tag :type="row.read ? 'success' : 'warning'">
            {{ row.read ? "已处理" : "未处理" }}
          </el-tag>
        </template>
      </vxe-column>
      <vxe-column field="createDate" title="时间" width="150" resizable />
      <vxe-column field="resolved" title="已读/未读" width="120">
        <template #default="{ row }">
          <span :class="['read-status', { read: row.read }]">
            {{ row.read ? "已读" : "未读" }}
          </span>
        </template>
      </vxe-column>
      <vxe-column title="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="toggleReadStatus(row)">
            {{ row.read ? "标记未读" : "标记已读" }}
          </el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { VxeTable, VxeColumn } from "vxe-table";
import "vxe-table/lib/style.css";
import { getAbnormalSituationOverview } from "@/api/abnormal-situation-overview";
defineOptions({
  name: "NotificationList"
});

const queryData = ref({
  page: { current: 1, size: 5, default: 20 },
  query: [],
  order: [
    {
      name: "createDate",
      sort: "desc"
    }
  ]
});

// 模拟数据
const notifications = ref([]);
onMounted(async () => {
  const res = await getAbnormalSituationOverview(queryData.value);
  console.log(res, "res");

  notifications.value = res.data.records;
  tabs.value[0].count = notifications.value.length;
  tabs.value[1].count = notifications.value.filter(
    n => n.resolved === false
  ).length;
  tabs.value[2].count = notifications.value.filter(
    n => n.type === "message"
  ).length;
});

// 分类标签
const tabs = ref([
  { label: "全部", value: "all", count: notifications.value.length },
  {
    label: "待办",
    value: "todo",
    count: notifications.value.filter(n => n.resolved === false).length
  },
  {
    label: "消息",
    value: "message",
    count: notifications.value.filter(n => n.type === "message").length
  }
]);

const activeTab = ref("all");

// 根据分类过滤数据
const filteredNotifications = computed(() => {
  if (activeTab.value === "all") return notifications.value;
  return notifications.value.filter(n => n.resolved === false);
});

// 切换已读/未读状态
function toggleReadStatus(row) {
  row.read = !row.read;
}
</script>

<style scoped>
.notification-list-page {
  padding: 20px;
}

.list-header {
  margin-bottom: 20px;
}

.list-tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.tab-item {
  padding: 5px;
  font-size: 0.75rem;
  cursor: pointer;
  background-color: #e0e0e0;
  border-radius: 4px;
}

.tab-item.active {
  color: white;
  background-color: #1890ff;
}
</style>
