<template>
  <div>
    <el-row v-show="isVisible" class="flex">
      <Card
        v-for="(item, index) in cardList"
        :key="index"
        class="flex-1"
        :title="item.title"
        :data="item.data"
      />
    </el-row>
    <!-- 伸缩分割线 -->
    <ArrowDivider />
    <ReTable
      ref="tableRef"
      :mappingStatus="salesDeliveryOrderMonitoringMap"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="[
        'createdPlannedIssuanceQuantity',
        'uncreatedPlannedIssuanceQuantity',
        'finalMaterialSourceNo',
        'sourceType',
        'createdPickingOrderQuantity',
        'uncreatedPickingOrderQuantity'
      ]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      height="94%"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { salesDeliveryOrderMonitoringId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import { useParentColumnTag } from "@/utils/hooks";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";
import { useVisibility } from "@/utils/useVisibility";
import { salesDeliveryOrderMonitoringMap } from "@/views/sales-delivery-order-monitoring/utils";
import {
  getSalesDeliveryOrderMonitoringListAPI,
  getSalesDeliveryOrderMonitoringFieldAPI,
  getNumberOfOverdueInspectionsAPI,
  getHeadThreeDaysTheNumberOfPendingTestsAPI,
  getHeadSevenDaysTheNumberOfPendingTestsAPI,
  getHeaderNumberOfThirdPartyTestsNotCompletedAPI,
  exportSalesDeliveryOrderMonitoringAPI,
  updateSalesDeliveryOrderMonitorAPI
} from "@/api/sales-delivery-order-monitoring";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import Card from "@/views/home/<USER>/Card/saleCard.vue";
import { useRouter } from "vue-router";
import { useCardData } from "@/utils/hooks";
defineOptions({
  name: "salesDeliveryOrderMonitoring"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const router = useRouter();
const { setParentColumnTag, getParentColumnTag } = useParentColumnTag();
const { isVisible } = useVisibility();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "whetherCompleted",
      sort: "desc"
    },
    {
      name: "countdownArrival",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  handleEdit,
  columns
} = useFetchTableData(
  salesDeliveryOrderMonitoringId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getSalesDeliveryOrderMonitoringFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSalesDeliveryOrderMonitoringListAPI,
  {
    states: salesDeliveryOrderMonitoringMap
  }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "countdownArrival",
    operator: "<=",
    threshold: 3,
    className: "row-red",
    relation: "and",
    children: [
      {
        columnKey: "whetherCompleted",
        operator: "==",
        threshold: "否",
        className: "row-red",
        sift: "and"
      }
    ]
  },
  {
    columnKey: "countdownArrival",
    operator: ">",
    threshold: 3,
    className: "row-pending",
    relation: "and",
    children: [
      {
        columnKey: "countdownArrival",
        operator: "<=",
        threshold: 7,
        className: "row-pending",
        sift: "and"
      },
      {
        columnKey: "whetherCompleted",
        operator: "==",
        threshold: "否",
        className: "row-pending",
        sift: "and"
      }
    ]
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureRemark",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit("editTime");
    }
  },
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSalesDeliveryOrderMonitoringAPI, {});
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  },
  {
    label: "buttons.pureStatus",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit(
        "status",
        updateSalesDeliveryOrderMonitorAPI,
        "saleOutBoundCode"
      );
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
  const columnList = ["saleOutBoundCode"];
  setParentColumnTag(
    row,
    columns.value,
    columnList,
    "salesDeliveryOrderMonitoring"
  );
  router.push({
    path: "/sales-delivery-order-row",
    query: {
      billNo: row.saleOutBoundCode
    }
  });
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
  if (column.property === "saleOutBoundCode") {
    const columnList = ["saleOutBoundCode"];
    setParentColumnTag(
      row,
      columns.value,
      columnList,
      "salesDeliveryOrderMonitoring"
    );
    router.push({
      path: "/sales-delivery-order-row",
      query: {
        billNo: row.saleOutBoundCode
      }
    });
  }
};
// 初始化 cardList
const cardList = ref([
  { title: "未完成第三方检验数量", data: null },
  { title: "3天临期未检验数量", data: null },
  { title: "7天临期未检验数量", data: null },
  { title: "逾期未检验数量", data: null }
]);

// API 请求函数ss
const fetchData = async () => {
  try {
    const [res1, res2, res3, res4] = await Promise.all([
      getHeaderNumberOfThirdPartyTestsNotCompletedAPI(),
      getHeadThreeDaysTheNumberOfPendingTestsAPI(),
      getHeadSevenDaysTheNumberOfPendingTestsAPI(),
      getNumberOfOverdueInspectionsAPI()
    ]);

    // 更新 cardList 数据
    cardList.value = [
      { title: "未完成第三方检验数量", data: res1.data },
      { title: "3天临期未检验数量", data: res2.data },
      { title: "7天临期未检验数量", data: res3.data },
      { title: "逾期未检验数量", data: res4.data }
    ];
  } catch (error) {
    console.error("数据获取失败:", error);
  }
};

// 组件挂载时调用 API
onMounted(fetchData);
</script>
