import { message } from "@/utils/message";
import { ref } from "vue";
import { getToken } from "@/utils/auth";
import { createFormData, deviceDetection } from "@pureadmin/utils";
import {
  uploadImageAPI,
  getImageAPI,
  deleteImageAPI
} from "@/api/sales-delivery-order-monitoring/sales-delivery-order-details";

export const useEditReportHook = (selectRow: any) => {
  /**
   * 编辑弹窗显示
   */
  const visible = ref(false);

  const formData = ref({});
  const isMobile = deviceDetection();
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const token = getToken().data;
  console.log(token, "token");

  /**
   * 带上传图片的编辑
   */
  const uploadEdit = async () => {
    if (selectRow.value.length > 1 || selectRow.value.length == 0) {
      selectRow.value.length == 0
        ? message("请选择数据", { type: "warning" })
        : message("只能选择一条数据", { type: "warning" });
      return;
    }
    const { data } = await getImageAPI(selectRow.value[0].id);

    formData.value = { ...selectRow.value[0], fileList: data, remark: "" };
    visible.value = true;
  };

  /**
   * 编辑弹窗关闭
   * */
  const close = () => {
    visible.value = false;
    formData.value.fileList = [];
  };

  /**
   * 图片上传
   */
  const handleCustomClick = e => {
    e.preventDefault();
    // PC端直接触发文件选择
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "";
    input.onchange = handleChange;
    // document.body.appendChild(input); // 将元素添加到 DOM 中
    input.click();
  };

  const handleChange = async e => {
    console.log(e.target.files, "e");
    const file = createFormData(
      {
        avatarFile: new File([e.target.files[0]], "avatar.png")
      },
      {
        fileKey: "file"
      }
    );
    const res = await uploadImageAPI(formData.value.id, file);
    formData.value.fileList.push(res.data);
    console.log(res, "res");
  };

  const handleRemove = async file => {
    console.log(file, "file");
    await deleteImageAPI(file.imageId);
    formData.value.fileList = formData.value.fileList.filter(
      item => item.imageId !== file.imageId
    );
  };

  const handleConfirm = async () => {};

  return {
    visible,
    formData,
    isMobile,
    baseUrl,
    token,
    uploadEdit,
    close,
    handleCustomClick,
    handleRemove,
    handleConfirm
  };
};
