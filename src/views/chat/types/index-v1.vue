<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import type { Chat, Message } from "./types/chat";
import ChatMessage from "@/components/Chat/ChatMessage.vue";
import ChatInput from "@/components/Chat/ChatInput.vue";
import ChatSidebar from "@/components/Chat/ChatSidebar.vue";
import DigitalAvarar from "@/components/Chat/DigitalAvarar.vue";

// defineOptions({
//   name: "Chat"
// });

const STORAGE_KEY = "vue-ai-chat-history";

const chats = ref<Chat[]>([]);
const currentChatId = ref<string | null>(null);
const loading = ref(false);
const isSpeaking = ref(false);

const currentChat = ref<Chat | null>(null);

watch(currentChatId, id => {
  currentChat.value = chats.value.find(chat => chat.id === id) || null;
});

const saveChats = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(chats.value));
};

watch(chats, saveChats, { deep: true });

const createNewChat = () => {
  const newChat: Chat = {
    id: Date.now().toString(),
    title: "1New Chat",
    messages: [
      {
        id: "1",
        content:
          "qHello! I'm your digital assistant. How can I help you today?",
        sender: "ai",
        timestamp: Date.now()
      }
    ],
    createdAt: Date.now()
  };
  chats.value.push(newChat);
  currentChatId.value = newChat.id;
  simulateAISpeaking(newChat.messages[0].content);
};

const deleteChat = (chatId: string) => {
  const index = chats.value.findIndex(chat => chat.id === chatId);
  if (index !== -1) {
    chats.value.splice(index, 1);
    if (currentChatId.value === chatId) {
      currentChatId.value = chats.value.length > 0 ? chats.value[0].id : null;
    }
  }
};

const renameChat = (chatId: string, newTitle: string) => {
  const chat = chats.value.find(chat => chat.id === chatId);
  if (chat) {
    chat.title = newTitle;
  }
};

const simulateAISpeaking = (text: string) => {
  isSpeaking.value = true;
  // Simulate speaking duration based on text length
  const duration = Math.max(2000, text.length * 50);
  setTimeout(() => {
    isSpeaking.value = false;
  }, duration);
};

const handleSend = async (content: string, files: File[]) => {
  if (!currentChat.value) return;

  // Add user message
  const userMessage: Message = {
    id: Date.now().toString(),
    content,
    sender: "user",
    timestamp: Date.now(),
    files
  };
  currentChat.value.messages.push(userMessage);

  // Update chat title if it's the first user message
  if (currentChat.value.messages.length === 2) {
    currentChat.value.title =
      content.slice(0, 30) + (content.length > 30 ? "..." : "");
  }

  // Simulate AI response
  loading.value = true;
  setTimeout(() => {
    if (!currentChat.value) return;

    const responses = [
      "I understand what you're saying. Let me help you with that.",
      "That's an interesting point! Here's what I think...",
      "I'd be happy to assist you with that request.",
      "Let me process that information and provide a helpful response.",
      "I appreciate you sharing that. Here's my perspective..."
    ];
    const response = responses[Math.floor(Math.random() * responses.length)];

    currentChat.value.messages.push({
      id: Date.now().toString(),
      content: response,
      sender: "ai",
      timestamp: Date.now()
    });
    loading.value = false;
    simulateAISpeaking(response);
  }, 1000);
};

onMounted(() => {
  const savedChats = localStorage.getItem(STORAGE_KEY);
  if (savedChats) {
    chats.value = JSON.parse(savedChats);
    if (chats.value.length > 0) {
      currentChatId.value = chats.value[0].id;
    }
  } else {
    createNewChat();
  }
});
</script>

<template>
  <div class="flex">
    <ChatSidebar
      :chats="chats"
      :current-chat-id="currentChatId"
      @select="currentChatId = $event"
      @new="createNewChat"
      @delete="deleteChat"
      @rename="renameChat"
    />

    <div class="flex-1 flex flex-col min-h-screen">
      <div class="p-4 border-b">
        <h1 class="text-xl font-semibold">
          {{ currentChat?.title || "AI Chat Assistant" }}
        </h1>
      </div>

      <div class="flex-1 overflow-y-auto">
        <div v-if="currentChat">
          <ChatMessage
            v-for="message in currentChat.messages"
            :key="message.id"
            :message="message"
          />
          <div v-if="loading" class="p-4 text-center text-gray-500">
            AI is thinking...
          </div>
        </div>
      </div>

      <ChatInput @send="handleSend" />
    </div>

    <DigitalAvatar :is-active="true" :is-speaking="isSpeaking" />
  </div>
</template>
