<template>
  <div style="width: 100%; height: 100%">
    <iframe
      v-show="!isLoading"
      ref="iframeElement"
      src="http://47.107.235.247:9080/webapp/chat/external?SUPERSONIC_TOKEN=eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eze3KV1CpJIePTdE0j0cnWwa-1i0u-4ivvJ-NizLmAm0GHRLQyPhfZVUWr32aOm9Ri8lfEAX-FTe_cYSEvuQLQ"
      style="width: 100%; height: 100%; min-height: 700px; border: none"
      frameborder="0"
      allow="microphone"
      @load="handleIframeLoad"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import ScrollingList from "@/components/Chat/WarnList.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n(); // 解构出t方法

const isLoading = ref(true);

const buttons = [
  t("common.pureSalesOrder"),
  t("common.pureSalesOutbound"),
  t("common.pureProductionOrder"),
  t("common.pureCompletionInspection"),
  t("common.pureWorkshopProduction"),
  t("common.pureWarehouseMaterialPreparation"),
  t("common.pureDispatchWork"),
  t("common.purePlannedOrder"),
  t("common.pureMRPCalculation")
];
const columns = [
  { key: "content", title: t("common.pureWarningContent"), width: 150 },
  { key: "type", title: t("common.pureWarningCategory"), width: 100 },
  { key: "time", title: t("common.pureWarningTime"), width: 200 },
  { key: "name", title: t("common.pureResponsiblePerson"), width: 100 },
  { key: "address", title: t("common.pureProblemArea"), width: 100 }
];

const sendList = [
  "请介绍一下达衍虚拟人？",
  "生产工序出现问题，哪些订单未能按时完成？",
  "人力资源不足，影响订单处理效率，有什么解决办法？",
  "什么是虚拟人形象定制？",
  "设备出现故障了，我该联系谁？"
];

// defineOptions({
//   name: "DeepSeek"
// });

const data = [
  {
    id: 1,
    content: "订单延迟交付",
    type: "过望",
    time: "2020-05-17"
  },
  {
    id: 2,
    content: "产品质量问题",
    type: "其他",
    time: "2020-05-17"
  },
  {
    id: 3,
    content: "供应链中断",
    type: "其他",
    time: "2020-05-17"
  },
  {
    id: 4,
    content: "设备故障",
    type: "过望",
    time: "2020-05-17"
  },
  {
    id: 5,
    content: "人员不足",
    type: "其他",
    time: "2020-05-17"
  },
  {
    id: 6,
    content: "设备故障",
    type: "其他",
    time: "2020-05-17"
  },
  {
    id: 7,
    content: "设备故障",
    type: "过望",
    time: "2020-05-17"
  },
  {
    id: 8,
    content: "供应链中断",
    type: "其他",
    time: "2020-05-17"
  }
];
// 属性
// iframeElement.value.requestFullscreen() 全屏

const buttonsContainer = ref<HTMLElement | null>(null);
const iframeElement = ref<HTMLIFrameElement | null>(null);
const updateIframeHeight = () => {
  if (iframeElement.value && buttonsContainer.value) {
    const containerHeight = window.innerHeight;
    const buttonsHeight = buttonsContainer.value.clientHeight;
    const paddingAndMargin = 40; // 调整 padding 和 margin 的总高度
    iframeElement.value.style.height = `${containerHeight - buttonsHeight - paddingAndMargin - 100}px`;
    // getTextareaContent();
  }
};

// 获取 iframe 中 textarea 的文本内容
const getTextareaContent = () => {
  if (iframeElement.value) {
    const iframeDocument =
      iframeElement.value.contentDocument ||
      iframeElement.value.contentWindow?.document;
    if (iframeDocument) {
      const textarea = iframeDocument.querySelector("textarea");
      if (textarea) {
        console.log("textarea 内容:", textarea.value);
      } else {
        console.error("未找到 textarea 元素");
      }
    } else {
      console.error("无法访问 iframe 的内部文档");
    }
  }
};

onMounted(() => {
  updateIframeHeight();
  window.addEventListener("resize", updateIframeHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateIframeHeight);
});

// 处理 iframe 加载完成事件
const handleIframeLoad = () => {
  console.log("iframe 加载完成", iframeElement.value);
  isLoading.value = false;
  //  sendMessageToIframe();
  // 在这里安全地访问 iframe 内部 DOM
  console.log(iframeElement.value.contentDocument, "contentDocument");

  console.log(iframeElement.value.contentWindow, "contentWindow");
  const eL = iframeElement.value.contentWindow.parent;
  const elemtentTxtre = eL.document.querySelectorAll(".custom_button");
  console.log(elemtentTxtre, "133343");
  console.log("***");

  if (iframeElement.value) {
    const iframeDoc =
      iframeElement.value.contentDocument ||
      iframeElement.value.contentWindow?.document;
    if (iframeDoc) {
      const textarea = iframeDoc.querySelector("textarea");
      if (textarea) {
        console.log("获取到 textarea 内容:", textarea.value);
      }
    }
  }
};

// 向 iframe 发送请求消息
const sendMessageToIframe = () => {
  if (iframeElement.value?.contentWindow) {
    iframeElement.value.contentWindow.postMessage(
      { type: "REQUEST_TEXT" }, // 定义消息类型
      "*" // 目标 origin，设为 "*" 允许跨域
    );
  }
};

// 监听 iframe 返回的消息
window.addEventListener("message", event => {
  if (event.data.type === "RESPONSE_TEXT") {
    console.log("从 iframe 获取的文本:", event.data.text);
  }
});

// 父页面通过轮询或事件监听
// const checkIframeContent = () => {
//   if (iframeElement.value) {
//     const iframeDoc = iframeElement.value.contentDocument;
//     if (iframeDoc) {
//       const targetElement = iframeDoc.querySelector(".dynamic-content");
//       if (targetElement) {
//         console.log("动态内容已加载:", targetElement);
//       } else {
//         setTimeout(checkIframeContent, 500); // 每 500ms 检查一次
//       }
//     }
//   }
// };
</script>

<style lang="scss" scoped>
@import url("@/views/chat/style.scss");

.top-btn-ai {
  justify-content: space-between;
}

/* 隐藏 iframe 的滚动条 */
.no-scrollbar {
  overflow: hidden;
}

.main-content {
  margin: 0 !important;
}

.list-disc li {
  cursor: pointer;
}

.list-none {
  list-style-type: none;
}

.btn-github {
  display: flex;
  gap: 0.5rem;
  place-content: center;
  padding: 0.45rem 1.2rem;
  margin-bottom: 8px;
  font-size: 0.825rem;
  font-weight: 500;
  line-height: 1rem;
  color: #333;
  cursor: pointer;
  background-color: #fff;
  border: none;
  border-radius: 100px;
  box-shadow:
    inset 0 1px 0 0 rgb(255 255 255 / 4%),
    inset 0 0 0 1px rgb(255 255 255 / 4%);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.btn-github:hover {
  color: #fff;
  background-color: #79bbff;
  box-shadow:
    inset 0 1px 0 0 #79bbff,
    inset 0 0 0 1px #79bbff;
  transform: translate(0, -0.25rem);
}

.box-show {
  box-shadow:
    rgb(60 64 67 / 30%) 0 1px 2px 0,
    rgb(60 64 67 / 15%) 0 2px 6px 2px;
}
</style>

<style scoped>
@media (width <= 1240px) {
  .top-btn-ai {
    gap: 5px;
    justify-content: flex-start;
  }
}
</style>
