<template>
  <div
    class="flex flex-col"
    style="
      background: linear-gradient(
        to top,
        rgb(255 255 255 / 0%) 0%,
        rgb(173 216 230 / 10%) 100%
      );
    "
  >
    <div class="flex-1 container mx-auto max-w-4xl p-4">
      <div class="rounded-lg shadow-lg overflow-hidden">
        <!-- 头部 -->
        <div class="border-b p-4 flex justify-between items-center">
          <h1 class="text-xl font-semibold">ChatBi</h1>
        </div>

        <!-- AI那边的消息 -->
        <div ref="chatContainer" class="h-[600px] overflow-y-auto">
          <ChatMessage
            v-for="(message, index) in messages"
            :key="index"
            :content="message.content"
            :isAI="message.isAI"
            @click="handleMessageClick(message)"
          />
          <div v-if="isTyping" class="flex gap-4 p-4 bg-gray-50">
            <div
              class="w-8 h-8 rounded-full bg-blue-500 flex-shrink-0 flex items-center justify-center text-white"
            >
              AI
            </div>
            <div class="flex gap-2">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
              <div
                class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: 0.2s"
              />
              <div
                class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: 0.4s"
              />
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="border-t p-4">
          <div class="flex" style="justify-content: space-between">
            <div
              class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex"
            >
              <select
                v-model="selectedTable"
                class="px-4 py-2 border rounded-lg focus:ring-2 font-semibold focus:ring-blue-500 focus:border-transparent"
                @change="handleTableChange"
              >
                <option value="retail">零售示例数据表</option>
                <option value="ecommerce">电商示例数据表</option>
                <option value="inventory">库存示例数据表</option>
                <option value="forecast">销售预测数据表</option>
              </select>

              <p>&nbsp;&nbsp;|&nbsp;&nbsp;</p>
              <p
                style="
                  font-size: 14px;
                  line-height: 28px;
                  color: #2682fa;
                  cursor: pointer;
                "
              >
                预览
              </p>
            </div>

            <button
              class="bg-white"
              style="
                height: 35px;
                padding: 0 20px;
                font-size: 14px;
                border-radius: 5px;
              "
              @click="clearChat"
            >
              清空对话
            </button>
          </div>
          <div class="relative">
            <textarea
              v-model="userInput"
              class="w-full pr-24 resize-none rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent p-4"
              rows="3"
              placeholder="发送时间 | 条件 | 维度 | 指标相关问题，shift+回车换行，回车发送"
              @keydown.enter.prevent="sendMessage"
            />
            <button
              class="absolute bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              :disabled="isTyping || !userInput.trim()"
              @click="sendMessage"
            >
              发送
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from "vue";
import { useScroll } from "@vueuse/core";
import ChatMessage from "@/components/Chat/ChatBi/ChatMessage.vue";

defineOptions({
  name: "ChatBi"
});

interface Message {
  content: string;
  isAI: boolean;
}

const INITIAL_MESSAGE = {
  content: `Hi, 我是ChatBI，我可以帮你分析问题，分析数据，生成查看。

已选择"专项市场数据报表"，请在下方选择您想要查看的报表：

1. 2024年全品类每个月的销售额分析报表
2. 2023年电商品类每个城市销售额分析报表
3. 2023年平台SKU每个月的库存情况报表
4. 2024年各个品类的销售额预测报表
5. 2024年销售额同比分析报表`,
  isAI: true
};

const TEMPLATE_RESPONSES: Record<string, string> = {
  "2024年全品类每个月的销售额分析报表": `根据2024年全品类销售数据分析：

1月销售额：¥1,234,567
2月销售额：¥1,456,789
3月销售额：¥1,678,901

主要发现：
1. Q1整体销售呈上升趋势
2. 休闲食品类增长最快，环比增长23%
3. 生鲜类目受季节影响，2月份有所下滑

建议：
1. 加大休闲食品类目的推广力度
2. 针对生鲜类目制定季节性营销策略
3. 关注增长最快的TOP3品类：零食、美妆、数码`,

  "2023年电商品类每个城市销售额分析报表": `2023年电商品类城市销售分布：

TOP5城市销售额：
1. 北京：¥8,765,432
2. 上海：¥7,654,321
3. 广州：¥6,543,210
4. 深圳：¥5,432,109
5. 杭州：¥4,321,098

重点发现：
1. 一线城市占总销售额的45%
2. 新一线城市增速最快，同比增长35%
3. 下沉市场潜力显现，三四线城市增速超20%

建议：
1. 持续深耕一线城市市场
2. 加大新一线城市资源投入
3. 针对下沉市场开发差异化产品`,

  "2023年平台SKU每个月的库存情况报表": `2023年平台SKU库存分析：

月度库存趋势：
1月：85%
2月：78%
3月：92%
...

库存异常SKU：
- 积压SKU：123个
- 断货SKU：45个
- 周转过慢：89个

改进建议：
1. 优化补货周期，建议设置安全库存
2. 清理积压商品，考虑促销处理
3. 加强热销商品库存预警`,

  "2024年各个品类的销售额预测报表": `2024年品类销售预测：

预计增长最快品类：
1. 数码电子：+45%
2. 美妆个护：+38%
3. 运动户外：+32%

市场机会：
1. 新能源相关产品需求上升
2. 健康养生品类持续走强
3. 智能家居市场潜力大

风险提示：
1. 传统品类增长放缓
2. 竞品价格战加剧
3. 原材料成本上涨`,

  "2024年销售额同比分析报表": `2024年销售额同比分析：

整体表现：
- 同比增长：+25%
- 环比增长：+8%
- 市场份额：23%

增长亮点：
1. 线上渠道增长33%
2. 新客户贡献提升40%
3. 复购率提升15%

需要关注：
1. 获客成本上升
2. 部分品类毛利下滑
3. 竞争加剧影响定价`
};

const messages = ref<Message[]>([INITIAL_MESSAGE]);
const userInput = ref("");
const isTyping = ref(false);
const chatContainer = ref<HTMLElement | null>(null);

const { y } = useScroll(chatContainer);

function clearChat() {
  messages.value = [INITIAL_MESSAGE];
  userInput.value = "";
}

function extractTemplateQuestion(content: string): string | null {
  const lines = content.split("\n");
  for (const line of lines) {
    const trimmed = line.trim();
    if (
      trimmed.match(/^\d+\.\s/) &&
      TEMPLATE_RESPONSES[trimmed.replace(/^\d+\.\s/, "")]
    ) {
      return trimmed.replace(/^\d+\.\s/, "");
    }
  }
  return null;
}

async function handleMessageClick(message: Message) {
  if (!message.isAI || isTyping.value) return;

  const question = extractTemplateQuestion(message.content);
  if (question && TEMPLATE_RESPONSES[question]) {
    messages.value.push({
      content: question,
      isAI: false
    });
    await simulateAIResponse(question);
  }
}

// 先模拟AI流式效果，接口后面找 林 对接
async function simulateAIResponse(input: string) {
  isTyping.value = true;

  const response =
    TEMPLATE_RESPONSES[input] ||
    `I received your message: "${input}"\n\nHere's a simulated streaming response that demonstrates the concept. In a real application, this would be connected to an actual AI API.`;

  let streamedResponse = "";
  messages.value.push({ content: "", isAI: true });

  for (const char of response) {
    streamedResponse += char;
    messages.value[messages.value.length - 1].content = streamedResponse;
    await new Promise(resolve => setTimeout(resolve, 30));
  }

  isTyping.value = false;
}

async function sendMessage() {
  if (!userInput.value.trim() || isTyping.value) return;

  const message = userInput.value;
  messages.value.push({
    content: message,
    isAI: false
  });

  userInput.value = "";
  await nextTick();
  scrollToBottom();

  await simulateAIResponse(message);
}

function scrollToBottom() {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
}

watch(
  messages,
  () => {
    nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true }
);

onMounted(() => {
  scrollToBottom();
});
</script>

<style>
@tailwind base;
@tailwind components;
@tailwind utilities;

.prose {
  @apply text-gray-800 leading-relaxed;
}

.prose p {
  @apply mb-4;
}

.prose p:last-child {
  @apply mb-0;
}
</style>

<style scoped>
.main-content {
  margin: 0 !important;
}
</style>
