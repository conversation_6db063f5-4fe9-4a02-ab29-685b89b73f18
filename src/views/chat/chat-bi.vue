<template>
  <div
    class="flex flex-col"
    style="
      background: linear-gradient(
        to top,
        rgb(255 255 255 / 0%) 0%,
        rgb(173 216 230 / 20%) 100%
      );
    "
  >
    <div class="flex-1 container mx-auto max-w-4xl p-4">
      <div class="rounded-lg overflow-hidden">
        <!-- 头部 -->
        <!-- <div class="p-4 flex justify-between items-center">
          <h1 class="text-xl font-semibold">ChatBi</h1>
        </div> -->

        <!-- 消息聊天 -->
        <div ref="chatContainer" class="h-[600px] overflow-y-auto">
          <ChatMessage
            v-for="(message, index) in messages"
            :key="index"
            :content="message.content"
            :isAI="message.isAI"
            @click="handleMessageClick(message)"
          />
          <div v-if="isTyping" class="flex gap-4 p-4 bg-gray-50">
            <div
              class="w-8 h-8 rounded-full bg-blue-500 flex-shrink-0 flex items-center justify-center text-white"
            >
              AI
            </div>
            <div class="flex gap-2">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
              <div
                class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: 0.2s"
              />
              <div
                class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style="animation-delay: 0.4s"
              />
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="p-4">
          <div
            class="flex items-center gap-4"
            style="justify-content: space-between; margin-bottom: 10px"
          >
            <div class="bg-white flex" style="padding: 5px; border-radius: 8px">
              <div
                class="cursor-pointer hover:text-blue-600"
                @click="dialogVisible = true"
              >
                {{ tableNames[selectedTable] }}
              </div>

              <!-- 弹窗 -->
              <el-dialog
                v-model="dialogVisible"
                width="450"
                title="选择数据表"
                draggable
              >
                <el-input
                  v-model="search"
                  placeholder="输入名称搜索"
                  clearable
                  style="margin-bottom: 20px"
                />
                <el-radio-group
                  v-model="selectedValue"
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    width: 100%;
                  "
                >
                  <el-radio
                    v-for="(label, value) in filteredTableNames"
                    :key="value"
                    :label="value"
                    style="width: 100%; margin: 10px; margin-top: 10px"
                  >
                    <div
                      class="flex"
                      style="justify-content: space-between; width: 380px"
                    >
                      <p>{{ label }}</p>
                      <p style="color: #329af2">预览</p>
                    </div>
                  </el-radio>
                </el-radio-group>
                <template v-slot:footer>
                  <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmSelection"
                      >确定</el-button
                    >
                  </span>
                  <p style="margin-top: 10px; color: #ffb869">
                    <span style="color: #bac5c5"> 已选1张表，</span
                    >点击确定清除已有对话
                  </p>
                </template>
              </el-dialog>
              <!-- <select
                v-model="selectedTable"
                style="cursor: pointer"
                @change="handleTableChange"
              >
                <option value="retail">零售示例数据表</option>
                <option value="ecommerce">电商示例数据表</option>
                <option value="inventory">库存示例数据表</option>
                <option value="forecast">销售预测数据表</option>
              </select> -->
              <p>&nbsp;&nbsp;|&nbsp;&nbsp;</p>
              <div
                class="text-blue-500 hover:text-blue-600 ml-2"
                style="cursor: pointer"
                @click="openTablePreview"
              >
                预览
              </div>
            </div>

            <button
              class="bg-white px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              style="
                height: 35px;
                padding: 0 20px;
                font-size: 14px;
                border-radius: 5px;
              "
              @click="clearChat"
            >
              清空对话
            </button>
          </div>
          <div class="relative">
            <textarea
              v-model="userInput"
              style="outline: none"
              class="w-full pr-24 resize-none rounded-lg border border-gray-300 focus:ring-2 focus:border-transparent p-4"
              rows="3"
              placeholder="发送时间 | 条件 | 维度 | 指标相关问题，shift+回车换行，回车发送"
              @keydown.enter.prevent="sendMessage"
            />
            <button
              class="absolute bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              :disabled="isTyping || !userInput.trim()"
              @click="sendMessage"
            >
              发送
            </button>
          </div>

          <!-- 额外功能 -->
          <div class="flex items-center gap-6 mt-4 text-sm text-gray-600">
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                v-model="deepThinking"
                type="checkbox"
                class="form-checkbox h-4 w-4 text-blue-500 rounded focus:ring-blue-500"
              />
              <span>深度思考</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                v-model="followUpQuestions"
                type="checkbox"
                class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500"
              />
              <span>追问</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 表的预览 -->
    <el-dialog
      v-model="showTablePreview"
      title="教育示例数据表"
      width="50%"
      max-width="1200px"
      close-on-click-modal="false"
      draggable
      @close="closeTablePreview"
    >
      <div class="p-4">
        <!-- 选项卡 -->
        <el-tabs v-model="previewTab" class="mb-4">
          <el-tab-pane label="字段预览" name="structure">
            <!-- 字段数据表预览 -->
            <div v-if="previewTab === 'structure'" class="space-y-2">
              <el-collapse v-model="tableFields.activeCollapse" accordion>
                <el-collapse-item
                  v-for="field in tableFields[selectedTable]"
                  :key="field"
                  :title="field"
                  :name="field"
                >
                  <div class="p-2 bg-gray-50 rounded">
                    {{ field }}
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-tab-pane>
          <el-tab-pane label="数据预览" name="data">
            <el-table
              :data="tableData"
              class="w-full"
              stripe
              style="width: 100%"
              height="600px"
            >
              <el-table-column
                v-for="field in tableFields[selectedTable]"
                :key="field"
                :label="field"
                :prop="field"
                align="left"
              />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="p-4 border-t flex justify-end gap-4">
        <el-button type="default" @click="closeTablePreview">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from "vue";
import { useScroll } from "@vueuse/core";
import ChatMessage from "@/components/Chat/ChatBi/ChatMessage.vue";

interface Message {
  content: string;
  isAI: boolean;
}

const tableNames = {
  retail: "零售示例数据表",
  ecommerce: "电商示例数据表",
  inventory: "库存示例数据表",
  forecast: "销售预测数据表"
};
const tableFields = {
  activeCollapse: [], // 默认不展开任何项
  retail: [
    "销售日期",
    "商品类别",
    "商品名称",
    "销售数量",
    "销售金额",
    "毛利率"
  ],
  ecommerce: [
    "订单日期",
    "城市",
    "品类",
    "商品SKU",
    "销售额",
    "客单价",
    "转化率"
  ],
  inventory: [
    "商品SKU",
    "库存数量",
    "安全库存",
    "周转天数",
    "入库日期",
    "出库日期"
  ],
  forecast: ["预测日期", "品类", "预测销售额", "同比增长", "置信度", "影响因素"]
};

const INITIAL_MESSAGES = {
  retail: `Hi, 我是ChatBI，我可以帮你分析问题，分析数据，生成查看。

已选择"零售示例数据表"，请在下方选择您想要查看的报表：

1. 2024年全品类每个月的销售额分析报表
2. 2023年电商品类每个城市销售额分析报表
3. 2023年平台SKU每个月的库存情况报表
4. 2024年各个品类的销售额预测报表
5. 2024年销售额同比分析报表`,

  ecommerce: `Hi, 我是ChatBI，我可以帮你分析电商数据。

已选择"电商示例数据表"，以下是常见分析维度：

1. 2024年各城市的销售趋势分析
2. 品类销售占比及增长分析
3. 客单价和转化率分析
4. TOP10商品SKU分析`,

  inventory: `Hi, 我是ChatBI，我可以帮你分析库存情况。

已选择"库存示例数据表"，以下是常见分析维度：

1. 库存周转率分析
2. 断货风险预警
3. 积压商品分析
4. 安全库存优化建议`,

  forecast: `Hi, 我是ChatBI，我可以帮你预测销售趋势。

已选择"销售预测数据表"，以下是预测维度：

1. 未来3个月销售预测
2. 品类增长趋势预测
3. 季节性波动分析
4. 影响因素分析`
};

const selectedTable = ref("retail");
const showTablePreview = ref(false);
const previewTab = ref("structure");
const messages = ref<Message[]>([
  { content: INITIAL_MESSAGES.retail, isAI: true }
]);
const userInput = ref("");
const isTyping = ref(false);
const chatContainer = ref<HTMLElement | null>(null);
const deepThinking = ref(false);
const followUpQuestions = ref(false);

// 弹窗的可见性
const dialogVisible = ref(false);

// 搜索框的内容
const search = ref("");

// 选中的数据表
const selectedValue = ref("");
const { y } = useScroll(chatContainer);

// 过滤后的数据表名称
const filteredTableNames = computed(() => {
  const searchValue = search.value.toLowerCase();
  return Object.keys(tableNames)
    .filter(key => tableNames[key].toLowerCase().includes(searchValue))
    .reduce((result, key) => {
      result[key] = tableNames[key];
      return result;
    }, {});
});

// 处理弹窗确定按钮
const confirmSelection = () => {
  selectedTable.value = selectedValue.value;
  messages.value = [
    {
      content: INITIAL_MESSAGES[selectedTable.value],
      isAI: true
    }
  ];
  userInput.value = "";
  console.log("Selected Value:", selectedTable.value);
  dialogVisible.value = false;
};

function openTablePreview() {
  showTablePreview.value = true;
}

function closeTablePreview() {
  showTablePreview.value = false;
}

function handleTableChange() {
  dialogVisible.value = true;
  messages.value = [
    {
      content: INITIAL_MESSAGES[selectedTable.value],
      isAI: true
    }
  ];
  userInput.value = "";
}

function clearChat() {
  messages.value = [
    {
      content: INITIAL_MESSAGES[selectedTable.value],
      isAI: true
    }
  ];
  userInput.value = "";
}

function extractTemplateQuestion(content: string): string | null {
  const lines = content.split("\n");
  for (const line of lines) {
    const trimmed = line.trim();
    if (
      trimmed.match(/^\d+\.\s/) &&
      TEMPLATE_RESPONSES[trimmed.replace(/^\d+\.\s/, "")]
    ) {
      return trimmed.replace(/^\d+\.\s/, "");
    }
  }
  return null;
}

async function handleMessageClick(message: Message) {
  if (!message.isAI || isTyping.value) return;
  console.log(123);
  const question = extractTemplateQuestion(message.content);
  if (question && TEMPLATE_RESPONSES[question]) {
    messages.value.push({
      content: question,
      isAI: false
    });
    await simulateAIResponse(question);
  }
}

const TEMPLATE_RESPONSES: Record<string, string> = {
  "2024年全品类每个月的销售额分析报表": `根据2024年全品类销售数据分析：

1月销售额：¥1,234,567
2月销售额：¥1,456,789
3月销售额：¥1,678,901

主要发现：
1. Q1整体销售呈上升趋势
2. 休闲食品类增长最快，环比增长23%
3. 生鲜类目受季节影响，2月份有所下滑

建议：
1. 加大休闲食品类目的推广力度
2. 针对生鲜类目制定季节性营销策略
3. 关注增长最快的TOP3品类：零食、美妆、数码`,

  "2023年电商品类每个城市销售额分析报表": `2023年电商品类城市销售分布：

TOP5城市销售额：
1. 北京：¥8,765,432
2. 上海：¥7,654,321
3. 广州：¥6,543,210
4. 深圳：¥5,432,109
5. 杭州：¥4,321,098

重点发现：
1. 一线城市占总销售额的45%
2. 新一线城市增速最快，同比增长35%
3. 下沉市场潜力显现，三四线城市增速超20%

建议：
1. 持续深耕一线城市市场
2. 加大新一线城市资源投入
3. 针对下沉市场开发差异化产品`,

  "2023年平台SKU每个月的库存情况报表": `2023年平台SKU库存分析：

月度库存趋势：
1月：85%
2月：78%
3月：92%
...

库存异常SKU：
- 积压SKU：123个
- 断货SKU：45个
- 周转过慢：89个

改进建议：
1. 优化补货周期，建议设置安全库存
2. 清理积压商品，考虑促销处理
3. 加强热销商品库存预警`,

  "2024年各个品类的销售额预测报表": `2024年品类销售预测：

预计增长最快品类：
1. 数码电子：+45%
2. 美妆个护：+38%
3. 运动户外：+32%

市场机会：
1. 新能源相关产品需求上升
2. 健康养生品类持续走强
3. 智能家居市场潜力大

风险提示：
1. 传统品类增长放缓
2. 竞品价格战加剧
3. 原材料成本上涨`,

  "2024年销售额同比分析报表": `2024年销售额同比分析：

整体表现：
- 同比增长：+25%
- 环比增长：+8%
- 市场份额：23%

增长亮点：
1. 线上渠道增长33%
2. 新客户贡献提升40%
3. 复购率提升15%

需要关注：
1. 获客成本上升
2. 部分品类毛利下滑
3. 竞争加剧影响定价`
};

async function simulateAIResponse(input: string) {
  isTyping.value = true;

  const response =
    TEMPLATE_RESPONSES[input] ||
    `I received your message: "${input}"\n\nHere's a simulated streaming response that demonstrates the concept. In a real application, this would be connected to an actual AI API.`;

  let streamedResponse = "";
  messages.value.push({ content: "", isAI: true });

  for (const char of response) {
    streamedResponse += char;
    messages.value[messages.value.length - 1].content = streamedResponse;
    await new Promise(resolve => setTimeout(resolve, 30));
  }

  isTyping.value = false;
}

async function sendMessage() {
  if (!userInput.value.trim() || isTyping.value) return;

  const message = userInput.value;
  messages.value.push({
    content: message,
    isAI: false
  });

  userInput.value = "";
  await nextTick();
  scrollToBottom();

  await simulateAIResponse(message);
}

function scrollToBottom() {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
}

watch(
  messages,
  () => {
    nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true }
);

onMounted(() => {
  scrollToBottom();
});

const tableData = [
  {
    销售日期: "2024-10-01",
    商品类别: "电子产品",
    商品名称: "手机",
    销售数量: 10,
    销售金额: 15000,
    毛利率: "20%"
  },
  {
    销售日期: "2024-10-02",
    商品类别: "家电",
    商品名称: "冰箱",
    销售数量: 5,
    销售金额: 10000,
    毛利率: "18%"
  },
  {
    销售日期: "2024-10-03",
    商品类别: "服装",
    商品名称: "T恤",
    销售数量: 20,
    销售金额: 3000,
    毛利率: "30%"
  },
  {
    销售日期: "2024-10-04",
    商品类别: "食品",
    商品名称: "牛奶",
    销售数量: 15,
    销售金额: 2000,
    毛利率: "25%"
  },
  {
    销售日期: "2024-10-05",
    商品类别: "日用品",
    商品名称: "牙刷",
    销售数量: 30,
    销售金额: 1000,
    毛利率: "35%"
  },
  {
    销售日期: "2024-10-01",
    商品类别: "电子产品",
    商品名称: "手机",
    销售数量: 10,
    销售金额: 15000,
    毛利率: "20%"
  },
  {
    销售日期: "2024-10-02",
    商品类别: "家电",
    商品名称: "冰箱",
    销售数量: 5,
    销售金额: 10000,
    毛利率: "18%"
  },
  {
    销售日期: "2024-10-03",
    商品类别: "服装",
    商品名称: "T恤",
    销售数量: 20,
    销售金额: 3000,
    毛利率: "30%"
  },
  {
    销售日期: "2024-10-04",
    商品类别: "食品",
    商品名称: "牛奶",
    销售数量: 15,
    销售金额: 2000,
    毛利率: "25%"
  },
  {
    销售日期: "2024-10-05",
    商品类别: "日用品",
    商品名称: "牙刷",
    销售数量: 30,
    销售金额: 1000,
    毛利率: "35%"
  },
  {
    销售日期: "2024-10-01",
    商品类别: "电子产品",
    商品名称: "手机",
    销售数量: 10,
    销售金额: 15000,
    毛利率: "20%"
  },
  {
    销售日期: "2024-10-02",
    商品类别: "家电",
    商品名称: "冰箱",
    销售数量: 5,
    销售金额: 10000,
    毛利率: "18%"
  },
  {
    销售日期: "2024-10-03",
    商品类别: "服装",
    商品名称: "T恤",
    销售数量: 20,
    销售金额: 3000,
    毛利率: "30%"
  },
  {
    销售日期: "2024-10-04",
    商品类别: "食品",
    商品名称: "牛奶",
    销售数量: 15,
    销售金额: 2000,
    毛利率: "25%"
  },
  {
    销售日期: "2024-10-05",
    商品类别: "日用品",
    商品名称: "牙刷",
    销售数量: 30,
    销售金额: 1000,
    毛利率: "35%"
  },
  {
    销售日期: "2024-10-01",
    商品类别: "电子产品",
    商品名称: "手机",
    销售数量: 10,
    销售金额: 15000,
    毛利率: "20%"
  },
  {
    销售日期: "2024-10-02",
    商品类别: "家电",
    商品名称: "冰箱",
    销售数量: 5,
    销售金额: 10000,
    毛利率: "18%"
  },
  {
    销售日期: "2024-10-03",
    商品类别: "服装",
    商品名称: "T恤",
    销售数量: 20,
    销售金额: 3000,
    毛利率: "30%"
  },
  {
    销售日期: "2024-10-04",
    商品类别: "食品",
    商品名称: "牛奶",
    销售数量: 15,
    销售金额: 2000,
    毛利率: "25%"
  },
  {
    销售日期: "2024-10-05",
    商品类别: "日用品",
    商品名称: "牙刷",
    销售数量: 30,
    销售金额: 1000,
    毛利率: "35%"
  },
  {
    销售日期: "2024-10-01",
    商品类别: "电子产品",
    商品名称: "手机",
    销售数量: 10,
    销售金额: 15000,
    毛利率: "20%"
  },
  {
    销售日期: "2024-10-02",
    商品类别: "家电",
    商品名称: "冰箱",
    销售数量: 5,
    销售金额: 10000,
    毛利率: "18%"
  },
  {
    销售日期: "2024-10-03",
    商品类别: "服装",
    商品名称: "T恤",
    销售数量: 20,
    销售金额: 3000,
    毛利率: "30%"
  },
  {
    销售日期: "2024-10-04",
    商品类别: "食品",
    商品名称: "牛奶",
    销售数量: 15,
    销售金额: 2000,
    毛利率: "25%"
  },
  {
    销售日期: "2024-10-05",
    商品类别: "日用品",
    商品名称: "牙刷",
    销售数量: 30,
    销售金额: 1000,
    毛利率: "35%"
  }
];
</script>

<style>
@tailwind base;
@tailwind components;
@tailwind utilities;

.prose {
  @apply text-gray-800 leading-relaxed;
}

.prose p {
  @apply mb-4;
}

.prose p:last-child {
  @apply mb-0;
}
</style>

<style scoped>
.main-content {
  margin: 0 !important;
}

.radio-group {
  display: flex;
  flex-direction: column;
}

.radio-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview {
  color: #0096ff;
  cursor: pointer;
}

.el-radio {
  width: 100%;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  text-align: center;
}
</style>
