<script setup lang="ts">
import { useRole } from "@/views/system/role/utils/hook";
import { ref, computed, nextTick, onMounted, watch } from "vue";
import { delay, subBefore, useResizeObserver } from "@pureadmin/utils";

import Close from "@iconify-icons/ep/close";
import Check from "@iconify-icons/ep/check";

const emit = defineEmits(["someEvent", "closeRoleMenu"]);

const props = defineProps({
  propIsShow: {
    type: Boolean,
    default: false
  },
  propRoleObj: {
    type: Object,
    default: () => ({})
  },
  tableRef: {
    type: Object,
    default: () => ({})
  }
});
const roleObj = ref(props.propRoleObj);

const handleClose = () => {
  // handleMenu({});
  emit("closeRoleMenu");
};

const iconClass = computed(() => {
  return [
    "w-[22px]",
    "h-[22px]",
    "flex",
    "justify-center",
    "items-center",
    "outline-none",
    "rounded-[4px]",
    "cursor-pointer",
    "transition-colors",
    "hover:bg-[#0000000f]",
    "dark:hover:bg-[#ffffff1f]",
    "dark:hover:text-[#ffffffd9]"
  ];
});

const treeRef = ref();
const tableRef = ref(props.tableRef);
const contentRef = ref();
const treeHeight = ref();

const {
  isShow,
  curRow,
  treeData,
  treeProps,
  isLinkage,
  isExpandAll,
  isSelectAll,
  treeSearchValue,
  handleMenu,
  handleSave,
  filterMethod,
  transformI18n,
  onQueryChanged,
  deviceDetection
} = useRole(treeRef);

onMounted(() => {
  useResizeObserver(treeRef, async () => {
    await nextTick();
    delay(60).then(() => {
      console.log(
        tableRef.value.getTableDoms().tableWrapper.style.height,
        "height"
      );

      treeHeight.value = parseFloat(
        subBefore(tableRef.value.getTableDoms().tableWrapper.style.height, "px")
      );
    });
  });
  emit("someEvent", treeRef.value);
});

watch(
  () => props.propRoleObj,
  newVal => {
    roleObj.value = newVal;

    if (treeRef.value) {
      emit("someEvent", treeRef.value); // 触发事件并传递 treeRef
    }
  }
);
isShow.value = props.propIsShow;

// nextTick(() => {
//   const timeoutId = setTimeout(() => {
//     // 在这里放置 3 秒后执行的代码
//     // isLinkage.value = false; // 启用父子联动
//
//     // 执行完毕后销毁 setTimeout，避免它再次执行
//     clearTimeout(timeoutId); // 清除定时器
//   }, 500); // 延迟 3000 毫秒（即 3 秒）
// });

const handleSaveClick = async roleObj => {
  await handleSave(roleObj);
  emit("closeRoleMenu");
};
</script>

<template>
  <div
    v-if="isShow"
    class="h-full px-2 bg-bg_color overflow-auto"
    :class="[
      roleObj?.roleType === 'individual' ? '' : 'mt-2',
      deviceDetection() ? 'mt-2' : 'pb-2 ml-2 ',
      isShow ? '!min-w-[calc(100vw-60vw-268px)]' : 'w-full'
    ]"
  >
    <div class="flex justify-between px-3 pt-5 pb-4">
      <div class="flex">
        <span :class="iconClass">
          <IconifyIconOffline
            v-tippy="{
              content: '关闭'
            }"
            class="dark:text-white"
            width="18px"
            height="18px"
            :icon="Close"
            @click="handleClose"
          />
        </span>
        <span :class="[iconClass, 'ml-2']">
          <IconifyIconOffline
            v-tippy="{
              content: '保存菜单权限'
            }"
            class="dark:text-white"
            width="18px"
            height="18px"
            :icon="Check"
            @click="handleSaveClick(roleObj)"
          />
        </span>
      </div>
      <p class="font-bold w-full truncate ml-20">
        {{ roleObj?.name }}
        {{ roleObj?.roleType === "individual" ? "的个人权限" : "菜单权限" }}
      </p>
    </div>
    <el-input
      v-model="treeSearchValue"
      placeholder="请输入菜单进行搜索"
      class="mb-1"
      clearable
      @input="onQueryChanged"
    />
    <div class="flex flex-wrap">
      <el-checkbox v-model="isExpandAll" label="展开/折叠" />
      <el-checkbox v-model="isSelectAll" label="全选/全不选" />
      <el-checkbox v-model="isLinkage" label="父子联动" />
    </div>
    <el-tree-v2
      v-if="isShow"
      ref="treeRef"
      show-checkbox
      :data="treeData"
      :props="treeProps"
      :default-checked-keys="roleObj.sysMenuIds"
      :height="treeHeight"
      :check-strictly="!isLinkage"
      :filter-method="filterMethod"
      @check-change="nodeClick"
    >
      <template #default="{ node }">
        <span>{{ transformI18n(node.label) }}</span>
      </template>
    </el-tree-v2>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
