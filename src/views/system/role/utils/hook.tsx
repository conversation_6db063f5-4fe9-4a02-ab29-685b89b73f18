import dayjs from "dayjs";
import editForm from "../form.vue";
import { handleTree } from "@/utils/tree";
import { message } from "@/utils/message";
// import { ElMessageBox } from "element-plus";
import { usePublicHooks } from "../../hooks";
import { transformI18n } from "@/plugins/i18n";
import { addDialog } from "@/components/ReDialog";
import type { FormItemProps } from "../utils/types";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection } from "@pureadmin/utils";
import { getMenuList, editRoleMenu, editUserMenu } from "@/api/system/menu";
import {
  getRoleList,
  delRoleList,
  addRoleList,
  updateRoleList
} from "@/api/system/role";
import { type Ref, reactive, ref, onMounted, h, toRaw, watch } from "vue";
import { getKeyList } from "@pureadmin/utils";
export function useRole(treeRef: Ref) {
  const form = reactive({
    name: "",
    code: "",
    status: ""
  });
  const curRow = ref();
  const formRef = ref();
  const dataList = ref([]);
  const treeIds = ref([]);
  const treeData = ref([]);
  const isShow = ref(false);
  const loading = ref(true);
  const isLinkage = ref(false);
  const treeSearchValue = ref();
  // const switchLoadMap = ref({});
  const isExpandAll = ref(false);
  const isSelectAll = ref(false);
  // const { switchStyle } = usePublicHooks();
  const treeProps = {
    value: "id",
    label: "title",
    children: "children"
  };
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 20,
    pageNum: 1,
    background: true,
    currentPage: 1
  });
  const { tagStyle } = usePublicHooks();
  const columns: TableColumnList = [
    {
      label: "角色编号",
      prop: "id",
      resizable: true
    },
    {
      label: "角色名称",
      prop: "name",
      minWidth: 200
    },
    {
      label: "角色标识",
      prop: "code",
      minWidth: 200
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} style={tagStyle.value(row.status)}>
          {row.status === true ? "启用" : "禁用"}
        </el-tag>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 160
    },
    {
      label: "创建时间",
      prop: "created",
      minWidth: 160,
      formatter: ({ created }) => dayjs(created).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 210,
      slot: "operation"
    }
  ];
  // const buttonClass = computed(() => {
  //   return [
  //     "!h-[20px]",
  //     "reset-margin",
  //     "!text-gray-500",
  //     "dark:!text-white",
  //     "dark:hover:!text-primary"
  //   ];
  // });

  // function onChange({ row, index }) {
  //   ElMessageBox.confirm(
  //     `确认要<strong>${
  //       row.status === 0 ? "停用" : "启用"
  //     }</strong><strong style='color:var(--el-color-primary)'>${
  //       row.name
  //     }</strong>吗?`,
  //     "系统提示",
  //     {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //       dangerouslyUseHTMLString: true,
  //       draggable: true
  //     }
  //   )
  //     .then(() => {
  //       switchLoadMap.value[index] = Object.assign(
  //         {},
  //         switchLoadMap.value[index],
  //         {
  //           loading: true
  //         }
  //       );
  //       setTimeout(() => {
  //         switchLoadMap.value[index] = Object.assign(
  //           {},
  //           switchLoadMap.value[index],
  //           {
  //             loading: false
  //           }
  //         );
  //         message(`已${row.status === 0 ? "停用" : "启用"}${row.name}`, {
  //           type: "success"
  //         });
  //       }, 300);
  //     })
  //     .catch(() => {
  //       row.status === 0 ? (row.status = 1) : (row.status = 0);
  //     });
  // }

  async function handleDelete(row) {
    await delRoleList(row.id).then(res => {
      if (res.code === 200) {
        message(`您删除了角色id为${row.id}的这条数据`, { type: "success" });
      } else {
        message("角色删除失败", { type: "error" });
      }
    });
    onSearch();
  }

  // function handleSizeChange(val: number) {
  //   console.log(`${val} items per page`);
  // }

  // function handleCurrentChange(val: number) {
  //   console.log(`current page: ${val}`);
  // }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.pageNum = 1;
    console.log(`${val} items per page`);
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.pageNum = val;
    onSearch();
    console.log("val-------------", val);
    console.log("pagination------------", pagination);
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  async function onSearch() {
    loading.value = true;
    const queryParams = {
      ...toRaw(form),
      pageSize: pagination.pageSize,
      pageCount: pagination.currentPage,
      pageNum: pagination.pageNum
    };
    const { data } = await getRoleList(queryParams);
    console.log("角色管理------", data);

    dataList.value = data.resultList;
    pagination.total = data.totalNum;
    pagination.pageSize = data.pageSize;
    pagination.currentPage = data.currentPage;
    pagination.pageNum = data.pageNum;
    console.log("dataList---------", dataList);
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  function openDialog(title = "新增", row?: FormItemProps) {
    addDialog({
      title: `${title}角色`,
      props: {
        formInline: {
          name: row?.name ?? "",
          remark: row?.remark ?? "",
          code: row?.code ?? "",
          id: row?.id ?? "",
          status: row?.status ?? true,
          isEdit: title === "修改" // 根据标题判断是否为编辑模式
        }
      },
      width: "40%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;
        function chores() {
          message(`您${title}了角色名称为${curData.name}的这条数据`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(async valid => {
          if (valid) {
            console.log("curData", curData);
            // 表单规则校验通过
            if (title === "新增") {
              // 实际开发先调用新增接口，再进行下面操作
              await addRoleList(curData).then(res => {
                if (res.code === 200) {
                  chores();
                } else {
                  message("角色新增失败", { type: "error" });
                }
              });
              // chores();
            } else {
              // 实际开发先调用修改接口，再进行下面操作
              await updateRoleList(curData).then(res => {
                if (res.code === 200) {
                  chores();
                } else {
                  message("角色修改失败", { type: "error" });
                }
              });
              // chores();
            }
          }
        });
      }
    });
  }

  /** 菜单权限 */
  async function handleMenu(row?: any) {
    const { id } = row;

    if (id) {
      curRow.value = row;
      console.log(curRow.value, "curRow.value");
      isShow.value = true;
      treeRef.value!.setCheckedKeys(row.sysMenuIds);
    } else {
      curRow.value = null;
      isShow.value = false;
    }
  }

  /** 高亮当前权限选中行 */
  function rowStyle({ row: { id } }) {
    return {
      cursor: "pointer",
      background: id === curRow.value?.id ? "var(--el-fill-color-light)" : ""
    };
  }

  /** 菜单权限-保存 */
  async function handleSave(roleObj) {
    // console.log(roleObj.sysMenuIds, "roleObj");

    const { id, name, roleType } = roleObj;
    // 根据用户 id 调用实际项目中菜单权限修改接口
    // console.log(roleObj, "data");

    if (roleType == "role") {
      const selLimit = [
        ...treeRef.value.getCheckedKeys(),
        ...treeRef.value.getHalfCheckedKeys()
      ];
      console.log(selLimit, "selLimit");

      if (selLimit.includes("200")) {
        selLimit.push("202");
      }
      const res = await editRoleMenu({
        roleId: id,
        sysMenuId: selLimit
      });

      onSearch();
      if (res.code == 200) {
        isShow.value = false;
        message(`角色名称为 ${name} 的菜单权限修改成功`, {
          type: "success"
        });
        roleObj.sysMenuIds = [
          ...treeRef.value.getCheckedKeys(),
          ...treeRef.value.getHalfCheckedKeys()
        ];
      }
    } else {
      const selLimit = [
        ...treeRef.value.getCheckedKeys(),
        ...treeRef.value.getHalfCheckedKeys()
      ];
      if (selLimit.includes("200")) {
        selLimit.push("206");
      }
      const res = await editUserMenu({
        userId: id,
        sysMenus: selLimit
      });
      // roleObj.sysMenuIds = treeRef.value.getCheckedKeys();
      onSearch();
      if (res.code == 200) {
        message(`用户名称为 ${name} 的菜单权限修改成功`, {
          type: "success"
        });
        roleObj.sysMenuIds = [
          ...treeRef.value.getCheckedKeys(),
          ...treeRef.value.getHalfCheckedKeys()
        ];
      }
    }
  }

  /** 数据权限  */
  // function handleDatabase() {}

  const onQueryChanged = (query: string) => {
    treeRef.value!.filter(query);
  };

  const filterMethod = (query: string, node) => {
    return transformI18n(node.title)!.includes(query);
  };

  /** 执行数据查询 */
  onMounted(async () => {
    onSearch();
    // 个人权限tree数据处理
    const { data } = await getMenuList({ pageNum: 1, pageSize: 1000 });
    // console.log(data.resultList, "data");

    treeIds.value = getKeyList(data.resultList, "id");
    // console.log(treeIds.value, "treeIds.value");
    data.resultList = data.resultList.filter(item => item.id !== "202");
    treeData.value = handleTree(data.resultList);
  });

  watch(isExpandAll, val => {
    val
      ? treeRef.value.setExpandedKeys(treeIds.value)
      : treeRef.value.setExpandedKeys([]);
  });

  watch(isSelectAll, val => {
    val
      ? treeRef.value.setCheckedKeys(treeIds.value)
      : treeRef.value.setCheckedKeys([]);
  });

  return {
    form,
    isShow,
    curRow,
    loading,
    columns,
    rowStyle,
    dataList,
    treeData,
    treeProps,
    isLinkage,
    pagination,
    isExpandAll,
    isSelectAll,
    treeSearchValue,
    // buttonClass,
    onSearch,
    resetForm,
    openDialog,
    handleMenu,
    handleSave,
    handleDelete,
    filterMethod,
    transformI18n,
    onQueryChanged,
    // handleDatabase,
    deviceDetection,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
