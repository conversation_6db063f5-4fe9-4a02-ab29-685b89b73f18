import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { isPhone, isEmail } from "@pureadmin/utils";

/** 自定义表单规则校验 */
export const formRules = reactive(<FormRules>{
  nickname: [{ required: true, message: "用户昵称为必填项", trigger: "blur" }],
  name: [{ required: true, message: "用户姓名为必填项", trigger: "blur" }],
  // password: [{ required: true, message: "用户密码为必填项", trigger: "blur" }],
  // parentId: [{ required: true, message: "归属部门为必选项", trigger: "blur" }],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback();
        } else if (!isPhone(value)) {
          callback(new Error("请输入正确的手机号码格式"));
        } else {
          callback();
        }
      },
      trigger: "blur"
      // trigger: "click" // 如果想在点击确定按钮时触发这个校验，trigger 设置成 click 即可
    }
  ],
  email: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback();
        } else if (!isEmail(value)) {
          callback(new Error("请输入正确的邮箱格式"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  username: [
    {
      required: true,
      message: "用户账号为必填项",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        // 校验是否包含中文字符或特殊字符
        const chineseRegex = /[\u4e00-\u9fa5]/;
        const specialCharRegex = /[^a-zA-Z0-9]/;

        if (value.length < 2 || value.length > 15) {
          callback(new Error("用户账号长度在2-15位"));
        } else if (chineseRegex.test(value)) {
          callback(new Error("用户账号不能包含中文字符"));
        } else if (specialCharRegex.test(value)) {
          callback(new Error("用户账号不能包含特殊字符"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  // parentId: [
  //   {
  //     required: true,
  //     message: "归属部门为必选项",
  //     trigger: "blur"
  //   },
  //   {
  //     validator: (rule, value, callback) => {
  //       if (value === 0) {
  //         callback(new Error("归属部门为必选项"));
  //       } else {
  //         callback();
  //       }
  //     }
  //   }
  // ],
  password: [
    {
      required: true,
      message: "用户密码为必填项",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        // 检查密码长度是否在8到16位之间
        if (value.length < 8 || value.length > 16) {
          callback(new Error("密码长度8-16位"));
        } else {
          // 检查密码是否只包含允许的字符
          const allowedCharsRegex = /^[0-9a-zA-Z!@#$%^&*()_+]+$/;
          if (!allowedCharsRegex.test(value)) {
            callback(new Error("密码格式错误"));
          } else {
            console.log("规则");

            callback();
          }
        }
      },
      trigger: "blur"
    }
  ]
});
