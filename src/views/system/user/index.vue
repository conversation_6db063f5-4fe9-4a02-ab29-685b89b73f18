<script setup lang="ts">
import PermissionMenu from "@/views/system/role/components/PermissionMenu.vue";

import { useRole } from "@/views/system/role/utils/hook";
import { ref, nextTick, toRaw } from "vue";
import tree from "./tree.vue";
import { useUser } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Upload from "@iconify-icons/ri/upload-line";
import Role from "@iconify-icons/ri/admin-line";
import Permissions from "@iconify-icons/ri/shield-user-line";
import Password from "@iconify-icons/ri/lock-password-line";
import More from "@iconify-icons/ep/more-filled";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import Refresh from "@iconify-icons/ep/refresh";
import AddFill from "@iconify-icons/ri/add-circle-line";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import exportToExcel from "@/utils/exportXlsx";
import { message } from "@/utils/message";

const { t } = useI18n(); // 解构出t方法
defineOptions({
  name: "SystemUser"
});
const treeRefRole = ref();
const treeRef = ref();
const formRef = ref();
const tableRef = ref();
const roleObj = ref();

const {
  form2,
  form,
  loading,
  columns,
  dataList,
  treeData,
  treeLoading,
  selectedNum,
  selectedrow,
  pagination,
  buttonClass,
  roleOptions,
  deviceDetection,
  onSearch,
  resetForm,
  onbatchDel,
  openDialog,
  onTreeSelect,
  handleUpdate,
  handleDelete,
  handleUpload,
  handleReset,
  handleRole,
  handleSizeChange,
  onSelectionCancel,
  handleCurrentChange,
  handleSelectionChange
} = useUser(tableRef, treeRef);

const {
  isShow,
  curRow,
  rowStyle,
  treeProps,
  isLinkage,
  isExpandAll,
  isSelectAll,
  treeSearchValue,
  // buttonClass,
  handleMenu,
  handleSave,
  filterMethod,
  transformI18n,
  onQueryChanged
} = useRole(treeRefRole);

const handleRoleMenu = async row => {
  console.log(row, "row");
  roleObj.value = row;
  roleObj.value.roleType = "individual";
  roleObj.value.name = row.name;
  // handleMenu(roleObj.value);
  await nextTick();
  // 以下代码可解决bug
  handleMenu(roleObj.value);
};
const handleSomeEvent = treeRefs => {
  treeRefRole.value = treeRefs;
};

/**
 * 额外一层方法，用于搜索后重置当前页码
 */
const querySearch = async () => {
  pagination.currentPage = 1;
  pagination.pageSize = 20;

  await onSearch();

  // 重置 transform 和 height
  const tabTopScroll = document.querySelectorAll(".el-scrollbar__thumb");
  console.log(tabTopScroll[1].scrollTop, "sc");
  tabTopScroll.style.transform = "translateY(0%)"; // 重置滚动位置
};
const handleExportExcel = () => {
  ElMessageBox.confirm(
    `确认要导出<strong>${selectedrow.value.length > 0 ? "所勾选的数据" : "所筛选的数据"}</strong>吗？`,
    "系统提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  )
    .then(async () => {
      try {
        const filteredColumns = columns.filter(column => {
          return (
            !["勾选列", "序号", "操作", "dept.name", "用户头像"].includes(
              column.label
            ) && column.prop !== "dept.name"
          );
        });
        console.log("selectedrow", selectedrow);

        if (selectedrow.value.length > 0) {
          console.log("123selectedrow");
        }
        const processedData =
          selectedrow.value.length > 0
            ? selectedrow.value.map(row => ({
                ...row,
                dept: row.dept.name
              }))
            : [];

        if (selectedrow.value.length > 0) {
          console.log("勾选数据导出");

          filteredColumns.push({
            label: "部门",
            prop: "dept"
          });
          console.log("勾选数据列字段导出", filteredColumns);
          console.log("勾选数据导出", processedData);
          exportToExcel(filteredColumns, processedData, "表格数据");
          message("勾选数据导出成功", { type: "success" });
        }
      } catch (error) {
        console.error("导出失败:", error);
        message("导出失败，请重试", { type: "error" });
      }
    })
    .catch(() => {
      console.log("用户取消退出");
      // 用户取消导出
    });
};
</script>

<template>
  <div
    :class="
      isShow
        ? ''
        : [
            'flex',
            'h-full',
            'justify-between',
            deviceDetection() && 'flex-wrap'
          ]
    "
  >
    <!--    <tree-->
    <!--      v-if="!isShow"-->
    <!--      ref="treeRef"-->
    <!--      :class="[-->
    <!--        'mr-2',-->
    <!--        deviceDetection() ? 'w-full' : 'min-w-[200px]',-->
    <!--        'h-full'-->
    <!--      ]"-->
    <!--      :treeData="treeData"-->
    <!--      :treeLoading="treeLoading"-->
    <!--      @tree-select="onTreeSelect"-->
    <!--    />-->
    <!--    <div-->
    <!--      :class="[-->
    <!--        deviceDetection()-->
    <!--          ? ['w-full', 'mt-2']-->
    <!--          : isShow-->
    <!--            ? ''-->
    <!--            : 'w-[calc(100%-200px)]'-->
    <!--      ]"-->
    <!--    >-->
    <div class="w-full">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        :class="isShow ? 'w-full' : ''"
        class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px] overflow-auto"
      >
        <el-form-item :label="$t('labels.pureUsername') + '：'" prop="name">
          <el-input
            v-model="form2.name"
            :placeholder="$t('login.pureNameReg')"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <el-form-item :label="$t('labels.pureNickname') + '：'" prop="username">
          <el-input
            v-model="form2.username"
            :placeholder="$t('login.pureNickNameReg')"
            clearable
            class="!w-[180px]"
          />
        </el-form-item>
        <!--        <el-form-item :label="$t('login.purePhone') + '：'" prop="phone">-->
        <!--          <el-input-->
        <!--            v-model="form2.phone"-->
        <!--            :placeholder="$t('login.purePhoneReg')"-->
        <!--            clearable-->
        <!--            class="!w-[180px]"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item :label="$t('login.pureRole') + '：'" prop="phone">
          <el-select
            v-model="form2.roleId"
            :placeholder="$t('login.pureRoleName')"
            class="!w-[180px]"
            clearable
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('login.pureDept') + '：'" prop="phone">
          <el-tree-select
            v-model="form2.deptId"
            :data="treeData"
            clearable
            :props="{
              value: 'id',
              label: 'name',
              children: 'children'
            }"
            check-strictly
            :render-after-expand="false"
            class="!w-[280px]"
          />
        </el-form-item>
        <el-form-item :label="$t('labels.pureStatus') + '：'" prop="status">
          <el-select
            v-model="form2.status"
            :placeholder="$t('login.pureSelect')"
            clearable
            class="!w-[180px]"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            @click="querySearch"
          >
            {{ $t("buttons.pureSearch") }}
          </el-button>
          <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
            {{ $t("buttons.pureReset") }}
          </el-button>
        </el-form-item>
      </el-form>
      <div :class="['flex', 'w-full', deviceDetection() ? 'flex-wrap' : '']">
        <PureTableBar
          :class="[isShow && !deviceDetection() ? '!w-[60vw]' : 'w-full']"
          :title="$t('menus.pureUser')"
          :columns="columns"
          @refresh="onSearch"
        >
          <template #buttons>
            <el-button
              type="primary"
              :icon="useRenderIcon(AddFill)"
              @click="openDialog()"
            >
              {{ $t("buttons.pureAddingnewusers") }}
            </el-button>
          </template>
          <template v-slot="{ size, dynamicColumns }">
            <div
              v-if="selectedNum > 0"
              v-motion-fade
              class="bg-[var(--el-fill-color-light)] w-full h-[46px] mb-2 pl-4 flex items-center"
            >
              <div class="flex-auto">
                <span
                  style="font-size: var(--el-font-size-base)"
                  class="text-[rgba(42,46,54,0.5)] dark:text-[rgba(220,220,242,0.5)]"
                >
                  已选 {{ selectedNum }} 项
                </span>
                <el-button type="primary" text @click="onSelectionCancel">
                  取消选择
                </el-button>
              </div>
              <!-- <el-popconfirm title="是否确认删除?" @confirm="onbatchDel()">
                <template #reference> -->
              <el-button
                type="danger"
                text
                class="mr-1"
                @click="handleExportExcel"
              >
                excel导出
              </el-button>
              <!-- </template>
              </el-popconfirm> -->
            </div>
            <pure-table
              ref="tableRef"
              row-key="id"
              adaptive
              :adaptiveConfig="{ offsetBottom: 108 }"
              align-whole="center"
              table-layout="auto"
              :loading="loading"
              :size="size"
              :border="true"
              :data="dataList"
              :columns="dynamicColumns"
              :pagination="pagination"
              :paginationSmall="size === 'small' ? true : false"
              :header-cell-style="{
                background: 'var(--el-fill-color-light)',
                color: 'var(--el-text-color-primary)'
              }"
              @selection-change="handleSelectionChange"
              @page-size-change="handleSizeChange"
              @page-current-change="handleCurrentChange"
            >
              <template #operation="{ row }">
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(EditPen)"
                  @click="openDialog('修改', row)"
                >
                  {{ $t("buttons.pureModification") }}
                </el-button>
                <el-popconfirm
                  :title="`是否确认删除用户姓名为${row.name}的这条数据`"
                  @confirm="handleDelete(row)"
                >
                  <template #reference>
                    <el-button
                      class="reset-margin"
                      link
                      type="primary"
                      :size="size"
                      :icon="useRenderIcon(Delete)"
                    >
                      {{ $t("buttons.pureDel") }}
                    </el-button>
                  </template>
                </el-popconfirm>
                <el-dropdown>
                  <el-button
                    class="ml-3 mt-[2px]"
                    link
                    type="primary"
                    :size="size"
                    :icon="useRenderIcon(More)"
                    @click="handleUpdate(row)"
                  />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <el-button
                          :class="buttonClass"
                          link
                          type="primary"
                          :size="size"
                          :icon="useRenderIcon(Upload)"
                          @click="handleUpload(row)"
                        >
                          上传头像
                        </el-button>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <el-button
                          :class="buttonClass"
                          link
                          type="primary"
                          :size="size"
                          :icon="useRenderIcon(Password)"
                          @click="handleReset(row)"
                        >
                          重置密码
                        </el-button>
                      </el-dropdown-item>
                      <!--                      <el-dropdown-item>-->
                      <!--                        <el-button-->
                      <!--                          :class="buttonClass"-->
                      <!--                          link-->
                      <!--                          type="primary"-->
                      <!--                          :size="size"-->
                      <!--                          :icon="useRenderIcon(Role)"-->
                      <!--                          @click="handleRole(row)"-->
                      <!--                        >-->
                      <!--                          分配角色-->
                      <!--                        </el-button>-->
                      <!--                      </el-dropdown-item>-->
                      <!-- <el-dropdown-item>
                        <el-button
                          :class="buttonClass"
                          link
                          type="primary"
                          :size="size"
                          :icon="useRenderIcon(Permissions)"
                          @click="handleRoleMenu(row)"
                        >
                          个人权限
                        </el-button>
                      </el-dropdown-item> -->
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </pure-table>
          </template>
        </PureTableBar>
        <!-- 菜单权限 -->
        <PermissionMenu
          v-if="isShow"
          class="mt-2"
          :propIsShow="isShow"
          :propRoleObj="roleObj"
          :tableRef="tableRef"
          @closeRoleMenu="handleMenu({})"
          @someEvent="handleSomeEvent"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

:deep(.el-button:focus-visible) {
  outline: none;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
