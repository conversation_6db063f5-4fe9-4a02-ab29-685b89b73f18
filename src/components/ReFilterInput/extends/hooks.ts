/**
 * useConditions Hook
 * 用于生成条件列表，根据不同字段类型展示不同的条件
 */
export const useConditions = () => {
  // 定义条件类型数组(根据后端字段类型type)
  const conditionTypes = ["varchar", "enum", "datetime", "double", "int"];
  // 定义条件标签数组
  const conditionLabels = [
    "包含",
    "等于",
    "不等于",
    "小于等于",
    "大于等于",
    "大于",
    "小于",
    "不包含",
    "左包含",
    "集合"
  ];

  /**
   * 根据条件类型和标签生成条件列表
   * @param types 条件类型数组
   * @param labels 条件标签数组
   * @returns 条件列表
   */
  const generateConditions = (types: string[], labels: string[]) => {
    return types
      .flatMap(type =>
        labels.map(label => {
          // 根据标签生成对应的值
          let value: string;
          switch (label) {
            case "等于":
              value = "eq";
              break;
            case "不等于":
              value = "ne";
              break;
            case "小于等于":
              value = "le";
              break;
            case "大于等于":
              value = "ge";
              break;
            case "大于":
              value = "gt";
              break;
            case "小于":
              value = "lt";
              break;
            case "包含":
              value = "like";
              break;
            case "不包含":
              value = "notLike";
              break;
            case "左包含":
              value = "leftLike";
              break;
            case "集合":
              value = "in";
              break;
            default:
              value = "";
          }
          return { label, value, type };
        })
      )
      .filter(condition => {
        // 根据字段类型过滤条件 为true返回全部、如果return[].includes(condition.value);表示只展示对应的
        if (condition.type === "varchar") return true;
        if (condition.type === "enum")
          return ["eq", "ne", "leftLike"].includes(condition.value);
        if (condition.type === "datetime")
          return [
            "like",
            "eq",
            "ge",
            "gt",
            "notLike",
            "lt",
            "le",
            "ne",
            "leftLike"
          ].includes(condition.value);
        if (["double", "int"].includes(condition.type))
          return ["eq", "ne", "le", "ge", "gt", "lt", "leftLike"].includes(
            condition.value
          );
        return false;
      });
  };
  // 生成条件列表
  const conditions = generateConditions(conditionTypes, conditionLabels);
  return {
    conditions
  };
};
