<script setup lang="tsx">
import { ref, watch, computed, nextTick } from "vue";
import { Search, Tools } from "@element-plus/icons-vue";
import QualityInput from "./qualityInput.vue";
import dayjs from "dayjs";
// 导入hook
import { useConditions } from "./extends/hooks";

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  // 用于下拉框默认下标
  index: {
    type: Number,
    default: 0
  },
  enterFilter: {
    type: Function,
    required: true
  },
  /** 根据定义的枚举值进行字段搜索类型区分  * @type {Array} */
  mappingStatus: {
    type: Array,
    default: () => []
  }
});

/**
 * @selectedField 可选择的字段
 * @selectedCondition 选择字段对应的筛选条件
 * @inputValue 输入框内容
 * @inputValueTime 时间选择器 发现与inputValue同用会造成数据污染
 * @mappingStatusDefault 状态选择器 发现与inputValue同用会造成数据污染
 * @hasExecuted 确保watch监听到列有数据后只执行一次
 */
const selectedField = ref("");
const selectedCondition = ref("");
const inputValue = ref("");
const inputValueTime = ref("");
const mappingStatusDefault = ref("");
let hasExecuted = false;

/**
 * @function 时间格式化
 * @param date 后端时间
 */
const formatDate1 = date => {
  inputValue.value = dayjs(date).format("YYYY-MM-DD");
};

/**
 * @function 匹配到的列type类型
 * @param field 根据字段类型展示对应的筛选框
 */
const getFieldType = field => {
  const column = props.columns.find(col => col.prop === field);
  if (column) {
    if (column.type === null || column.type === undefined) {
      return "";
    } else {
      if (column.type.match(/varchar/i)) {
        return "varchar";
      } else if (column.type.match(/datetime/i)) {
        return "datetime";
      } else if (column.type.match(/enum/i)) {
        return "enum";
      } else if (column.type.match(/double/i)) {
        return "double";
      } else if (column.type.match(/int/i)) {
        return "int";
      }
    }
  }
  return "";
};

/**
 *
 * @param field 解析 enum 类型字段选项
 */
const parseEnumOptions = field => {
  const column = props.columns.find(col => col.prop === field);
  if (column && column.type.match(/enum/i)) {
    const enumMatch = column.type.match(/enum\((.+)\)/);
    if (enumMatch) {
      return enumMatch[1]
        .split(",")
        .map(option => option.trim().replace(/'/g, ""));
    }
  }
  return [];
};

const { conditions } = useConditions();

// 根据字段类型过滤条件
const filteredConditions1 = computed(() => {
  const fieldType1 = getFieldType(selectedField.value);
  return conditions.filter(condition => condition.type === fieldType1);
});

// 更新过滤后的条件数组
const updateFilteredConditions = (selectedField, filteredConditions) => {
  const fieldType = getFieldType(selectedField.value);
  return conditions.filter(condition => condition.type === fieldType);
};

watch(
  () => props.columns,
  newColumns => {
    if (newColumns && newColumns.length > 0 && !hasExecuted) {
      hasExecuted = true;
      selectedField.value = newColumns[props.index].prop;
      selectedCondition.value = filteredConditions1?.value[0]?.value;
      updateFilteredConditions(selectedField, filteredConditions1);
    }
  }
);

// 初始化过滤条件
updateFilteredConditions(selectedField, filteredConditions1);

const selVal = ref({
  selectedField1: selectedField,
  selectedCondition1: selectedCondition,
  inputValue1: inputValue
});
/** @function 获取子组件的筛选数据 */
const getChildrenSelVal = () => {
  return selVal.value;
};

/** @function 清空子组件的筛选数据
 * @param {number} index - 子组件的索引
 */
const clearChildrenSelVal = index => {
  // selectedField.value = props.columns[index].prop;
  // selectedCondition.value = filteredConditions1.value[0].value;
  mappingStatusDefault.value = "";
  inputValue.value = "";
  inputValueTime.value = "";
};
/**
 * @function 设置筛选框的值
 * @param {string} field - 字段名
 * @param {string} condition - 条件
 * @param {string} value - 值
 */
const setFilterValues = (field, condition, value) => {
  console.log("setFilterValues被调用:", field, condition, value);

  // 确保字段存在于columns中
  const fieldExists = props.columns.some(col => col.prop === field);
  if (!fieldExists) {
    console.warn("字段不存在于columns中:", field);
    return;
  }

  // 设置字段
  selectedField.value = field;
  console.log("设置字段:", selectedField.value);

  // 强制更新条件列表
  setTimeout(() => {
    // 更新条件列表
    filteredConditions1.value = conditions.filter(
      c => c.type === getFieldType(field)
    );
    console.log("更新条件列表:", filteredConditions1.value);

    // 设置条件
    // 确保条件存在于条件列表中
    const conditionExists = filteredConditions1.value.some(
      c => c.value === condition
    );
    if (conditionExists) {
      selectedCondition.value = condition;
    } else if (filteredConditions1.value.length > 0) {
      // 如果条件不存在，使用第一个条件
      selectedCondition.value = filteredConditions1.value[0].value;
    }
    console.log("设置条件:", selectedCondition.value);

    // 设置值
    if (getFieldType(field) === "datetime") {
      try {
        // 尝试解析日期
        const date = dayjs(value).toDate();
        inputValueTime.value = date;
        formatDate1(date);
        console.log("设置日期值:", inputValueTime.value, inputValue.value);
      } catch (error) {
        console.error("日期解析失败:", error);
        inputValue.value = value;
      }
    } else if (getMappingStatus(field)) {
      mappingStatusDefault.value = value;
      inputValue.value = value;
      console.log(
        "设置映射状态值:",
        mappingStatusDefault.value,
        inputValue.value
      );
    } else {
      inputValue.value = value;
      console.log("设置普通值:", inputValue.value);
    }

    // 更新selVal
    selVal.value = {
      selectedField1: selectedField.value,
      selectedCondition1: selectedCondition.value,
      inputValue1: inputValue.value
    };
    console.log("更新selVal:", selVal.value);

    // 手动触发一次更新，确保UI更新
    nextTick(() => {
      console.log("UI已更新");
    });
  }, 100); // 使用setTimeout确保DOM已更新
};

// 向父组件暴露条件
defineExpose({
  clearChildrenSelVal,
  getChildrenSelVal,
  setFilterValues
});
// 字段下拉框选中事件
const selChange = val => {
  inputValue.value = "";
  // 修改字段选择后条件表单不更新
  selectedCondition.value = filteredConditions1.value[0].value;
};

/**
 * 将枚举的键进行判断，相当于提取枚举键，根据枚举的键展示中文
 */
const handleChangeMap = val => {
  if (val === "" || val == undefined || val == null)
    return (inputValue.value = "");
  if (val == "true" || val == "false") {
    inputValue.value = val === "true" ? 1 : 0;
    mappingStatusDefault.value = val;
  } else {
    inputValue.value = val;
    mappingStatusDefault.value = val;
  }
};

/**
 * 如果匹配列的键和该枚举名字的对象属性名进行匹配，匹配到了提取里面的中文
 */
const getMappingStatus = prop => {
  if (!Array.isArray(props.mappingStatus) || props.mappingStatus.length === 0) {
    // console.error("Table.vue props.mappingStatus 为空");
    return false;
  }

  const mappingItem = props.mappingStatus.find(item => item.name === prop);
  if (mappingItem) {
    const { name, ...rest } = mappingItem; // 提取 `name` 属性之外的所有属性
    return rest;
  }

  // console.warn(`No matching item found for prop: ${prop}`);
  return false;
};
</script>

<template>
  <div class="filter-set">
    <el-select
      v-model="selectedField"
      placeholder="选择字段"
      class="filter-element"
      filterable
      @change="selChange"
    >
      <el-option
        v-for="column in columns"
        :key="column.prop"
        :label="column.label"
        :value="column.prop"
      />
    </el-select>
    <el-select
      v-model="selectedCondition"
      placeholder="选择条件"
      class="filter-element-type"
    >
      <el-option
        v-for="condition in filteredConditions1"
        :key="condition.value"
        :label="condition.label"
        :value="condition.value"
      />
    </el-select>
    <div v-if="getFieldType(selectedField) === 'varchar'">
      <el-input
        v-model="inputValue"
        :placeholder="$t('login.pureKeyWords')"
        class="filter-element"
        clearable
        @keydown.enter="enterFilter"
      />
    </div>
    <div v-else-if="getFieldType(selectedField) === 'double'">
      <el-input
        v-model="inputValue"
        placeholder="输入数量"
        class="filter-element"
        type="number"
        clearable
        @keydown.enter="enterFilter"
      />
    </div>
    <div v-else-if="getFieldType(selectedField) === 'datetime'">
      <el-date-picker
        v-model="inputValueTime"
        type="date"
        placeholder="选择日期"
        class="filter-element"
        @change="formatDate1"
        @keydown.enter="enterFilter"
      />
    </div>
    <div v-else-if="getFieldType(selectedField) === 'enum'">
      <el-select
        v-model="inputValue"
        placeholder="选择状态"
        class="filter-element"
      >
        <el-option
          v-for="option in parseEnumOptions(selectedField)"
          :key="option"
          :label="option"
          :value="option"
        />
      </el-select>
    </div>

    <div v-else-if="getMappingStatus(selectedField)">
      <el-select
        v-model="mappingStatusDefault"
        clearable
        class="filter-element"
        @change="val => handleChangeMap(val)"
      >
        <el-option
          v-for="(label, value) in getMappingStatus(selectedField)"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>
    </div>
    <div v-else>
      <el-input
        v-model="inputValue"
        :placeholder="$t('login.pureKeyWords')"
        class="filter-element"
        @keydown.enter="enterFilter"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-button + .el-button) {
  margin-left: 0;
}

.custom-tooltip {
  cursor: pointer;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-component {
  display: flex;
  flex-flow: column wrap;
  gap: 10px;
  margin: 15px 0 0 15px;
}

.filter-set {
  display: flex;
  flex-wrap: wrap;
  // gap: 10px;
}

.filter-element {
  width: 150px;
}

.filter-element-type {
  width: 110px;
}

.filter-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  min-width: 120px;
}

@media (width <= 450px) {
  .flex {
    flex-flow: nowrap;
  }

  .filter-set,
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-element,
  .filter-element-type,
  .filter-actions {
    width: 100% !important;
  }
}
</style>
