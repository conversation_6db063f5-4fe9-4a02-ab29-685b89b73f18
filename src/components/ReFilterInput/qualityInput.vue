<template>
  <el-dialog
    v-model="isModalVisible"
    title="筛选条件设置"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    draggable
  >
    <div>
      <!-- 操作按钮 -->
      <div style="margin-bottom: 10px; text-align: left">
        <el-button size="small" @click="addRow">新增行</el-button>
        <el-button size="small" :disabled="!selectedRow" @click="deleteSelected"
          >删除行</el-button
        >
        <el-button
          size="small"
          :disabled="filterRows.length === 0"
          @click="clearAll"
          >全部删除</el-button
        >
        <el-button size="small" :disabled="!selectedRow" @click="insertRow"
          >插入行</el-button
        >
        <el-button size="small" :disabled="!selectedRow" @click="copyRow"
          >复制行</el-button
        >
      </div>

      <!-- 条件列表 -->
      <el-table
        :data="showFilterRows"
        style="width: 100%"
        border
        size="small"
        row-key="id"
        highlight-current-row
        @current-change="handleRowSelect"
      >
        <el-table-column type="index" label="#" align="center" />
        <el-table-column label="字段" align="center">
          <template #default="{ row }">
            <el-select
              v-model="row.name"
              placeholder="选择字段"
              filterable
              :disabled="row.disabled"
              @change="updateConditionAndQuery(row)"
            >
              <el-option
                v-for="option in fieldOptions"
                :key="option.prop"
                :label="option.label"
                :value="option.prop"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="条件" align="center">
          <template #default="{ row }">
            <el-select
              v-model="row.condition"
              placeholder="选择条件"
              :disabled="row.disabled"
            >
              <el-option
                v-for="condition in getFilteredConditions(
                  getFieldType(row.name)
                )"
                :key="condition.value"
                :label="condition.label"
                :value="condition.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="值" align="center">
          <template #default="{ row }">
            <template v-if="getMappingStatus(row.name)">
              <el-select
                v-model="row.query"
                clearable
                :disabled="row.disabled"
                @change="val => handleChangeMap(row, val)"
              >
                <el-option
                  v-for="(label, value) in getMappingStatus(row.name)"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </template>
            <template v-else-if="getFieldType(row.name) === 'enum'">
              <el-select
                v-model="row.query"
                placeholder="选择值"
                clearable
                :disabled="row.disabled"
              >
                <el-option
                  v-for="option in getEnumOptions(row.name)"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>
            </template>

            <template v-else>
              <component
                :is="getInputComponent(row.name)"
                v-model="row.query"
                placeholder="输入值"
                clearable
                :disabled="row.disabled"
              />
            </template>
          </template>
        </el-table-column>

        <el-table-column label="逻辑" align="center">
          <template #default="{ row }">
            <el-select
              v-model="row.logic"
              placeholder="选择逻辑"
              :disabled="row.disabled"
            >
              <el-option label="并且" value="and" />
              <el-option label="或者" value="or" />
            </el-select>
          </template>
        </el-table-column>
        <!-- 删除按钮列 -->
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-icon
              v-show="!row.disabled"
              style="font-size: large; color: cornflowerblue; cursor: pointer"
              @click="deleteRow(row.id)"
              ><Remove
            /></el-icon>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 操作按钮 -->
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, defineExpose } from "vue";
import { Remove } from "@element-plus/icons-vue";
import dayjs from "dayjs";
// 导入hook
import { useConditions } from "./extends/hooks";

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  /** 根据定义的枚举值进行字段搜索类型区分  * @type {Array} */
  mappingStatus: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(["update:visible"]);
const mappingStatusDefault = ref("");
// 控制模态框显示
const isModalVisible = ref(false);
// 展示条件列表=普通筛选条件+高级筛选条件
const showFilterRows = ref([]);

// 条件列表，初始化为空
const filterRows = ref([]);

// 普通筛选条件
const defaultFilters = ref([]);

const setDefaultFilters = filters => {
  defaultFilters.value = filters;
  updateFilterRows();
};

const updateFilterRows = () => {
  // 将默认筛选条件显示在高级筛选中
  showFilterRows.value = [...defaultFilters.value, ...filterRows.value];
};

updateFilterRows();

// 字段选项 (从 props.columns 数据中获取)
const fieldOptions = computed(() =>
  props.columns.map(col => ({ label: col.label, prop: col.prop }))
);

// 当前选中的行
const selectedRow = ref(null);

// 字段的默认选项
const defaultField =
  fieldOptions.value.length > 0 ? fieldOptions.value[0].prop : "";
const defaultCondition = ref("");
const defaultValue = "";
const defaultLogic = "and";

// 从hook中获取类型
const { conditions } = useConditions();

// 根据字段类型过滤条件
const getFilteredConditions = fieldType => {
  return conditions.filter(condition => condition.type === fieldType);
};

// 获取字段类型
const getFieldType = field => {
  const column = props.columns.find(col => col.prop === field);
  // console.log(column, "column");
  if (column) {
    if (column.type.match(/varchar/i)) {
      return "varchar";
    } else if (column.type.match(/datetime/i)) {
      return "datetime";
    } else if (column.type.match(/enum/i)) {
      return "enum";
    } else if (column.type.match(/double/i)) {
      return "double";
    } else if (column.type.match(/int/i)) {
      return "int";
    }
  }
  return "";
};

// 获取输入组件类型
const getInputComponent = field => {
  const fieldType = getFieldType(field);
  if (fieldType === "datetime") {
    return "el-date-picker";
  } else if (fieldType === "enum") {
    return "el-select";
  } else {
    return "el-input";
  }
};

// 获取枚举类型选项
const getEnumOptions = field => {
  const fieldType = getFieldType(field);
  if (fieldType === "enum") {
    const column = props.columns.find(col => col.prop === field);
    if (column && column.type.match(/enum/i)) {
      const enumMatch = column.type.match(/enum\((.+)\)/);
      if (enumMatch) {
        return enumMatch[1]
          .split(",")
          .map(option => option.trim().replace(/'/g, ""));
      }
    }
  }
  return [];
};

// 更新行条件和输入框类型
const updateConditionAndQuery = row => {
  row.condition = getFilteredConditions(getFieldType(row.name))[0]?.value || "";
  row.query = "";
};

// 添加新行，并设置默认值
const addRow = () => {
  const newRow = {
    id: Date.now(),
    name: defaultField,
    condition:
      getFilteredConditions(getFieldType(defaultField))[0]?.value || "",
    query: defaultValue,
    logic: defaultLogic
  };
  filterRows.value.push(newRow);
  selectedRow.value = newRow; // 默认选中新增的行
  updateFilterRows();
};

// 删除选中的行
const deleteSelected = () => {
  if (selectedRow.value) {
    const index = filterRows.value.findIndex(
      row => row.id === selectedRow.value.id
    );
    if (index !== -1) {
      filterRows.value.splice(index, 1);
      selectedRow.value = null; // 清空选中状态
    }
  }
  updateFilterRows();
};

// 删除特定行
const deleteRow = id => {
  const index = filterRows.value.findIndex(row => row.id === id);
  if (index !== -1) {
    filterRows.value.splice(index, 1);
  }
  updateFilterRows();
};

// 清空所有行
const clearAll = () => {
  filterRows.value.splice(0, filterRows.value.length);
  selectedRow.value = null; // 清空选中状态
  updateFilterRows();
};

// 插入行到选中行上方，并设置默认值
const insertRow = () => {
  if (selectedRow.value) {
    const index = filterRows.value.findIndex(
      row => row.id === selectedRow.value.id
    );
    if (index !== -1) {
      const newRow = {
        id: Date.now(),
        name: defaultField,
        condition:
          getFilteredConditions(getFieldType(defaultField))[0]?.value || "",
        query: defaultValue,
        logic: defaultLogic
      };
      filterRows.value.splice(index, 0, newRow);
      selectedRow.value = newRow; // 默认选中新增的行
    }
  }
  updateFilterRows();
};

// 复制选中行到下方
const copyRow = () => {
  if (selectedRow.value) {
    const index = filterRows.value.findIndex(
      row => row.id === selectedRow.value.id
    );
    if (index !== -1) {
      const newRow = { ...selectedRow.value, id: Date.now() };
      filterRows.value.splice(index + 1, 0, newRow);
      selectedRow.value = newRow; // 默认选中新增的行
    }
  }
  updateFilterRows();
};

// 行选中事件
const handleRowSelect = row => {
  selectedRow.value = row;
};

// 确定操作
const handleOk = () => {
  // 移除 name 为空的行
  filterRows.value = filterRows.value.filter(row => row.name);
  // 转换 Proxy 对象为普通对象，并格式化时间类型
  const plainFilterRows = filterRows.value.map(row => {
    const formattedRow = {
      name: row.name,
      condition: row.condition,
      query: row.query,
      logic: row.logic
    };
    if (getFieldType(row.name) === "datetime" && row.query) {
      formattedRow.query = formatDate(row.query);
    }
    return formattedRow;
  });
  emits("update:visible", plainFilterRows);

  isModalVisible.value = false;
};

// 取消操作
const handleCancel = () => {
  isModalVisible.value = false;
};

const showModal = () => {
  isModalVisible.value = true;
};

// 格式化日期
const formatDate = date => {
  return dayjs(date).format("YYYY-MM-DD");
};
/**
 * 将枚举的键进行判断，相当于提取枚举键，根据枚举的键展示中文
 */
const getMappingStatus = prop => {
  if (!Array.isArray(props.mappingStatus) || props.mappingStatus.length === 0) {
    // console.error("Table.vue props.mappingStatus 为空");
    return false;
  }

  const mappingItem = props.mappingStatus.find(item => item.name === prop);
  if (mappingItem) {
    const { name, ...rest } = mappingItem; // 提取 `name` 属性之外的所有属性
    return rest;
  }

  // console.warn(`No matching item found for prop: ${prop}`);
  return false;
};
/**
 * 如果匹配列的键和该枚举名字的对象属性名进行匹配，匹配到了提取里面的中文
 */
const handleChangeMap = (row, val) => {
  if (val === "" || val == undefined || val == null) return (row.query = "");
  if (val == "true" || val == "false") {
    row.query = val == "true" ? 1 : 0;
    mappingStatusDefault.value = val;
  } else {
    row.query = val;
    mappingStatusDefault.value = val;
  }
};

watch(
  () => props.columns,
  newColumns => {
    if (newColumns.length > 0) {
      defaultCondition.value =
        getFilteredConditions(getFieldType(defaultField))[0]?.value || "";
    }
  },
  { immediate: true }
);

defineExpose({
  setDefaultFilters,
  showModal,
  clearAll
});
</script>

<style scoped>
.el-button {
  margin-right: 8px;
}
</style>
