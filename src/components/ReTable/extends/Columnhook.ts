export const getColumnHook = () => {
  // 获取字段类型
  const getFieldType = (field, columns) => {
    const column = columns.find(col => col.prop === field);
    // console.log(columns, "1");

    if (column) {
      if (column.type.match(/varchar/i)) {
        return "varchar";
      } else if (column.type.match(/datetime/i)) {
        return "datetime";
      } else if (column.type.match(/enum/i)) {
        return "enum";
      } else if (column.type.match(/double/i)) {
        return "double";
      }
    }
    return "";
  };
  // 解析 enum 类型字段选项
  const getParseEnumOptions = (field, columns) => {
    const column = columns.find(col => col.prop === field);
    if (column && column.type.match(/enum/i)) {
      const enumMatch = column.type.match(/enum\((.+)\)/);
      if (enumMatch) {
        return enumMatch[1]
          .split(",")
          .map(option => option.trim().replace(/'/g, ""));
      }
    }
    return [];
  };
  return {
    getFieldType,
    getParseEnumOptions
  };
};
