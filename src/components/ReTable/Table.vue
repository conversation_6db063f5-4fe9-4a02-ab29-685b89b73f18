<template>
  <div
    class="table-container"
    :style="
      isVisible
        ? 'height: calc(100vh - 7.125rem);'
        : 'height: calc(100vh - 8.5rem);'
    "
  >
    <InputSearch
      :columns="columns"
      :topBtns="topButtons"
      :mappingStatus="mappingStatus"
      :fastFilter="fastFilter"
      :queryData="queryData"
      @update-filter="InputSearchUpdateFilter"
      @reset-filter="InputSearchResetFilter"
    />
    <div class="table-wrapper">
      <!-- 固定表头 -->
      <!-- :style="isVisible && isFullLifecycleRoute ? '' : 'display: none'" -->
      <div class="scroll-box">
        <div
          ref="headerScrollRef"
          class="header-scroll-container"
          :style="{ width: headerScrollWidth, bottom: headerScrollBottom }"
          @scroll="handleHeaderScroll"
        >
          <div
            class="scroll-shadow"
            :style="{ width: tableBodyWidth + 'px' }"
          />
        </div>
      </div>
      <!-- checkbox-config="{trigger: 'row'}" 可能与某些行点击事件冲突 -->
      <vxe-table
        ref="tableRef"
        :key="tableKey"
        class="my-table-scrollbar"
        border
        :loading="pictLoading"
        :data="tableDatas"
        highlight-current-row
        max-height="100%"
        :row-config="{
          isHover: true,
          height: 45,
          resizable: false
        }"
        :column-config="{ drag: false, resizable: false }"
        :resizable-config="{ dragMode: 'fixed' }"
        :resizable="true"
        row-config.useKey="true"
        :row-class-name="tableRowClassName"
        :size="'medium'"
        :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
        :scroll-x="{
          enabled: true,
          gt: 0,
          mode: 'wheel',
          scrollToLeftOnChange: false
        }"
        :height="height != '' ? height : isVisible ? '92%' : '100%'"
        :show-footer="props.showTotal"
        :autoHeight="true"
        :footer-data="footerData"
        :footer-cell-style="getFooterCellStyle"
        :sort-config="{}"
        :header-cell-style="{
          height: '4.4875rem'
        }"
        show-overflow
        :round="true"
        :auto-resize="false"
        :sync-resize="false"
        :tree-config="{
          transform: true,
          ...treeConfig,
          showIcon: true,
          expandAll: isExpandAll
        }"
        show-footer-overflow
        :checkbox-config="{ trigger: 'row' }"
        @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange"
        @cell-dblclick="handleCellClick"
        @resizable-change="headerDragendWidth"
      >
        <!-- 序列号合计 -->
        <!-- <vxe-column field="seq" type="seq" width="50" fixed="left" /> -->
        <!-- 勾选框列 合计 -->
        <vxe-table-column type="checkbox" width="55" fixed="left" field="seq" />
        <!-- 动态生成列 -->

        <template v-for="column in visibleColumns" :key="column.prop">
          <vxe-table-column
            :field="column.prop"
            :title="column.label"
            :width="column.width"
            :sortable="true"
            :fixed="column.fixed ? 'left' : ''"
            header-align="center"
            show-overflow="tooltip"
            show-footer-overflow
            v-bind="{ 'tree-node': isTreeNode(column.prop) }"
          >
            <template #header>
              <div class="header-top-1">
                <div class="header-top-2">
                  <el-tooltip
                    v-if="tableCellEditAble.includes(column.prop)"
                    content="内容可编辑"
                    placement="top"
                  >
                    <el-icon> <Edit /> </el-icon>
                  </el-tooltip>
                  <span
                    :class="{
                      'status-tag': headerCellKeys.includes(column.prop)
                    }"
                    style="display: block"
                  >
                    {{ column.label }}
                  </span>
                </div>
                <!-- 根据 column.mappingStatus 判断显示输入框或下拉选择框 -->
                <div>
                  <!-- 日期时间选择器 -->
                  <vxe-date-picker
                    v-if="column.type === 'datetime'"
                    v-model="filters[column.prop]"
                    type="date"
                    placeholder="选择日期时间"
                    clearable
                    style="height: 32px"
                    @change="handleFilterChange"
                  />
                  <!-- 输入框 -->
                  <vxe-input
                    v-else-if="!getMappingStatus(column.prop)"
                    v-model="filters[column.prop]"
                    placeholder="搜索"
                    clearable
                    style="height: 30px"
                    @change="handleFilterChange"
                  />
                  <!-- 选择框 -->
                  <vxe-select
                    v-else
                    v-model="filters[column.prop]"
                    clearable
                    style="height: 32px"
                    @change="handleFilterChange"
                  >
                    <vxe-option
                      v-for="(label, value) in getMappingStatus(column.prop)"
                      :key="value"
                      :label="label"
                      :value="value"
                    />
                  </vxe-select>
                </div>
              </div>
            </template>
            <!-- 以上是字段头部区域，以下是对应字段的内容区域 -->
            <template #default="scope">
              <span
                v-if="
                  (scope.row.edit &&
                    tableCellEditAble.some(item => item == column.prop)) ||
                  column.type === 'textarea'
                "
              >
                <!-- 判断是否为日期 -->
                <el-date-picker
                  v-if="isDateType(column)"
                  ref="inputRef"
                  v-model="scope.row[column.prop]"
                  type="datetime"
                  size="small"
                  style="width: 80%"
                  placeholder="Pick a Date"
                  format="YYYY/MM/DD HH:mm:ss"
                  @blur="() => (scope.row.edit = false)"
                />
                <!-- 判断下拉 -->
                <el-select
                  v-else-if="getFieldType(column.prop, columns) === 'enum'"
                  v-model="scope.row[column.prop]"
                  :resize="true"
                  :style="{ width: `${column.width - 20}px` }"
                  size="small"
                  placeholder="选择状态(1)"
                  @blur="() => (scope.row.edit = false)"
                >
                  <el-option
                    v-for="option in getParseEnumOptions(column.prop, columns)"
                    :key="option"
                    :label="option"
                    :value="option"
                  />
                </el-select>
                <!--  判断交期异常备注特殊处理成可编辑字段类型文本域 -->
                <span
                  v-else-if="
                    column.type === 'textarea' &&
                    hasPermission('permission:btn:exceptionDelivery')
                  "
                >
                  <el-input
                    v-if="!scope.row[column.prop] || scope.row.isEditErr"
                    v-model="scope.row.errInputValue"
                    :value="scope.row[column.prop]"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入"
                    :style="{ width: `${column.width - 40}px` }"
                    @keydown.enter="handleSaveErr(scope.row)"
                  >
                    <template #append>
                      <el-button
                        style="flex-shrink: 0"
                        type="primary"
                        size="small"
                        @click="handleSaveErr(scope.row)"
                      >
                        保存
                      </el-button>
                    </template>
                  </el-input>
                  <span v-else class="flex justify-between pr-5">
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="scope.row[column.prop]"
                      placement="top"
                    >
                      <span
                        class="block overflow-hidden text-ellipsis"
                        :style="{ width: `${column.width - 100}px` }"
                      >
                        {{ scope.row[column.prop] }}
                      </span>
                    </el-tooltip>

                    <el-button
                      type="primary"
                      size="small"
                      @click="handleOpenEditErr(scope.row)"
                    >
                      修改
                    </el-button>
                  </span>
                </span>
                <!-- 其他情况为输入框 -->
                <span v-else-if="column.type === 'textarea'">
                  {{ scope.row[column.prop] }}
                </span>
                <el-input
                  v-else
                  ref="inputRef"
                  v-model="scope.row[column.prop]"
                  size="small"
                  style="width: 80%"
                  @blur="() => (scope.row.edit = false)"
                />
              </span>
              <span v-else-if="isDateType(column) || column.type == 'date'">
                {{ formatDateTime(scope.row[column.prop]) }}
              </span>
              <!-- 对异常标id进行超链接处理 -->
              <!-- <span v-else-if="column.label == '异常表id'">
                <span class="hyperlink">{{ scope.row[column.prop] }}</span>
              </span> -->
              <span v-else> {{ scope.row[column.prop] }} </span>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <div class="pager-container">
      <div
        class="selected-info"
        :class="isVisible ? 'pagination-container-fixed-selected' : ''"
      >
        已勾选 <span style="color: #ff8200">{{ selectRowNum }} </span>条
      </div>
      <vxe-pager
        v-model:current-page="currentPage_"
        v-model:page-size="pageSize_"
        :class="isVisible ? 'pagination-container-fixed' : ''"
        :style="{ backgroundColor: isDark ? '#02020e' : '#fff' }"
        :total="total"
        :page-sizes="[20, 30, 40, 50, 100, 150, 200, 300, 500, 1000, 2000]"
        @page-change="pageChange"
      />
    </div>

    <!-- 列设置 -->
    <ColumnSettingDialog
      :visible="isColumnSettingVisible"
      :searchQuery="searchQuery"
      :visibleColumns="visibleColumns"
      :hiddenColumns="hiddenColumns"
      :updateColumns="updateColumnsSetting"
      @update:searchQuery="updateSearchQuery"
      @close="closeColumnSetting"
      @save="saveColumnSetting"
      @move-to-visible="moveToVisible"
      @move-to-hidden="moveToHidden"
      @update-column-width="updateColumnWidth"
    />
    <EditDialog
      :EditDialogVisible="EditDialogVisible"
      :RowData="rowData"
      :columns="columns"
      :tableCellEditAble="tableCellEditAble"
      :fetchTableData="fetchTableData"
      @close="closeEditDialog"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, onMounted, computed } from "vue";
import { Edit } from "@element-plus/icons-vue";
import { toRaw } from "vue";
import { ElInput, ElPagination, ElTable, ElTableColumn } from "element-plus";
import InputSearch from "@/components/ReFilterInput/input.vue";
import ColumnSettingDialog from "./ColumnSettingDialog.vue";
import _ from "lodash";
import { deviceDetection } from "@pureadmin/utils";
import { browserPerformChunk } from "@/utils/performChunk";
import { formatDateTime } from "@/utils/formateDateTime";
import { useVisibility } from "@/utils/useVisibility";
import { useIsFullLifecycleRoute } from "@/utils/useIsFullLifecycelRoute";
import { useDark } from "@pureadmin/utils";
const { isDark } = useDark();

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

const { hasPermission } = useButtonPermission(route);

// 导入hook
import { getColumnHook } from "./extends/Columnhook";

const { isVisible } = useVisibility();
const { isFullLifecycleRoute } = useIsFullLifecycleRoute();

import EditDialog from "./EditDialog.vue";
import { addSaleExceptionInformation } from "@/api/home/<USER>";
import { getHeaderPage, headerPageUpdate } from "@/api/system/common";
import { useNav } from "@/layout/hooks/useNav";
import { useVisibilityStore } from "@/store/modules/showBox";
import { useButtonPermission } from "@/utils/hooks";

const props = defineProps({
  /** * 操作按钮 * @type {Array} */
  topButtons: {
    type: Array,
    default: () => []
  },
  /** * 列设置名称 * @type {Array} */
  columns: {
    type: Array,
    default: () => []
  },
  /** * 接口请求 * @type {Function} */
  fetchData: {
    type: Function,
    required: true
  },
  /** * 对该字段进行颜色标注 表内容可点击跳转 * @type {Array} */
  headerCellKeys: {
    type: Array,
    default: () => []
  },
  /** * 对该字段进行编辑标注 表内容可点击编辑 * @type {Array} */
  tableCellEditAble: {
    type: Array,
    default: () => []
  },
  /** * 根据外部的筛选条件给行定义不同的颜色 * @type {Array} */
  tableRowWarnStatus: {
    type: Array,
    default: () => []
  },
  /** * 对表的高度进行限制 * @type {String, Number} */
  height: {
    type: [String, Number],
    default: ""
  },
  /** * 更新搜索内容 * @type {Function} */
  updateQueryDate: {
    type: Function,
    required: true
  },
  /** * 初始化搜索内容 * @type {Object} */
  queryData: {
    type: Object
  },
  /** * 是否显示底部求和 * @type {Boolean} */
  showTotal: {
    type: Boolean
  },
  /** 求和方法  * @type {Function} */
  getTotal: {
    type: Function,
    default: () => () => {}
  },
  /** 层级数据  * @type {String} */
  treeNodeKey: {
    type: String,
    default: ""
  },
  /** 层级数据配置项  * @type {Object} */
  treeConfig: {
    type: Object,
    default: () => ({})
  },
  /** 根据定义的枚举值进行字段搜索类型区分  * @type {Array} */
  mappingStatus: {
    type: Array,
    default: () => []
  },
  fastFilter: {
    type: Object,
    default: () => {}
  },
  /** * 初始过滤条件 * @type {Object} */
  initialFilters: {
    type: Object,
    default: () => ({})
  }
});
// 引入获取enum的hook
const { getParseEnumOptions, getFieldType } = getColumnHook();

const emit = defineEmits([
  "selectedRows",
  "cellClick",
  "rowClick",
  "updateColumns",
  "pageSizeChange",
  "currentPageChange",
  "InputSearchResetFilter",
  "InputSearchUpdateFilter"
]);

// 数据相关
const tableRef = ref(null);
const tableDatas = ref([]);
const total = ref(0);
const currentPage_ = ref(props.queryData?.page?.current || 1);
const pageSize_ = ref(props.queryData?.page?.size || 200);
const columns = ref(props.columns);
// 列表勾选的内容
const selectedRows = ref([]);
// 列表编辑表单的ref
const rowEdit = ref();
// 列设置相关
const isColumnSettingVisible = ref(false);
const searchQuery = ref("");
const pictLoading = ref(true);

const visibleColumns = ref(columns.value.filter(col => col.visible));
const hiddenColumns = ref(columns.value.filter(col => !col.visible));
const selectedHiddenColumns = ref([]);
const selectedVisibleColumns = ref([]);
const tempVisibleColumns = ref([]);
const tempHiddenColumns = ref([]);
const filteredHiddenColumns = ref([...hiddenColumns.value]);
const filteredVisibleColumns = ref([...visibleColumns.value]);
const selectRowNum = ref(0);
// 添加搜索功能
const filters = reactive({});
const isExpandAll = ref(false);
// 编辑对话框展示
const EditDialogVisible = ref(false);
// 传入的数据
const rowData = ref({});

const footerData = ref([{ seq: "合计" }]);
const tableKey = ref(0); // 用于强制重新渲染表格的键
const updateColumnsSetting = ref({});

const { isCollapse } = useNav();

const visibilityStore = useVisibilityStore();

const headerScrollBottom = computed(() => {
  const bottom = visibilityStore.isVisible ? 40 : 0;
  return `${bottom}px`;
});
const headerScrollWidth = computed(() => {
  const width = isCollapse.value ? 64 : 234;
  return `calc(100% - ${width}px)`;
});

// 交期异常备注修改
const handleOpenEditErr = row => {
  row.isEditErr = true;
};

// 交期异常备注填写
const handleSaveErr = row => {
  const data = {
    id: row.id,
    errMsg: row.errInputValue,
    orderNo: row.billNo
  };
  // if (!data.errMsg.trim()) {
  //   return;
  // }
  addSaleExceptionInformation(data).then(res => {
    if (res.code === 200) {
      fetchTableData();
    }
  });
};

// 顶部滚动条
const tableBodyWidth = ref(0);
const headerScrollRef = ref<HTMLElement>();

const updateTableBodyWidth = async () => {
  try {
    const maxRetries = 3;
    let retryCount = 0;

    const tryGetElements = async () => {
      const tableBody = tableRef.value?.$el?.querySelector(
        ".vxe-table--body-wrapper"
      );
      const scrollSpace = tableRef.value?.$el?.querySelector(
        ".vxe-table--scroll-x-space"
      );
      if (tableBody && scrollSpace) {
        tableBodyWidth.value = Math.max(
          tableBody.scrollWidth,
          scrollSpace.scrollWidth
        );
        return true;
      } else if (tableBody) {
        tableBodyWidth.value = tableBody.scrollWidth;
        return true;
      }

      if (retryCount < maxRetries) {
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, 800));
        return tryGetElements();
      }
      return false;
    };

    await tryGetElements();
  } catch (error) {
    console.error("更新表格宽度时发生错误:", error);
  }
};

const debouncedUpdateTableBodyWidth = _.debounce(updateTableBodyWidth, 100);

onMounted(async () => {
  await debouncedUpdateTableBodyWidth();

  const observer = new MutationObserver(async mutations => {
    for (const mutation of mutations) {
      if (mutation.addedNodes.length) {
        await debouncedUpdateTableBodyWidth();
        observer.disconnect();
        break;
      }
    }
  });

  observer.observe(tableRef.value.$el, {
    childList: true,
    subtree: true
  });
});

// 同步滚动逻辑
const handleBodyScroll = ({ scrollLeft }) => {
  if (headerScrollRef.value) {
    headerScrollRef.value.scrollLeft = scrollLeft;
  }
};

const handleHeaderScroll = (event: Event) => {
  const scrollLeft = (event.target as HTMLElement).scrollLeft;
  if (tableRef.value) {
    tableRef.value.scrollTo(scrollLeft, null);
  }
};

// 监听表格宽度变化
watch(tableRef, newVal => {
  if (newVal) {
    nextTick(async () => {
      await debouncedUpdateTableBodyWidth();
      // // 获取表格横向滚动内容容器
      // const tableBody = newVal.$el.querySelector(".vxe-table--body-wrapper");
      // // 获取表格横向滚动占位容器
      // const scrollSpace = newVal.$el.querySelector(
      //   ".vxe-table--scroll-x-space"
      // );
      // if (tableBody && scrollSpace) {
      //   // 取两个容器中最大的宽度（防止固定列导致的宽度计算错误）
      //   tableBodyWidth.value = Math.max(
      //     tableBody.scrollWidth,
      //     scrollSpace.scrollWidth
      //   );
      // } else if (tableBody) {
      //   tableBodyWidth.value = tableBody.scrollWidth;
      // } else {
      //   console.warn("无法获取表格横向滚动内容容器或占位容器");
      // }
    });
  }
});

// 给表尾合计添加样式
const getFooterCellStyle = {
  fontWeight: "bold"
  // background: "var(--table-header-bg-color)",
  // color: "var(--el-text-color-primary)"
};

const dark = {
  background: "var(--table-header-bg-color)",
  color: "var(--el-text-color-primary)"
};

const cachedRowClasses = new Map();

// 求和底部
const getFooterTotal = async () => {
  const res = await props.getTotal();

  if (Array.isArray(res)) {
    const sumResult = res.reduce((acc, field) => {
      acc[field] = tableDatas.value
        .reduce((sum, row) => sum + row[field], 0)
        .toFixed(2);
      return acc;
    }, {});
    footerData.value = [{ seq: "合计", ...sumResult }];
  } else if (typeof res === "object" && res !== null) {
    footerData.value = [{ seq: "合计", ...res }];
  }
};

// 初始化数据
const fetchTableData = async () => {
  selectRowNum.value = 0;
  pictLoading.value = true;
  try {
    // 父组件传入的查询接口
    const data = await props.fetchData();

    if (data.code === 200) {
      // 确保 data.records 不为 null 或 undefined
      const records = data.records || [];
      tableDatas.value = records.map(item => ({
        ...item,
        edit: false
      }));
      total.value = data.total || 0;

      // 等待 DOM 更新完成
      await nextTick();

      // 在数据加载完成后计算合计
      if (props.showTotal) {
        await getFooterTotal();
      }

      // 重新应用列设置
      columns.value = [...visibleColumns.value, ...hiddenColumns.value];

      // 如果存在高亮行且是带有id跳转，则滚动到该行
      if (props.tableRowWarnStatus.length > 0 && route.query.id) {
        // 获取高亮行
        // const row = tableDatas.value.find(row => row.id === route.query.id);
        const row = tableDatas.value[51];
        tableRef.value.scrollToRow(row);
      }
    }
  } catch (error) {
    console.error("Error fetching data:", error);
  } finally {
    pictLoading.value = false;
  }
};
// sortDataByFields(data, ["outstandingQuantity", "anotherField"]
/**
 *用来排序工序代码
 * @param data 需要进行排序操作的字数据
 * @param fields 需要进行排序操作的字段数组
 */
function sortDataByFields(data, fields) {
  //提取字段部分 使用正则表达式提取前缀和数字部分。
  const getFieldParts = value => {
    const prefix = value.match(/^[A-Z]+/)[0];
    const num = parseInt(value.match(/\d+$/)[0], 10);
    return { prefix, num };
  };
  //根据提取的内容进行排序 根据这些部分进行排序。对于每个字段，如果前缀相同，则比较数字部分；如果不同，则按字母顺序比较前缀。
  return data.sort((a, b) => {
    for (const field of fields) {
      const partA = getFieldParts(a[field]);
      const partB = getFieldParts(b[field]);
      if (partA.prefix === partB.prefix) {
        if (partA.num !== partB.num) {
          return partA.num - partB.num;
        }
      } else {
        return partA.prefix.localeCompare(partB.prefix);
      }
    }
    return 0;
  });
}

// 打开列设置弹窗
const openColumnSetting = () => {
  isColumnSettingVisible.value = true;
  tempVisibleColumns.value = JSON.parse(JSON.stringify(visibleColumns.value));
  tempHiddenColumns.value = JSON.parse(JSON.stringify(hiddenColumns.value));
  // 监听列设置弹窗的显示状态
  watch(
    () => isColumnSettingVisible.value,
    newVal => {
      if (newVal) {
        filteredHiddenColumns.value = [...tempHiddenColumns.value];
        filteredVisibleColumns.value = [...tempVisibleColumns.value];
      }
    }
  );
};

// 列过滤功能
const filterColumns = () => {
  const query = searchQuery.value.toLowerCase();
  filteredHiddenColumns.value = tempHiddenColumns.value.filter(col =>
    col.label.toLowerCase().includes(query)
  );
  filteredVisibleColumns.value = tempVisibleColumns.value.filter(col =>
    col.label.toLowerCase().includes(query)
  );
};

// 列表过滤功能 使用lodash插件
const handleFilterChange = _.debounce(() => {
  //因为下拉选择后的值会自动转为字符串，需进行转换
  const queries = []; // 构建查询条件
  resetPagination();
  currentPage_.value = 1;
  for (let i = 0; i < Object.entries(filters).length; i++) {
    const key = Object.keys(filters)[i];
    let value = filters[key];

    // 简单判断 null 值，转换为空字符串
    if (value == null) {
      filters[key] = "";
      value = "";
    }

    // console.log(value, "value");

    // 判断布尔值并转换
    if (value === "true" || value === "false") {
      value = value === "true" ? 1 : 0;
      filters[key] = value ? "true" : "false";
    }

    if (typeof value === "boolean" || typeof value === "number") {
      queries.push({
        name: key,
        condition: "like",
        query: value, // 直接使用布尔值或数字形式的值
        logic: "and"
      });
    } else if (typeof value === "string" && value.trim()) {
      queries.push({
        name: key,
        condition: "like",
        query: value, // 使用字符串形式的值
        logic: "and"
      });
    }
  }

  props.updateQueryDate(queries); // 根据过滤条件重新请求数据

  fetchTableData();
}, 800); // 延迟800ms执行

// 显示字段到临时的可见列
const moveToVisible = () => {
  selectedHiddenColumns.value.forEach(col => {
    const index = tempHiddenColumns.value.findIndex(
      item => item.prop === col.prop
    );
    if (index > -1) {
      tempHiddenColumns.value.splice(index, 1);
      tempVisibleColumns.value.push(col);
    }
  });
  filterColumns();
  selectedHiddenColumns.value = [];
};

// 隐藏字段到临时的隐藏列
const moveToHidden = () => {
  selectedVisibleColumns.value.forEach(col => {
    const index = tempVisibleColumns.value.findIndex(
      item => item.prop === col.prop
    );
    if (index > -1) {
      tempVisibleColumns.value.splice(index, 1);
      tempHiddenColumns.value.push(col);
    }
  });
  filterColumns();
  selectedVisibleColumns.value = [];
};

// 更新列宽
const updateColumnWidth = column => {
  const col = tempVisibleColumns.value.find(col => col.prop === column.prop);
  if (col) {
    col.width = column.width;
  }
};

// 保存列设置
const saveColumnSetting = async newColumns => {
  visibleColumns.value = newColumns.visibleColumns;
  hiddenColumns.value = newColumns.hiddenColumns;
  isColumnSettingVisible.value = false;
  // 确保取消固定的列应用正确
  columns.value = visibleColumns.value.concat(hiddenColumns.value);
  if (newColumns.status === "save") {
    tableKey.value += 1; // 每次更新 columns 时，递增 tableKey
  }
  emit("updateColumns", columns.value);
};

// 存储用户分页大小记录
const savePageSize = (menu, pageSize) => {
  const pageSizeRecord = {
    sysMenu: menu,
    pageSize: pageSize
  };
  currentPageSize_ = pageSize;
  headerPageUpdate(pageSizeRecord);
};

// 从数据库中获取分页大小
let currentPageSize_ = 0;
const getPageSize = async () => {
  const currentMenu = router.currentRoute.value.path;
  const pageSizeRecord = await getHeaderPage(currentMenu.replaceAll("/", ""));
  // 获取当前页
  if (pageSizeRecord.data) {
    pageSize_.value = pageSizeRecord.data.pageSize;
    currentPageSize_ = pageSizeRecord.data.pageSize;
    emit("pageSizeChange", pageSize_);
  }
  await fetchTableData();
};

getPageSize();

// 分页
const pageChange = ({ pageSize, currentPage }) => {
  const currentMenu = router.currentRoute.value.path;

  if (currentPageSize_ !== pageSize) {
    savePageSize(currentMenu.replaceAll("/", ""), pageSize);
  }

  emit("pageSizeChange", pageSize_);
  emit("currentPageChange", currentPage_);
  currentPage_.value = currentPage;
  pageSize_.value = pageSize;
  fetchTableData();

  nextTick(() => {
    const tabScroll = document.querySelector(
      ".vxe-table--body-wrapper.body--wrapper"
    );
    // 使用ref获取到el-table的body元素并重置滚动条
    if (tabScroll) {
      tabScroll.scrollTop = 0; // 重置滚动条
      // tabScroll.scrollLeft = 0; // 重置滚动条
    }
  });
};

const isDateType = column => {
  return column.type === "datetime";
};

watch(
  () => props.columns, // 监听 props.columns 而非整个 props
  newColumns => {
    const { visible, hidden } = splitColumns(newColumns);
    columns.value = newColumns;
    visibleColumns.value = visible;
    hiddenColumns.value = hidden;
    tableKey.value += 1; // 每次更新 columns 时，递增 tableKey
  },
  { deep: true, immediate: false } // 如果需要初始化时执行，可以添加这个选项
);

// 将分割逻辑提取到一个独立函数
function splitColumns(columns) {
  const visible = [];
  const hidden = [];
  columns.forEach(col => {
    if (col.visible) {
      visible.push(col);
    } else {
      hidden.push(col);
    }
  });
  return { visible, hidden };
}

// 初始化数据
// fetchTableData();

const closeColumnSetting = () => {
  isColumnSettingVisible.value = false;
};

// 处理搜索查询的更新
const updateSearchQuery = query => {
  searchQuery.value = query;
};

/**
 *
 * @param param0 {row, column}
 * @description 权级：编辑 > 单元格点击 > 行点击
 */
const handleCellClick = ({ row, column }) => {
  /* 1.先判断是否编辑 是的话直接return 不进行任何跳转 */
  const editAble = props.tableCellEditAble.some(key => {
    return column.property === key;
  });

  if (editAble)
    return props.tableCellEditAble.some(key => {
      if (column.property === key) {
        // 取消其他行的编辑状态
        if (rowEdit.value?.edit) {
          rowEdit.value.edit = false;
        }
        rowEdit.value = row;
        rowEdit.value.edit = true;
        // 修改编辑为true
        row.edit = true;
      }
    });

  /** 2.再判断是否单元格点击  */
  const isMatched = props.headerCellKeys.some(key => {
    return column.property === key;
  });
  const $table = tableRef.value;
  if (isMatched) {
    emit("cellClick", row, column);
  } else {
    // console.debug("row", row, [row]);
    // $table.toggleCheckboxRow(row);
    // $table.setCheckboxRow([row], true);
    emit("rowClick", row, column);
  }
};

/**
 * 根据条件判断是否高亮该行黄底标注
 */
const tableRowClassName = ({ row }) => {
  const warnStatus = props.tableRowWarnStatus.find(item => {
    // todo
    let value; // console.log(item.columnKey);
    if (item.relation == "and") {
      const removePercentage = value => {
        if (typeof value === "string" && value.includes(item.sift)) {
          return parseFloat(value.slice(0, -1));
        }
        return value;
      };

      function checkItem(item, row) {
        // 如果 item 有 columnKey，进行对比
        if (item.columnKey) {
          const rowValue = removePercentage(toRaw(row)[item.columnKey]);
          const threshold = item.threshold;

          let conditionResult = false;

          switch (item.operator) {
            case "<":
              conditionResult = rowValue < threshold;
              break;
            case "<=":
              conditionResult = rowValue <= threshold;
              break;
            case ">":
              conditionResult = rowValue > threshold;
              break;
            case ">=":
              conditionResult = rowValue >= threshold;
              break;
            case "==":
              conditionResult = rowValue == threshold;
              break;
            case "!=":
              conditionResult = rowValue != threshold;
              break;
            default:
              conditionResult = false;
          }

          // 如果条件不满足，直接返回 false
          if (!conditionResult) {
            return false;
          }
        }
        // console.log("通过");
        // 如果 item 有 children，递归遍历 children
        if (item.children && item.children.length > 0) {
          if (item.relation === "and") {
            // 如果 relation 是 "and"，所有子项都必须满足条件
            for (const child of item.children) {
              if (!checkItem(child, row)) {
                return false; // 如果有一个子项不满足条件，直接返回 false
              }
            }
          } else if (item.relation === "or") {
            // 如果 relation 是 "or"，只要有一个子项满足条件即可
            for (const child of item.children) {
              if (checkItem(child, row)) {
                return true; // 如果有一个子项满足条件，直接返回 true
              }
            }
            return false; // 如果所有子项都不满足条件，返回 false
          }
        }

        return true; // 如果所有条件都满足，返回 true
      }
      // 调用递归函数
      const res = checkItem(item, row);
      // 判断条件通过标色
      if (res) {
        return item.className;
      }
    } else if (item.sift) {
      const removePercentage = value => {
        if (typeof value === "string" && value.includes(item.sift)) {
          return parseFloat(value.slice(0, -1));
        }
        return value;
      };
      value = removePercentage(row[item.columnKey]);
      switch (item.operator) {
        case "<":
          return value < item.threshold;
        case ">":
          return value > item.threshold;
        case "==":
          return value == item.threshold;
        case "!=":
          return value != item.threshold;
        default:
          return false;
      }
    } else {
      value = row[item.columnKey];
      switch (item.operator) {
        case "<":
          return value < item.threshold;
        case ">":
          return value > item.threshold;
        case "==":
          return value == item.threshold;
        case "!=":
          return value != item.threshold;
        default:
          return false;
      }
    }
  });

  //        wbw 在这里做日期判断
  let SPDTime = row.salesOrderPlannedDeliveryTime;
  if (SPDTime) {
    const currentDate = new Date();
    const SPDTimeDate = new Date(SPDTime);
    const timeDifference = SPDTimeDate - currentDate;
    const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
    //获取当前数据状态
    const SPDstate = row.salesOrderState; //状态
    const undeliveredQuantity = row.undeliveredQuantity; //未出库数量
    if (
      daysDifference < 7 &&
      undeliveredQuantity != 0 &&
      !(SPDstate === "完结")
    ) {
      //未完成全部销售出库或状态手动关闭时，标红置顶
      return "row-red";
    } else if (
      daysDifference > 7 &&
      undeliveredQuantity != 0 &&
      !(SPDstate === "完结")
    ) {
      return "row-pending";
    } else {
      return "";
    }
  }

  const className = warnStatus ? warnStatus.className : "";

  cachedRowClasses.set(row, className);
  return className;
};

const loading = boolean => {
  pictLoading.value = boolean;
};

// 清空table的搜索框的方法
const resetFilter = () => {
  visibleColumns.value.forEach(item => {
    filters[item.prop] = "";
  });
};

// 获取列宽度的方法
const getColumnWidths = () => {
  const cols = tableRef.value.$el.querySelectorAll("colgroup col");
  // const columnWidths = Array.from(cols).map(col => col.width);
  const columnWidths = Array.from(cols).map(col => Math.round(col.clientWidth)); // 四舍五入
  return columnWidths;
};
// 修复拖拽bug

const headerDragendWidth = params => {
  const column = params.column; // 获取被调整大小的列
  const newWidth = column.renderWidth; // 获取列的新宽度

  // console.log(column.property, column.width, "new=>", newWidth);
  updateColumnsSetting.value = { prop: column.property, newWidth };
  // 修复拖拽宽度重复发送请求
  // emit(
  //   "updateColumns",
  //   props.columns,
  //   { prop: column.property, newWidth },
  //   "editWidth"
  // );
};

// 打开编辑方法,会同时传入选中行与列信息
const handleOpenEdit = (handle, row) => {
  EditDialogVisible.value = true;

  rowData.value.handle = handle;
  rowData.value.row = row;
};

// 编辑弹窗关闭
const closeEditDialog = () => {
  EditDialogVisible.value = false;
  rowData.value = {};
  fetchTableData();
};

// 监听外层列表勾选变化
const handleSelectionChange = selection => {
  selectedRows.value = [];
  selectRowNum.value = selection.records.length;
  selectedRows.value = selection.records;
  emit("selectedRows", selectedRows.value);
};

const isTreeNode = prop => {
  // 判断哪些 prop 需要添加 tree-node 属性
  return prop === props.treeNodeKey;
};

// 重置分页
const resetPagination = () => {
  watch(
    () => props.queryData, // 监听 props.columns 而非整个 props
    newQueryData => {
      currentPage_.value = newQueryData.page.current;
      pageSize_.value = newQueryData.page.size;
    },
    { deep: true, immediate: false } // 如果需要初始化时执行，可以添加这个选项
  );
};

watch(
  () => props.queryData, // 监听 props.columns 而非整个 props
  newQueryData => {
    if (
      router.currentRoute.value.path === "/abnormal-situation-overview" &&
      router.currentRoute.value.query.id
    ) {
      currentPage_.value = newQueryData.page.current;
      pageSize_.value = newQueryData.page.size;
      fetchTableData();
    }
  },
  { deep: true, immediate: false } // 如果需要初始化时执行，可以添加这个选项
);

/**
 * 如果匹配列的键和该枚举名字的对象属性名进行匹配，匹配到了提取里面的中文
 */
const getMappingStatus = (prop: string) => {
  if (!Array.isArray(props.mappingStatus) || props.mappingStatus.length === 0) {
    // console.error("Table.vue props.mappingStatus 为空");
    return false;
  }

  const mappingItem = props.mappingStatus.find(item => item.name === prop);
  if (mappingItem) {
    const { name, ...rest } = mappingItem; // 提取 `name` 属性之外的所有属性
    return rest;
  }

  // console.warn(`No matching item found for prop: ${prop}`);
  return false;
};

const InputSearchUpdateFilter = filter => {
  console.log("InputSearchUpdateFilter被调用，filter:", filter);
  currentPage_.value = 1;
  emit("currentPageChange", currentPage_.value);
  emit("InputSearchUpdateFilter", filter);
};

const InputSearchResetFilter = () => {
  console.log("InputSearchResetFilter被调用");
  emit("InputSearchResetFilter");
};

// 监听queryData变化
watch(
  () => props.queryData,
  newVal => {
    console.log("ReTable中queryData变化:", newVal);
  },
  { deep: true, immediate: true }
);

const handleIsExtract = () => {
  isExpandAll.value = !isExpandAll.value;
  tableKey.value++;
  return isExpandAll.value;
};

//用语高亮某一行
const selectTableRow = row => {
  tableRef.value.setCurrentRow(row);
};

// 暴露列设置的方法
defineExpose({
  openColumnSetting,
  loading,
  resetFilter,
  handleOpenEdit,
  resetPagination,
  fetchTableData,
  handleIsExtract,
  selectTableRow,
  tableDatas
});

// 监听filters变化
watch(
  filters,
  (newFilters, oldFilters) => {
    // 统计有多少个过滤条件
    const activeFilters = Object.entries(newFilters).filter(
      ([key, value]) => value !== ""
    );

    handleFilterChange();
  },
  { deep: true }
);

// 监听initialFilters变化
watch(
  () => props.initialFilters,
  newFilters => {
    // 清空当前filters
    Object.keys(filters).forEach(key => {
      delete filters[key];
    });
    // 将新的值添加进去
    Object.assign(filters, newFilters);
  },
  { deep: true, immediate: true } // immediate: true 确保初始化时也执行
);
</script>

<style lang="scss" scoped>
.pager-container {
  display: flex;
  gap: 10px; /* 在它们之间添加一些间距 */
  align-items: center;
  justify-content: flex-end; /* 将对齐方式设置为左对齐 */
  padding: 10px 0;
  background-color: transparent;
  border-top: 1px solid transparent;
}

.selected-info {
  z-index: 999;
  font-size: 14px;
  color: #606266;
}

.vxe-pager {
  background: none;
}

.pagination-container-fixed {
  position: fixed;
  right: 20px;
  bottom: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%; /* 设置为100%以适应表格宽度 */
  height: 40px;
  padding: 10px 20px;
}

.pagination-container-fixed-selected {
  position: fixed;
  right: 550px;
  bottom: 0;
  z-index: 999;
  height: 28px;
}

.table-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  overflow: hidden;
}

/* 滚动条 */
.header-scroll-container {
  position: fixed;
  right: 0;
  bottom: 40px;
  z-index: 99;
  width: 97%;
  overflow-x: scroll;

  &::-webkit-scrollbar {
    width: 20px;
    height: 12px;
  }

  &::-webkit-scrollbar-thumb {
    background: #bfbfbf; /* 灰色滑块 */
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb:hover,
  &::-webkit-scrollbar-thumb:active {
    background-color: #787878;
  }
}

.scroll-shadow {
  position: absolute;
  height: 1px; /* 最小高度即可 */
  opacity: 0; /* 保持不可见 */
}

/* 暗黑模式适配 */
[data-vxe-ui-theme="dark"] .header-scroll-container {
  background: #333;

  &::-webkit-scrollbar {
    background: #333;
  }

  &::-webkit-scrollbar-thumb {
    background: #376593;
  }

  &::-webkit-scrollbar-thumb:hover,
  &::-webkit-scrollbar-thumb:active {
    background-color: #376593;
  }
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
}

.vxe-table--fixed-wrapper {
  position: relative;
}

.scroll-table-show {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.status-tag {
  display: block;
  width: 100%;
  color: #409eff; /* 超链接蓝色 */
  text-decoration: underline; /* 添加下划线 */
  cursor: pointer; /* 鼠标指针变为手型 */
  transition: color 0.3s; /* 添加过渡效果 */
}

.status-tag:hover {
  color: #337ecc; /* 悬停时颜色加深 */
}
</style>

<style scoped lang="scss">
:deep(.el-table .cell) {
  text-align: center;
}

.top-btn-group {
  display: flex;
  justify-content: end;
  margin: 0 0 10px 10px;
}

.sort-align {
  display: inline-block;
  color: red;
  text-align: center;
}

:deep(.el-table [class*="el-table__row--level"] .el-table__expand-icon) {
  display: none;
}

.header-top-1 {
  flex-direction: column;
  align-items: center;
  overflow: visible !important;
  white-space: nowrap;
}

.header-top-2 {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

<style>
.row-pending {
  background-color: #fdf6ec; /* 淡黄色 */
}

.row-gray {
  background-color: #f4f4f5; /* 淡黄色 */
}

.row-processing {
  background-color: #fdf6ec; /* 淡黄色 */
}

.row-red {
  background-color: #fef0f0;
}

.hyperlink {
  color: #409eff; /* 使用主题蓝色 */
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s;
}

.hyperlink:hover {
  color: #337ecc; /* 深蓝色 */
  text-decoration: none;
}

.row-blue {
  background-color: #ecf5ff;
}
</style>

/** 滚动条样式 */
<style lang="scss" scoped>
.my-table-scrollbar {
  ::-webkit-scrollbar {
    width: 8px;
    height: 0.1px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }
}

/** 默认模式 */
[data-vxe-ui-theme="light"] {
  .my-table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #fff;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }

    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #787878;
    }
  }
}

/** 暗黑模式 */
[data-vxe-ui-theme="dark"] {
  .my-table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #151518;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #4d7cab;
    }

    .selected-info .pagination-container-fixed-selected {
      background-color: #151518;
    }

    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #376593;
    }
  }
}
</style>

<style>
.vxe-header--column.col--ellipsis > .vxe-cell {
  overflow: visible !important;
}

.vxe-tree-cell {
  padding: 0 !important;
}
</style>
