<template>
  <div v-if="buttons.length != 0">
    <el-button
      v-for="(button, index) in buttons"
      :key="index"
      :type="button.type || 'primary'"
      :size="button.size || 'default'"
      style="margin-left: 8px"
      @click="button.action"
    >
      {{ t(button.label) }}
    </el-button>
  </div>
</template>

<script lang="tsx" setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n(); // 解构出t方法
const props = defineProps({
  buttons: {
    type: Array,
    default: () => []
  }
});
</script>
