<template>
  <div class="flex gap-2">
    <el-tag v-if="routeTitle != ''" class="custom-info custom-tag">{{
      $t(routeTitle)
    }}</el-tag>
    <el-tag v-for="(item, index) in otherField" :key="index" class="custom-tag">
      {{ item.label }}: {{ item.value }}
    </el-tag>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from "vue";
import { ElTag } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
  otherField: {
    type: Array,
    default: () => []
  },
  routeTitle: {
    type: String,
    default: ""
  }
});
</script>

<style scoped>
.flex {
  display: flex;
  flex-wrap: wrap;
}

.gap-2 {
  gap: 12px; /* 调整标签间距 */
}

/* 自定义标签样式 */
.custom-tag {
  min-width: 150px; /* 设置最小宽度，让方块看起来更整齐 */
  padding: 8px 16px; /* 调整内边距，使方块变大 */
  font-size: 16px; /* 调整字体大小 */
  border-radius: 4px; /* 圆角调整 */
}

/* 自定义 info 标签颜色 */
.custom-info {
  color: #000; /* 更深背景色的字体颜色 */
}
</style>
