<template>
  <el-dialog
    v-if="visible"
    :model-value="visible"
    :title="$t('buttons.pureColumnSet')"
    :width="isMobile ? '95%' : '55%'"
    :close-on-click-modal="false"
    draggable
    @close="dialogVisible"
  >
    <!-- 搜索栏 -->
    <el-input
      v-model="localSearchQuery"
      :placeholder="$t('search.pureSearch')"
      style="margin-bottom: 10px"
      clearable
      @input="SearchColumns"
    />
    <!-- 列设置表格 -->
    <div :class="isMobile ? '' : 'tables-wrapper'" class="disable-select">
      <div class="table-container hidden-columns">
        <!-- 隐藏列表格 -->
        <el-table
          :data="filteredHiddenColumns"
          row-key="prop"
          height="300"
          :header-cell-style="{
            position: 'sticky',
            top: '0',
            background: '#fff',
            zIndex: 1
          }"
          :empty-text="$t('search.pureHide')"
          @selection-change="handleHiddenSelectionChange"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column type="index" label="#" width="40" />
          <el-table-column prop="label" :label="$t('panel.pureHideColumn')" />
        </el-table>
      </div>

      <div :class="isMobile ? 'buttons-container-device' : 'buttons-container'">
        <el-button
          type="primary"
          class="btn-margin"
          size="small"
          @click="moveToVisible"
          >{{ $t("buttons.pureDisplay") }}</el-button
        >
        <el-button
          type="primary"
          class="btn-margin"
          size="small"
          @click="moveToHidden"
          >{{ $t("buttons.pureHide") }}</el-button
        >
      </div>

      <div
        id="sortable-table"
        class="table-container visible-columns"
        style="overflow: auto"
      >
        <!-- 可拖动显示列表格 -->
        <el-table
          v-loading="isLoading"
          element-loading-text="加载中"
          :data="filteredVisibleColumns"
          row-key="prop"
          height="300"
          :header-cell-style="{
            position: 'sticky',
            top: '0',
            background: '#fff',
            zIndex: 1
          }"
          empty-text="暂无显示列数据"
          @selection-change="handleVisibleSelectionChange"
        >
          <el-table-column type="selection" width="30" fixed />

          <el-table-column
            :label="$t('fields.pureColumnNames')"
            fixed
            width="140"
          >
            <template v-slot="scope">
              <div
                class="sortable-handle"
                :class="{ 'ignore-element': scope.row.fixed }"
                style="display: flex; align-items: center; font-weight: 600"
                :style="!scope.row.fixed ? '' : 'line-height: 0'"
              >
                <div v-if="!scope.row.fixed" class="sortable-handle">☰</div>
                <el-popover
                  v-else
                  width="100"
                  trigger="hover"
                  content="冻结列不可排序"
                >
                  <template #reference>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1.7em"
                      height="1.7em"
                      viewBox="0 0 24 24"
                      class="sort-align"
                    >
                      <path
                        fill="currentColor"
                        d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16m-1-5h2v2h-2zm0-8h2v6h-2z"
                      />
                    </svg>
                  </template>
                </el-popover>
                <div style="margin-left: 10px">
                  {{ scope.row.label }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="列名" width="140">
            <template v-slot="scope">
              {{ scope.row.prop }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('fields.pureWidth')" width="80">
            <template v-slot="scope">
              <el-input
                v-model="scope.row.width"
                size="small"
                :value="scope.row.width"
                @blur="updateColumnWidth(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('fields.pureFixedPosition')" width="120">
            <template v-slot="scope">
              <el-select
                v-model="scope.row.fixed"
                placeholder="不固定"
                @change="updateColumnFixed(scope.row)"
              >
                <el-option label="不固定" :value="false" />
                <el-option label="固定" :value="true" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="当前排序" width="80">
            <template v-slot="scope">
              <el-input
                v-model="scope.row.index"
                size="small"
                :value="scope.row.index"
                @blur="editColumnIndex(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template v-slot:footer>
      <el-button type="primary" @click="reset">重置</el-button>
      <el-button
        type="primary"
        :disabled="tempVisibleColumns.length === 0"
        @click="save"
        >{{ $t("buttons.pureSave") }}</el-button
      >
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup>
import { ref, nextTick, watch, onMounted } from "vue";
import Sortable from "sortablejs";
import { cloneDeep, deviceDetection } from "@pureadmin/utils";
import { debounce } from "lodash";
const isMobile = deviceDetection();
const isLoading = ref(false);
const props = defineProps({
  visible: Boolean,
  searchQuery: String,
  visibleColumns: Array,
  hiddenColumns: Array,
  newWidth: Array,
  updateColumns: Object
});

const emit = defineEmits([
  "update:searchQuery",
  "close",
  "save",
  "move-to-visible",
  "move-to-hidden",
  "update-column-width"
]);

const tempVisibleColumns = ref([...props.visibleColumns]);

const tempHiddenColumns = ref([...props.hiddenColumns]);

const selectedHiddenColumns = ref([]);
const selectedVisibleColumns = ref([]);
const filteredHiddenColumns = ref([...tempHiddenColumns.value]);

const filteredVisibleColumns = ref([...tempVisibleColumns.value]);
const localSearchQuery = ref(props.searchQuery);
console.log(1);

// 监听搜索查询并更新本地查询状态
watch(
  () => props.searchQuery,
  newVal => {
    isLoading.value = true;
    localSearchQuery.value = newVal;
    SearchColumns();
  }
);

const SearchColumns = () => {
  const query = localSearchQuery.value.toLowerCase();
  filteredHiddenColumns.value = tempHiddenColumns.value.filter(col =>
    col.label.toLowerCase().includes(query)
  );
  filteredVisibleColumns.value = tempVisibleColumns.value.filter(col =>
    col.label.toLowerCase().includes(query)
  );
  emit("update:searchQuery", localSearchQuery.value);
  isLoading.value = false;
}; // 100ms 防抖限制

// 过滤列 该函数会发生强制回流的问题，使用节流的方式修复
const filterColumns = debounce(() => {
  filteredHiddenColumns.value = tempHiddenColumns.value;
  filteredVisibleColumns.value = tempVisibleColumns.value;
  isLoading.value = false;
}, 45); // 100ms 防抖限制

// 处理隐藏列选择变更
const handleHiddenSelectionChange = selection => {
  selectedHiddenColumns.value = selection;
};

// 将选中的隐藏列移动到可见列
const handleVisibleSelectionChange = selection => {
  selectedVisibleColumns.value = selection;
  // updateColumnFixed();
};

// 将选中的可见列移动到隐藏列
const moveToVisible = () => {
  selectedHiddenColumns.value.forEach(col => {
    const index = tempHiddenColumns.value.findIndex(
      item => item.prop === col.prop
    );
    if (index > -1) {
      // 设置 visible 为 true 并将列移动到可见列
      col.visible = true;
      tempHiddenColumns.value.splice(index, 1);
      tempVisibleColumns.value.push(col);
    }
  });
  updateColumnFixed();
  selectedHiddenColumns.value = [];
  emit("move-to-visible");
};

// 将选中的可见列移动到隐藏列
const moveToHidden = () => {
  selectedVisibleColumns.value.forEach(col => {
    const index = tempVisibleColumns.value.findIndex(
      item => item.prop === col.prop
    );
    if (index > -1) {
      // 设置 visible 为 false 并将列移动到隐藏列
      col.visible = false;
      tempVisibleColumns.value.splice(index, 1);
      tempHiddenColumns.value.push(col);
    }
  });
  updateColumnFixed();
  selectedVisibleColumns.value = [];
  emit("move-to-hidden");
};

// 更新列宽度
const updateColumnWidth = column => {
  const col = tempVisibleColumns.value.find(col => col.prop === column.prop);
  if (col) {
    col.width = column.width;
  }
  console.log("当前输入的宽度值：", column.width);
  emit("update-column-width", column);
};

// 编辑排序
const editColumnIndex = column => {
  const newColumn = cloneDeep(column);
  // 删除原位置的列
  tempVisibleColumns.value.splice(tempVisibleColumns.value.indexOf(column), 1);
  // 将newColumn插入到index位置
  tempVisibleColumns.value.splice(column.index - 1, 0, newColumn);
  // 重新设置index
  columnsIndex();
};

// 固定位置后数据重新排序,将固定的排序到顶部
const updateColumnFixed = () => {
  tempVisibleColumns.value.sort((a, b) => {
    return a.fixed === true ? -1 : b.fixed === true ? 1 : 0;
  });
  columnsIndex();
};

// 保存列设置
const save = ({ status = "save" } = {}) => {
  localSearchQuery.value = "";
  emit("save", {
    visibleColumns: [...tempVisibleColumns.value],
    hiddenColumns: [...tempHiddenColumns.value],
    status
  });
};

// 重置列设置
// 定义默认值
const defaultColumnValues = {
  width: 200,
  fixed: false
};

const reset = () => {
  // 重置列设置为默认值，宽度设为200
  tempVisibleColumns.value = tempVisibleColumns.value.map(column => ({
    ...column,
    width: defaultColumnValues.width,
    fixed: defaultColumnValues.fixed
  }));
  // 更新 filteredVisibleColumns 和 filteredHiddenColumns
  filteredVisibleColumns.value = [...tempVisibleColumns.value]; //显示列
  filteredHiddenColumns.value = [...tempHiddenColumns.value]; //隐藏列
  columnsIndex();
  console.log("列设置已重置:", tempVisibleColumns.value);
  localSearchQuery.value = "";
  isLoading.value = false;
  // 触发保存或其他操作（如果需要）
};

// 关闭对话框
const dialogVisible = () => {
  localSearchQuery.value = "";
  emit("close");
};

// 更新列索引方法
const columnsIndex = () => {
  tempVisibleColumns.value.forEach((col, index) => {
    col.index = index + 1;
  });
  // filterColumns();
  SearchColumns();
};

columnsIndex();

updateColumnFixed();

// 监听对话框可见状态并初始化列数据
watch(
  () => props.visible,
  async newVal => {
    isLoading.value = await true;
    if (newVal) {
      tempVisibleColumns.value = [...props.visibleColumns];
      tempHiddenColumns.value = [...props.hiddenColumns];
      updateColumnFixed();
      nextTick(() => {
        const el = document.querySelector(
          "#sortable-table .el-table__body-wrapper tbody"
        ) as HTMLElement;
        if (el) {
          Sortable.create(el, {
            // 去掉以下属性表示整列都可拖拽
            handle: ".sortable-handle",
            animation: 300,
            filter: ".ignore-element",
            delay: 1,
            ghostClass: "blue-background-class",
            forceFallback: true,
            draggable: "tr",
            onEnd: ({ newIndex, oldIndex }) => {
              if (newIndex !== oldIndex) {
                const movedItem = tempVisibleColumns.value.splice(
                  oldIndex,
                  1
                )[0];
                tempVisibleColumns.value.splice(newIndex, 0, movedItem);
                tempVisibleColumns.value = [...tempVisibleColumns.value];
                updateColumnFixed();
              }
            }
          });
        }
      });
    }
  }
);

// 获取所有列设置的函数
const getAllColumnSettings = () => {
  const visibleColumnsSettings = tempVisibleColumns.value.map(col => ({
    label: col.label,
    width: col.width,
    fixed: col.fixed,
    prop: col.prop,
    visible: col.visible,
    index: col.index,
    type: col.type
  }));

  const hiddenColumnsSettings = tempHiddenColumns.value.map(col => ({
    label: col.label,
    width: col.width,
    fixed: col.fixed,
    prop: col.prop,
    visible: col.visible,
    index: col.index,
    type: col.type
  }));
  return {
    visibleColumns: visibleColumnsSettings,
    hiddenColumns: hiddenColumnsSettings
  };
};

// 在组件挂载时直接获取列设置
onMounted(() => {
  getAllColumnSettings();
});

// 监听列数据变化并获取最新列设置
watch([() => tempVisibleColumns.value, () => tempHiddenColumns.value], () => {
  // isLoading.value = true;
  getAllColumnSettings();
});

// 监听父组件传递的 newWidth 变化
watch(
  () => props.updateColumns,
  updateColumns => {
    tempVisibleColumns.value = props.visibleColumns.map(col => {
      if (col.prop === updateColumns.prop) {
        return { ...col, width: updateColumns.newWidth };
      }
      return col;
    });
    filteredVisibleColumns.value = [...getAllColumnSettings().visibleColumns];
    console.log("更新后的 tempVisibleColumns：", tempVisibleColumns.value);
    console.log(
      "更新后的 filteredVisibleColumns：",
      filteredVisibleColumns.value
    );
    save({ status: "editWidth" });
  },
  { deep: true, immediate: false }
);
</script>

<style scoped lang="scss">
/* 禁用文本选择 */
.disable-select {
  user-select: none;

  /* Safari */

  /* Firefox */

  /* IE10+ */

  /* Opera */
}

.vistible-row-system {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eee;
}

.btn-margin {
  margin: 20px 0;
}

.tables-wrapper {
  display: grid;
  grid-template-columns: 0.5fr auto 1fr;
  gap: 10px;
  overflow: hidden; // 防止溢出
}

.table-container {
  width: 100%;
  border: 1px solid #eee;
}

.el-table {
  font-size: 12px;
  line-height: 1.4;
}

.el-table th,
.el-table td {
  padding: 4px 8px;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: center;
  padding: 0 10px; // 增加内边距，防止溢出
}

.buttons-container-device {
  display: flex;
  gap: 20px;
  justify-content: center;
  padding: 0 10px;
  margin: 5px 100px;
}

.sortable-handle {
  text-align: center;
  cursor: move;
}

.ignore-element {
  cursor: default;
}

:deep(.el-table .cell) {
  text-align: center;
}

.top-btn-group {
  display: flex;
  justify-content: end;
  margin: 0 0 10px 10px;
}

.sort-align {
  display: inline-block;
  color: red;
  text-align: center;
}
</style>
