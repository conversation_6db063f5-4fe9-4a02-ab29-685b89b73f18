<script setup lang="tsx">
import { defineProps, nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import {
  remarkAbnormalSituationAPI,
  updateAbnormalSituationHandlerAPI,
  getAbnormalSituationHandler
} from "@/api/abnormal-situation-overview";
import { updateArrivalTimeAPI } from "@/api/sales-delivery-order-monitoring";
import { getUserList } from "@/api/system/user";
import { message } from "@/utils/message";
import type { PropType } from "vue";
import dayjs from "dayjs";
const { t } = useI18n(); // 解构出t方法
const props = defineProps({
  EditDialogVisible: {
    type: Boolean
  },
  RowData: {
    type: Array || Object
  },
  tableCellEditAble: {
    type: Array
  },
  // 修改部分：指定 tableRef 为 ref 对象类型
  fetchTableData: {
    type: Function as PropType<() => void>
  }
});

const dialogVisible = ref(props.EditDialogVisible);
// 定义表单数据
const form = ref({
  handlerId: "",
  handler: "", // 处理人
  textField: "", // 文本域的绑定值
  dateTime: dayjs().format("YYYY-MM-DD HH:mm:ss") // 日期时间
});
const rowData = ref(props.RowData);
const userOptions = ref([]);
// 定义表单验证规则
const formRules = {
  handler: [{ required: true, message: "请输入处理人", trigger: "blur" }],
  textField: [{ required: true, message: "请输入内容", trigger: "blur" }]
};

const emit = defineEmits(["close"]);

const close = () => {
  dialogVisible.value = false;
  form.value.textField = "";
  // Bug修复：将日期对象转换为字符串，以符合类型要求
  form.value.dateTime = new Date().toISOString();

  emit("close");
};

watch(
  () => props.EditDialogVisible,
  async newVal => {
    // console.log(props.RowData, "1");
    dialogVisible.value = newVal;
    rowData.value = props.RowData;
    // 当处理分配操作时获取用户列表
    if (newVal && rowData.value.handle === "distribution") {
      const firstErrType = rowData.value.row[0].errType;
      const formData = new FormData();
      formData.append("errType", firstErrType);
      const res = await getAbnormalSituationHandler(formData);
      userOptions.value = res.data;
      // userOptions.value = (
      //   await getUserList({ pageSize: 999, pageNum: 1 })
      // ).data.resultList;
    }
  }
);

// 定义表单引用
const formRef = ref(null);

// 提交表单的方法
const handleSubmit = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      // 创建一个包含所有请求的 Promise 数组

      if (rowData.value.handle == "distribution") {
        const ids = rowData.value.row.map(item => item.id);

        const res = await updateAbnormalSituationHandlerAPI({
          ids: ids,
          handler: form.value.handler,
          handlerId: form.value.handlerId
        });
        if (res.code == 200) {
          message("分配成功", { type: "success" });
          dialogVisible.value = false;
          props.fetchTableData();
        } else {
          message("分配失败", { type: "error" });
        }
      } else if (rowData.value.handle == "editTime") {
        console.log("时间", rowData.value);
        const res = await updateArrivalTimeAPI(
          form.value.dateTime,
          rowData.value.row[0].saleOutBoundCode
        );
        if (res.code == 200) {
          message("修改成功", { type: "success" });
          dialogVisible.value = false;
          props.fetchTableData();
        } else {
          message("修改失败", { type: "error" });
        }
      } else {
        const ids = rowData.value.row.map(item => {
          return {
            id: item.id,
            remark: form.value.textField
            //  resolved: item.resolved
          };
        });
        const res = await remarkAbnormalSituationAPI(ids);
        if (res.code == 200) {
          message("修改成功", { type: "success" });
          dialogVisible.value = false;
          props.fetchTableData();
        } else {
          message("修改失败", { type: "error" });
        }
        dialogVisible.value = false;
      }
    } else {
      console.log("表单验证失败");
    }
  });
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      rowData.handle == 'distribution'
        ? t('buttons.pureDistribution')
        : t('buttons.pureRemark')
    "
    width="50%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    draggable
    @close="close()"
  >
    <div class="form-container">
      <el-form
        ref="formRef"
        style="width: 100%"
        :model="form"
        label-width="100px"
        :rules="formRules"
        label-position="top"
        :hide-required-asterisk="true"
      >
        <template v-if="rowData.handle == 'distribution'">
          <el-form-item prop="handler">
            <el-select
              v-model="form.handlerId"
              placeholder="请选择处理人"
              class="w-full"
              clearable
              filterable
              @change="
                () => {
                  form.handler = userOptions.find(
                    item => item.id === form.handlerId
                  )?.name;
                }
              "
            >
              <el-option
                v-for="(item, index) in userOptions"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <!-- 新增表格 -->
          </el-form-item>

          <!-- 添加 border 和 stripe 属性 -->
          <el-table
            :data="rowData.row"
            style="width: 100%; max-height: 400px; margin: 10px auto"
            border
            stripe
          >
            <!-- 这里假设 RowData 中的每个对象有 id、remark、resolved 等字段，你可以根据实际情况修改 -->

            <el-table-column prop="id" label="ID" />
            <el-table-column prop="errMsg" label="异常情况" />
          </el-table>
        </template>
        <template v-else-if="rowData.handle == 'editTime'">
          <el-form-item prop="dateTime">
            <el-date-picker
              v-model="form.dateTime"
              type="datetime"
              placeholder="请选择日期和时间"
              :value-format="'YYYY-MM-DD HH:mm:ss'"
            />
          </el-form-item>
        </template>
        <el-form-item v-else-if="rowData.handle == 'handle'" prop="textField">
          <el-input
            v-model="form.textField"
            type="textarea"
            placeholder="请输入内容"
            rows="6"
            col="20"
            style="width: 100%; height: 150px"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close()">{{ t("buttons.pureCancel") }}</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ t("buttons.pureConfirm") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.filter-element {
  width: 220px;
}

.form-container {
  display: flex;
  justify-content: center; /* 水平居中 */
}
</style>
