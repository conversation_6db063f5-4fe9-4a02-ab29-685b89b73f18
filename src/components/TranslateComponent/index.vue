<template>
  <div id="translate">
    <slot />
    <div v-if="loading">加载中...</div>
    <div v-if="error">加载翻译脚本失败。</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from "vue";

const props = defineProps({
  targetLanguage: {
    type: String,
    default: "english"
  },
  autoTranslate: {
    type: Boolean,
    default: true
  }
});

const loading = ref(true);
const error = ref(false);
const translateScriptSrc =
  "https://cdn.staticfile.net/translate.js/3.5.1/translate.js";

const loadScript = (src, onLoadCallback) => {
  const script = document.createElement("script");
  script.src = src;
  script.onload = onLoadCallback;
  script.onerror = () => {
    loading.value = false;
    error.value = true;
    console.error("Failed to load the translation script.");
  };
  document.head.appendChild(script);
};

const executeTranslation = () => {
  if (window.translate) {
    window.translate.language.setLocal(props.targetLanguage);
    window.translate.service.use("client.edge");
    window.translate.execute();
    loading.value = false;
  } else {
    loading.value = false;
    error.value = true;
    console.error("Translation script is not available.");
  }
};

onMounted(() => {
  loadScript(translateScriptSrc, () => {
    if (props.autoTranslate) {
      executeTranslation();
    }
  });
});

onUnmounted(() => {
  // 清理脚本或其他操作
});

// 调用方式
// <TranslateComponent
//   :targetLanguage="'english'"
//   :autoTranslate="true"
//   style="width: 150px !important"
// >
//   <div>这里是需要翻译的内容</div>
// </TranslateComponent>
</script>

<style scoped>
.translateSelectLanguage {
  position: absolute;
  top: 100px;
  right: 100px;
}
</style>
