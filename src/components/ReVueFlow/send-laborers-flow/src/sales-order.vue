<script setup>
import { ref, defineProps, defineEmits } from "vue";
import { Posi<PERSON>, <PERSON><PERSON> } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
// 接收传递的 `props`
defineProps({
  title: {
    type: String,
    required: true
  },
  progress: {
    type: Number,
    default: 0
  },
  handles: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
  <div>
    <div class="operator-node-li">销售订单</div>

    <Handle
      id="sales-order"
      type="source"
      :position="Position.Right"
      :connectable="false"
    />
  </div>

  <Progress
    :percentage="50"
    :width="50"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
  />
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(0, -60px);
}
</style>
