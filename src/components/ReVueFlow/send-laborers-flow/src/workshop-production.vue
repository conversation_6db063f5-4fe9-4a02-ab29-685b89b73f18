<script setup>
import { ref, defineProps, defineEmits } from "vue";
import { Position, Handle } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
// 接收传递的 `props`
defineProps({
  title: {
    type: String,
    default: "销售订单"
  },
  progress: {
    type: Number,
    default: 0
  },
  handles: {
    type: Array,
    default: () => [
      {
        id: "target-c",
        type: "target",
        position: "Position-Top",
        connectable: false
      },
      {
        id: "source",
        type: "source",
        position: "Position-Right",
        connectable: false
      },
      {
        id: "target-b",
        type: "target",
        position: "Position-Left",
        connectable: false
      }
    ]
  }
});
// 定义事件
const emit = defineEmits(["toggle-nodes-son"]);

// 切换节点的显示
const toggleNodes = () => {
  // emit("toSendLaborerSon");
  // 派工子流程跳转
  console.log("车间生产子流程监控跳转成功");
};
</script>

<template>
  <div>
    <div class="operator-node-li">车间生产</div>

    <Handle type="source" :position="Position.Bottom" :connectable="false" />
    <Handle type="source" :position="Position.Top" :connectable="false" />
    <Handle
      id="workshop-production-1"
      type="target"
      :position="Position.Left"
      :connectable="false"
    />
  </div>
  <svg
    class="extra-e7-btn"
    xmlns="http://www.w3.org/2000/svg"
    width="1.5em"
    height="1.5em"
    viewBox="0 0 512 512"
    @click.stop="toggleNodes"
  >
    <path
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="45"
      d="M192 176v-40a40 40 0 0 1 40-40h160a40 40 0 0 1 40 40v240a40 40 0 0 1-40 40H240c-22.09 0-48-17.91-48-40v-40"
    />
    <path
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="45"
      d="m288 336l80-80l-80-80M80 256h272"
    />
  </svg>

  <Progress
    :percentage="40"
    :width="50"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
  />
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.extra-e7-btn {
  position: absolute;
  width: 25px !important;
  height: 25px !important;
  padding: 2px;
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  background-color: #3498db !important;
  border-radius: 50% !important;
  transform: translate(270%, 0) rotate(180deg);
}

.progress-bar {
  position: absolute;
  transform: translate(0, -55px);
}
</style>
