<script setup>
import { ref, defineProps, defineEmits } from "vue";
import { Position, <PERSON><PERSON> } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
// 接收传递的 `props`
defineProps({
  title: {
    type: String,
    default: "销售订单"
  },
  progress: {
    type: Number,
    default: 0
  },
  handles: {
    type: Array,
    default: () => [{ id: "sales-order", type: "source", position: "right" }]
  }
});
</script>

<template>
  <div>
    <div class="operator-node-li">{{ title }}</div>

    <Handle
      v-for="handle in handles"
      :key="handle.id"
      :type="handle.type"
      :position="
        handle.position == 'left'
          ? Position.Left
          : handle.position == 'right'
            ? Position.Right
            : handle.position == 'top'
              ? Position.Top
              : Position.Bottom
      "
      :connectable="false"
    />
  </div>

  <!-- <Progress
    :percentage="progress"
    :width="50"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
  /> -->
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10.625rem;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(0, -55px);
}
</style>
