import {
  getMRPProgress, // MRP
  getPlanOrderProgress, // 计划订单
  getProductOrderProgress, // 生产订单
  getDispatchWorkersProgress, // 派工
  getWarehousePreparationProgress, // 仓库准备
  getWorkshopProductionProgress, // 车间生产
  getInspectionCompletionProgress, // 检验完成 完工校验
  getProductionStockProgress, // 生产入库
  getProductionOrderProgress, // 生产订单
  getSellOutProgress, // 销售出库
  getSaleOrderProgress, // 销售订单
  getPurchaseOrderProgress, // 采购订单
  getOutOrderProgress // 出库 委外订单
} from "@/api/full-lifecycle/progress";

/**
 * @function 获取全生命周期进度条
 * @param {number} billNo 单据编号
 * @returns {Promise<number>} 进度条百分比数组
 */
export const useProgressTotal = async (billNo = 0) => {
  try {
    // 查询现有的进度条
    const [
      res1,
      res2,
      res3,
      res4,
      res5,
      res6,
      res7,
      res8,
      res9,
      res10,
      res11,
      res12,
      res13
    ] = await Promise.all([
      getMRPProgress(billNo),
      getPlanOrderProgress(billNo),
      getProductOrderProgress(billNo),
      getDispatchWorkersProgress(billNo),
      getWarehousePreparationProgress(billNo),
      getWorkshopProductionProgress(billNo),
      getInspectionCompletionProgress(billNo),
      getProductionStockProgress(billNo),
      getProductionOrderProgress(billNo),
      getSellOutProgress(billNo),
      getSaleOrderProgress(billNo),
      getPurchaseOrderProgress(billNo),
      getOutOrderProgress(billNo)
    ]);

    // 将所有结果存储在一个数组中
    let results = [
      res1,
      res2,
      res3,
      res4,
      res5,
      res6,
      res7,
      res8,
      res9,
      res10,
      res11,
      res12,
      res13
    ];

    // 如果 API 请求成功，处理返回的数据
    results = results.map(item => {
      if (item.code === 200 && Number.isFinite(item.data)) {
        // 转换为百分比，保留小数点后三位往下取整
        return Math.floor(item.data * 1000) / 10;
      } else {
        // 如果 API 返回错误代码，则返回 0
        return null;
      }
    });

    return { results };
  } catch (error) {
    // 捕获异常并处理
    console.error("Error in useProgressTotal:", error);
    // 返回一个空的或默认值的数组，防止代码崩溃
    return { results: Array(11).fill(0) };
  }
};

export const usePurchaseOrderProgress = async () => {};
