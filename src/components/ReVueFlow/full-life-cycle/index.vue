<script setup>
import { ref, watchEffect, onMounted, watch, onUnmounted, reactive } from "vue";
import { VueFlow, Handle, Position, MarkerType } from "@vue-flow/core";
import { MiniMap } from "@vue-flow/minimap";
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";
import WarePrepar from "./src/warehouse-preparation.vue";
import SendLaborer from "./src/send-laborers.vue";
import CommonNode from "./src/common-node.vue";
import WorkshopProduction from "./src/workshop-production.vue";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useProgressTotal } from "../hooks/useProgressTotal";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
const { t, locale } = useI18n(); // 解构出t方法
const nodeStore = useNodeStore();
const router = useRouter();
const route = useRoute();
import { onActivated } from "vue";
const store = useNodeStore();

// 获取所有路由
const routes = router.options.routes;

// 查找指定路径的路由信息
const routeInfo = findRoute(routes, "/full-lifecycle/purchase-order");

// 递归查找当前路由
function findRoute(routes, path) {
  for (const route of routes) {
    // 如果当前路由匹配
    if (route.path === path) {
      return route.meta;
    }

    // 如果有子路由，递归查找
    if (route.children && route.children.length > 0) {
      const found = findRoute(route.children, path);
      if (found) {
        return found;
      }
    }
  }
  return null; // 如果没有找到
}
/**
 * 当路由参数
 */
onActivated(() => {
  if (!route.query.sytText1) {
    router.replace({
      path: "/full-lifecycle/purchase-order",
      query: {
        sytText1: store.queryId
      }
    });
  }
});
// 当前角色权限访问节点
const roleNodes = route.meta.nodeIds;
// 定义节点状态
const STATUS = {
  PENDING: "pending",
  IN_PROGRESS: "in-progress",
  COMPLETED: "completed"
};
const emit = defineEmits(["clickNode"]);
// 初始节点和边
const initialNodes = ref([
  {
    id: "1",
    label: "销售订单",
    position: { x: -150, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.COMPLETED,
    highlighted: true,
    labelBgBorderRadius: 4,
    labelBgStyle: { fill: "#FFCC00", color: "#fff", fillOpacity: 0.7 },
    // targetPosition: Position.Right
    sourcePosition: Position.Right,
    type: "sales"
  },
  {
    id: "2",
    label: "销售出库",
    position: { x: 80, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.IN_PROGRESS,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "out-storage"
  },
  {
    id: "3",
    label: "生产订单",
    position: { x: 350, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "production-order"
  },
  {
    id: "4",
    label: "生产入库",
    position: { x: 630, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.IN_PROGRESS,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "production-in-storage"
  },
  {
    id: "5",
    label: "完工检验",
    position: { x: 950, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.COMPLETED,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "completion-inspection"
  },
  {
    id: "6",
    label: "车间生产",
    position: { x: 1250, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Bottom,
    type: "workshop-production"
  },
  {
    id: "7",
    label: "仓库备料",
    position: { x: 1250, y: 200 },
    dimensions: {
      width: 150,
      height: 40
    },
    status: STATUS.IN_PROGRESS,
    highlighted: false,
    type: "operator",
    targetPosition: Position.Top,
    sourcePosition: Position.Right
  },
  {
    id: "extra-1",
    label: "采购订单",
    position: locale.value == "en" ? { x: 1600, y: 150 } : { x: 1450, y: 150 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "purchase-orders"
  },
  {
    id: "extra-2",
    label: "委外订单",
    position: locale.value == "en" ? { x: 1600, y: 260 } : { x: 1450, y: 260 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "outsourced-orders"
  },
  {
    id: "8",
    label: "派工",
    position: { x: 1000, y: 200 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.COMPLETED,
    highlighted: false,
    targetPosition: Position.Right,
    sourcePosition: Position.Left,
    type: "dispatch-troops"
  },
  {
    id: "9",
    label: "审核生产订单及用料清单",
    position: { x: 550, y: 200 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.IN_PROGRESS,
    highlighted: false,
    targetPosition: Position.Right,
    sourcePosition: Position.Left,
    type: "production-orders-and-material-lists"
  },
  {
    id: "10",
    label: "计划订单",
    position: { x: 305, y: 200 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Right,
    sourcePosition: Position.Left,
    type: "program-orders"
  },
  {
    id: "11",
    label: "MRP运算",
    position: { x: 50, y: 200 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.COMPLETED,
    highlighted: false,
    targetPosition: Position.Right,
    type: "mrp"
  }
]);

const initialEdges = ref([
  {
    id: "e1-1",
    source: "1",
    target: "2",
    animated: true,
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e1-2",
    source: "2",
    target: "3",
    label: "",

    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e2-3",
    source: "2",
    target: "3",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e3-4",
    source: "3",
    target: "4",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e4-5",
    source: "4",
    target: "5",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e5-6",
    source: "5",
    target: "6",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e1-7",
    source: "6",
    target: "7",
    label: "",
    targetHandle: "target-c",

    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e7-extra1",
    source: "7",
    target: "extra-1",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e7-extra2",
    source: "7",
    target: "extra-2",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e7-8",
    source: "7",
    target: "8",
    label: "",
    sourceHandle: "target-b",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e8-9",
    source: "8",
    target: "9",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e9-10",
    source: "9",
    target: "10",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e10-11",
    source: "10",
    target: "11",
    label: "",
    animated: true,
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  }
]);

// 监听 locale 变化，动态更新节点属性
watchEffect(() => {
  initialNodes.value = initialNodes.value.map(node => {
    if (node.id === "extra-1") {
      return {
        ...node,
        position:
          locale.value === "en" ? { x: 1600, y: 150 } : { x: 1450, y: 150 }
      };
    } else if (node.id === "extra-2") {
      return {
        ...node,
        position:
          locale.value === "en" ? { x: 1600, y: 260 } : { x: 1450, y: 260 }
      };
    }
    return node;
  });
});

// 控制显示额外节点
const showAdditionalNodes = ref(false);
// 每个节点点击事件
const nodeClickHandler = async props => {
  // console.log(routeInfo.nodeIds, "props");
  if (!routeInfo.nodeIds.some(item => item === props.node.label)) {
    message("当前用户没有权限查看该节点", {
      type: "warning"
    });
    return;
  }

  console.log(props.node.label, "props");
  if (props.node.id.startsWith("extra")) {
    nodeStore.setNode({ id: "7", label: "仓库备料" });
  } else {
    nodeStore.setNode({ id: props.node.id, label: props.node.label }); // 保存节点信息到 Store 持久化
  }
  const clickedNodeId = props.node.id;
  console.log(props.node.label, "props.node.id");
  initialNodes.value = await initialNodes.value.map(node => ({
    ...node,
    highlighted: node.id === clickedNodeId
  }));

  updateNodeStyles(); // 更新节点样式

  // 判断点击是否子节点
  if (props.node.label === "采购订单" || props.node.label === "委外订单") {
    props.node.label === "采购订单"
      ? router.push({ name: "preparationPurchaseOrder" })
      : router.push({ name: "preparationOutOrder" });
    return;
  }
  emit("clickNode", props.node.label);
};
const vueFlowInstance = ref(null);

const onFlowInit = instance => {
  vueFlowInstance.value = instance;

  // 初次适配节点范围
  instance.fitView({ padding: 0.4, includeHiddenNodes: true });
};
// 监听窗口变化 这个死代码主要解决了窗口变化后，全局的流程恢复居中显示
const handleResize = () => {
  const windowWidth = window.innerWidth;
  if (windowWidth < 1650 && vueFlowInstance.value) {
    vueFlowInstance.value.fitView();
  } else {
    vueFlowInstance.value.fitView({ padding: 0.3, includeHiddenNodes: true });
  }
};

// 获取并存储每个节点的进度条
const nodesProgress = ref([]);
let simulatedNode = reactive({});
onMounted(() => {
  /** 使用同步与table同步获取数据 */
  useProgressTotal(nodeStore.queryId).then(({ results }) => {
    nodesProgress.value = [...results];
  });

  // 获取本地存储的节点值，如果没有则使用默认值
  const storedNodeId = nodeStore.nodeId;
  const storedNodeLabel = nodeStore.nodeLabel;

  // 如果值是 undefined 或 null，则使用默认值
  if (storedNodeId === undefined) {
    simulatedNode = { node: { id: "1", label: "销售订单" } };
  } else {
    simulatedNode = { node: { id: storedNodeId, label: storedNodeLabel } };
  }
  nodeClickHandler(simulatedNode);
  window.addEventListener("resize", handleResize);
});

// 计算节点样式
const getNodeStyle = (status, highlighted) => {
  // 对应类型的节点样式
  let backgroundColor;
  switch (status) {
    case STATUS.COMPLETED:
      backgroundColor = "#19CAAD";

      break;
    case STATUS.IN_PROGRESS:
      backgroundColor = "#19CAAD";
      break;
    default:
      backgroundColor = "#19CAAD";
      break;
  }

  // 每个节点的样式
  return {
    background: highlighted ? "#0099cc" : backgroundColor, // 高亮节点为蓝色黄色
    color: highlighted ? "#ccccccc" : "white",
    boxShadow: highlighted
      ? "rgba(244, 96, 108, 0.56) 0px 22px 70px 4px"
      : "none",
    width: 100,
    height: 50,
    borderRadius: "8px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boder: "none",
    "--vf-node-color": "1px solid #fff"
  };
};

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
// // 更新节点样式
const updateNodeStyles = () => {
  initialNodes.value.forEach(node => {
    // console.log(node, "node");
    node.style = getNodeStyle(node.status, node.highlighted);
  });
};

updateNodeStyles(); // 初始化节点样式

// 仓库备料子节点显示隐藏
const toggleNodes = state => {
  showAdditionalNodes.value = !!state;
  initialNodes.value = initialNodes.value.map(node => {
    if (node.id.startsWith("extra-")) {
      return { ...node, hidden: !showAdditionalNodes.value };
    }
    return node;
  });
};

// 获取节点拖拽后的位置坐标、位置偏移量等关键信息
const dragNode = props => {
  // console.log(props);
};

// 整体的位移变化
const flowMove = props => {
  // console.log(props);
};
</script>

<template>
  <div id="vue-flow-container" style="width: 100%">
    <VueFlow
      ref="vueFlowInstance"
      class="math-flow flowchat-container"
      :nodes="initialNodes"
      :edges="initialEdges"
      :nodes-draggable="false"
      fit-view-on-init
      :zoom-on-scroll="true"
      :zoom-on-pinshoot="true"
      :prevent-scrolling="true"
      max-zoom="1.1"
      min-zoom="0.5"
      @init="onFlowInit"
      @nodeDrag="dragNode"
      @nodeClick="nodeClickHandler"
      @move="flowMove"
    >
      <!-- 销售订单 -->
      <template #node-sales>
        <CommonNode
          :hiddenProgress="true"
          :progress="nodesProgress[10]"
          :title="t('common.pureSalesOrder')"
        />
      </template>
      <!-- 销售出库 -->
      <template #node-out-storage>
        <CommonNode
          :title="t('common.pureSalesOutbound')"
          :progress="nodesProgress[9]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 生产订单 -->
      <template #node-production-order>
        <CommonNode
          :title="t('common.pureProductionOrder')"
          :progress="nodesProgress[8]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 生产入库 -->
      <template #node-production-in-storage>
        <CommonNode
          :title="t('common.pureProductionWarehousing')"
          :progress="nodesProgress[7]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 完工检验 -->
      <template #node-completion-inspection>
        <CommonNode
          :title="t('common.pureCompletionInspection')"
          :progress="nodesProgress[6]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 车间生产待封装 -->
      <template #node-workshop-production>
        <WorkshopProduction
          :roleNodes="roleNodes"
          :progress="nodesProgress[5]"
          :title="t('common.pureWorkshopProduction')"
        />
      </template>
      <!-- 派工 -->
      <template #node-dispatch-troops>
        <SendLaborer
          :roleNodes="roleNodes"
          :progress="nodesProgress[3]"
          :title="t('common.pureDispatchWork')"
        />
      </template>
      <!-- 审核生产订单及用料清单 -->
      <template #node-production-orders-and-material-lists>
        <CommonNode
          :title="t('common.pureReviewProductionOrdersAndMaterialLists')"
          :progress="nodesProgress[2]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 计划订单 -->
      <template #node-program-orders>
        <CommonNode
          :title="t('common.purePlannedOrder')"
          :progress="nodesProgress[1]"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- mrp -->
      <template #node-mrp>
        <CommonNode
          :title="t('common.pureMRPCalculation')"
          :progress="nodesProgress[0]"
          :handles="[
            { id: 'source', type: 'target', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <template #node-operator>
        <!-- 仓库备料 -->
        <WarePrepar
          :progress="nodesProgress[4]"
          :title="t('common.pureWarehouseMaterialPreparation')"
          @toggle-nodes-son="toggleNodes"
        />
      </template>
      <!-- 采购订单 -->
      <template #node-purchase-orders>
        <CommonNode
          :roleNodes="roleNodes"
          :title="t('common.purePurchaseOrder')"
          :progress="nodesProgress[11]"
          :style="locale === 'en' ? { marginLeft: '39px' } : {}"
          :handles="[{ id: 'target', type: 'target', position: 'left' }]"
        />
      </template>
      <!-- 委外订单 -->
      <template #node-outsourced-orders>
        <CommonNode
          :roleNodes="roleNodes"
          :title="t('common.pureOutsourcingOrders')"
          :progress="nodesProgress[12]"
          :handles="[{ id: 'target', type: 'target', position: 'left' }]"
        />
      </template>
      <Background />
      <Controls :showInteractive="false" />
      <!-- <MiniMap v-if="!showAdditionalNodes" pannable zoomable /> -->
    </VueFlow>
  </div>
</template>

<style>
@import url("@/style/flow/style.css");
@import url("@/style/flow/theme-default.css");
@import url("@/style/flow/minmap.css");
@import url("@/style/flow/controls.css");
@import url("@/style/flow/node-resizer.css");
</style>
<style scoped>
.flowchat-container {
  height: 340px !important;
  border: #ccc 1px solid;
  border-radius: 5px;
}

.vue-flow__minimap {
  transform: scale(75%);
  transform-origin: bottom left;
}
</style>
<style scoped>
@media (width <= 1200px) {
  .flowchat-container {
    height: 280px !important;
  }

  .vue-flow__minimap {
    transform: scale(65%);
  }
}

@media (width <= 992px) {
  .flowchat-container {
    height: 260px !important;
  }

  .vue-flow__minimap {
    transform: scale(55%);
  }
}

@media (width <= 768px) {
  .flowchat-container {
    height: 220px !important;
  }

  .vue-flow__minimapfw {
    transform: scale(45%);
  }
}

@media (width <= 576px) {
  .flowchat-container {
    height: 200px !important;
  }

  .vue-flow__minimap {
    transform: scale(35%);
  }
}

@media (width >= 1200px) {
  :deep(.vue-flow__container) {
    top: 10px !important;
  }
}

.extra-e7-btn {
  position: absolute;
  width: 25px !important;
  height: 25px !important;
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  background-color: #3498db !important;
  border-radius: 50% !important;
  transform: translate(270%, 0);
}

.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(0, -60px);
}

:deep(.vue-flow__container) {
  top: 5px !important;
}

:deep(.vue-flow__node-default, .vue-flow__node-input, .vue-flow__node-output) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 20px;
  color: #fff;
  background-color: #19caad;
  border-radius: 8px;
}

:deep(.vue-flow__viewport) {
  /* 去掉 点点 的背景 */
  background: #edf2f7;
}

.math-flow {
  width: 100%;
  height: 100%;
  background-color: #edf2f7;
}

.vue-flow__handle {
  width: 10px;
  height: 14px;
  background: #aaa;
  border-radius: 4px;
}

.vue-flow__edges path {
  stroke-width: 3;
}

.vue-flow__node {
  background-color: #f3f4f6;
}

.vue-flow__node-value {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-value.selected {
  box-shadow: 0 0 0 2px #ec4899;
}

.vue-flow__node-value input {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 10px #0000001a;
}

.vue-flow__node-value input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #ec4899;
  transition: box-shadow 0.2s;
}

.vue-flow__node-value .vue-flow__handle {
  background-color: #ec4899;
}

.vue-flow__node-operator {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-operator.selected {
  box-shadow: 0 0 0 2px #2563eb;
}

.vue-flow__node-operator button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
  background-color: #4a5568;
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 10px #0000004d;
}

.vue-flow__node-operator button svg {
  width: 100%;
  height: 100%;
}

.vue-flow__node-operator button:hover {
  background-color: #2563eb;
  transition: background-color 0.2s;
}

.vue-flow__node-operator button.selected {
  background-color: #2563eb;
}

/* 左上角句柄 */
.vue-flow__node-operator .vue-flow__handle[data-handleid="target-a"] {
  top: 25%;
}

/* 左下角句柄 */
.vue-flow__node-operator .vue-flow__handle[data-handleid="target-b"] {
  top: 50%;
}

.vue-flow__node-operator .vue-flow__handle {
  background-color: #2563eb;
}

.vue-flow__handle-top {
  transform: rotate(90deg) translate(-10px, 5px);
}

.vue-flow__handle-bottom {
  transform: rotate(90deg) translate(10px, 5px);
}

.vue-flow__node-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-result.selected {
  box-shadow: 0 0 0 2px #5ec697;
}

.vue-flow__node-result .result {
  display: flex;
  gap: 8px;
  font-size: 24px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  margin: 0;
  appearance: none;
}

input[type="number"] {
  appearance: textfield;
}

/* @import "https://cdn.jsdelivr.net/npm/@vue-flow/core@1.41.5/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/core@1.41.5/dist/theme-default.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/controls@latest/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/minimap@latest/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/node-resizer@latest/dist/style.css"; */
</style>
