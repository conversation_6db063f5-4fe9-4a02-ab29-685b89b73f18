<script setup>
import { defineProps, defineEmits, computed } from "vue";
import { Position, Handle } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
import { useRouter } from "vue-router";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { message } from "@/utils/message";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n(); // 解构出t方法
const nodeStore = useNodeStore();
// 接收传递的 `props`
const props = defineProps({
  roleNodes: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ""
  },
  progress: {
    type: Number,
    default: null
  },
  handles: {
    type: Array,
    default: () => [
      {
        id: "target-c",
        type: "target",
        position: "Position-Top",
        connectable: false
      },
      {
        id: "source",
        type: "source",
        position: "Position-Right",
        connectable: false
      },
      {
        id: "target-b",
        type: "target",
        position: "Position-Left",
        connectable: false
      }
    ]
  }
});

// 定义事件
const emit = defineEmits(["toggle-nodes-son"]);
const router = useRouter();
// 切换节点的显示
const toggleNodes = () => {
  if (!props.roleNodes.some(item => item === "派工子流程")) {
    message("当前用户没有权限查看该节点", {
      type: "warning"
    });
    return;
  }
  /** 持久化全生命周期的节点 */
  nodeStore.setNode({ id: "8", label: "派工" });
  router.push("/full-lifecycle/send-laborers/send-laborers-son");
  // console.log("派工子流程监控跳转成功");
};

function isNotNullOrZero(value) {
  return value || value === 0 || value === 0.0;
}
const progressValue = computed(() => {
  console.log("props", isNotNullOrZero(props.progress));

  return isNotNullOrZero(props.progress) ? props.progress : false;
});
</script>

<template>
  <div class="flex items-center justify-center gap-10">
    <div>
      <div class="operator-node-li">{{ title }}</div>

      <Handle type="source" :position="Position.Right" :connectable="false" />

      <Handle
        id="send-laborer-1"
        type="target"
        :position="Position.Left"
        :connectable="false"
      />
    </div>

    <svg
      class="extra-e7-btn"
      xmlns="http://www.w3.org/2000/svg"
      width="1.5em"
      height="1.5em"
      viewBox="0 0 512 512"
      :style="locale === 'en' ? { right: '30px' } : {}"
      @click.stop="toggleNodes"
    >
      <path
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="45"
        d="M192 176v-40a40 40 0 0 1 40-40h160a40 40 0 0 1 40 40v240a40 40 0 0 1-40 40H240c-22.09 0-48-17.91-48-40v-40"
      />
      <path
        fill="none"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="45"
        d="m288 336l80-80l-80-80M80 256h272"
      />
    </svg>
  </div>

  <Progress
    :percentage="progressValue"
    :width="55"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
    :color="progressValue ? '' : '#e0e0e0'"
  />
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.extra-e7-btn {
  position: absolute;
  width: 25px !important;
  height: 25px !important;
  padding: 2px;
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  background-color: #3498db !important;
  border-radius: 50% !important;
  transform: translate(240%, 0) rotate(180deg);
}

.progress-bar {
  position: absolute;
  transform: translate(0, -55px);
}
</style>
