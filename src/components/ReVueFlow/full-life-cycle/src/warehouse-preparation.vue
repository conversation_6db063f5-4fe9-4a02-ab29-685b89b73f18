<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import { Position, Handle } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
import { message } from "@/utils/message";
import { useI18n } from "vue-i18n";
// 接收传递的 `props`
const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  progress: {
    type: Number,
    default: null
  },
  handles: {
    type: Array,
    default: () => [
      {
        id: "target-c",
        type: "target",
        position: "Position-Top",
        connectable: false
      },
      {
        id: "source",
        type: "source",
        position: "Position-Right",
        connectable: false
      },
      {
        id: "target-b",
        type: "target",
        position: "Position-Left",
        connectable: false
      }
    ]
  }
});
const showAdditionalNodes = ref(false);
// 定义事件
const emit = defineEmits(["toggle-nodes-son"]);

// 切换节点的显示
const toggleNodes = show => {
  showAdditionalNodes.value = show;
  emit("toggle-nodes-son", showAdditionalNodes.value);
};

function isNotNullOrZero(value) {
  return value || value === 0 || value === 0.0;
}
const progressValue = computed(() => {
  console.log("props", isNotNullOrZero(props.progress));

  return isNotNullOrZero(props.progress) ? props.progress : false;
});
</script>

<template>
  <div>
    <div class="operator-node-li">{{ title }}</div>

    <Handle type="source" :position="Position.Right" :connectable="false" />

    <Handle
      id="target-b"
      type="target"
      :position="Position.Left"
      :connectable="false"
    />
    <Handle
      id="target-c"
      type="target"
      :position="Position.Top"
      :connectable="false"
    />
  </div>
  <button
    v-if="!showAdditionalNodes"
    class="extra-e7-btn"
    :style="locale === 'en' ? { right: '330px' } : {}"
    @click.stop="toggleNodes(1)"
  >
    +
  </button>
  <button
    v-if="showAdditionalNodes"
    class="extra-e7-btn"
    @click.stop="toggleNodes(0)"
  >
    -
  </button>
  <Progress
    :percentage="progressValue"
    :width="55"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
    :color="progressValue ? '' : '#e0e0e0'"
  />
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.extra-e7-btn {
  position: absolute;
  right: 30px;
  width: 25px !important;
  height: 25px !important;
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  background-color: #3498db !important;
  border-radius: 50% !important;
  transform: translate(270%, 0);
}

.progress-bar {
  position: absolute;
  transform: translate(0, -55px);
}
</style>
