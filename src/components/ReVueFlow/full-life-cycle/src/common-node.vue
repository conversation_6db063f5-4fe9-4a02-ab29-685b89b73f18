<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import { Position, Handle } from "@vue-flow/core";
import Progress from "../../../ReProgress/index.vue";
// 接收传递的 `props`
const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  progress: {
    type: Number,
    default: null
  },
  handles: {
    type: Array,
    default: () => [{ id: "sales-order", type: "source", position: "right" }]
  },
  hiddenProgress: {
    type: Boolean,
    default: false
  }
});

const hiddenProgress = computed(() => {
  return props.hiddenProgress;
});

function isNotNullOrZero(value) {
  return value || value === 0 || value === 0.0;
}
const progressValue = computed(() => {
  console.log(props.progress, isNotNullOrZero(props.progress), "11");

  return isNotNullOrZero(props.progress) ? props.progress : false;
});
</script>

<template>
  <div>
    <div class="operator-node-li">{{ title }}</div>

    <Handle
      v-for="handle in handles"
      :key="handle.id"
      :type="handle.type"
      :position="
        handle.position == 'left'
          ? Position.Left
          : handle.position == 'right'
            ? Position.Right
            : handle.position == 'top'
              ? Position.Top
              : Position.Bottom
      "
      :connectable="false"
    />
  </div>

  <Progress
    v-if="!hiddenProgress"
    :percentage="isNotNullOrZero(progressValue) ? progressValue : null"
    :width="55"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
    :color="progressValue ? '' : '#e0e0e0'"
  />
</template>

<style scoped>
.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(0, -55px);
}
</style>
