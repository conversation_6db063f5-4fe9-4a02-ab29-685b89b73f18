import {
  getProductOrderProgress,
  getMRPProgress,
  getPlanOrderProgress
} from "@/api/full-lifecycle/progress";
export const useProgressTotal = async billNo => {
  // 查询现有的进度条
  const [res1, res2, res3] = await Promise.all([
    getProductOrderProgress(billNo),
    getMRPProgress(billNo),
    getPlanOrderProgress(billNo)
  ]);

  // 将所有结果存储在一个数组中
  let results = [res1, res2, res3];

  results = results.map(item => {
    if (item.code === 200) {
      return item.data;
    } else {
      return 0;
    }
  });

  return { results };
};
