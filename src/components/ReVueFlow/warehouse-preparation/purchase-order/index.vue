<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from "vue";
import { VueFlow, Position, MarkerType } from "@vue-flow/core";
import { MiniMap } from "@vue-flow/minimap";
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";
import CommonNode from "./src/common-node.vue";
import { useRoute } from "vue-router";
import { useProgressTotal } from "./hooks/useProgressTotal";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { throttle } from "lodash";
import { getPurchaseOrderProgressAPI } from "@/api/full-lifecycle/warehouse-preparation/purchase-order";
const nodeStore = useNodeStore();
// 定义节点状态
const STATUS = {
  PENDING: "pending",
  IN_PROGRESS: "in-progress",
  COMPLETED: "completed"
};
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n(); // 解构出t方法
const emit = defineEmits(["clickNode"]);
// 初始节点和边
const initialNodes = ref([
  {
    id: "1",
    label: "采购订单",
    position: locale.value == "en" ? { x: 0, y: 50 } : { x: 10, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.COMPLETED,
    highlighted: true,
    labelBgBorderRadius: 4,
    labelBgStyle: { fill: "#FFCC00", color: "#fff", fillOpacity: 0.7 },
    // targetPosition: Position.Right
    sourcePosition: Position.Right,
    type: "purchase-order"
  },
  {
    id: "2",
    label: "入库上架",
    position: locale.value == "en" ? { x: 210, y: 50 } : { x: 210, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.IN_PROGRESS,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "inbound-shelves"
  },
  {
    id: "3",
    label: "来料检验",
    position: locale.value == "en" ? { x: 480, y: 50 } : { x: 410, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "material-inspection"
  },
  {
    id: "4",
    label: "采购送货",
    position: locale.value == "en" ? { x: 740, y: 50 } : { x: 610, y: 50 },
    dimensions: { width: 150, height: 40 },
    status: STATUS.PENDING,
    highlighted: false,
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    type: "purchase-delivery"
  }
]);

const initialEdges = ref([
  {
    id: "e1-1",
    source: "1",
    target: "2",
    animated: false,
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e1-2",
    source: "2",
    target: "3",
    label: "",

    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e2-3",
    source: "2",
    target: "3",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  },
  {
    id: "e3-4",
    source: "3",
    target: "4",
    label: "",
    markerEnd: MarkerType.ArrowClosed,
    markerStart: MarkerType.ArrowClosed
  }
]);
// 控制显示额外节点
const showAdditionalNodes = ref(false);
// 每个节点点击事件
const nodeClickHandler = async props => {
  const clickedNodeId = props.node.id;
  initialNodes.value = await initialNodes.value.map(node => ({
    ...node,
    highlighted: node.id === clickedNodeId
  }));

  updateNodeStyles(); // 更新节点样式
  nodeStore.setSonPurchaseOrder({ id: props.node.id, label: props.node.label }); // 保存节点信息到 Store 持久化
  emit("clickNode", props.node.label);
};
const vueFlowInstance = ref(null);

const onFlowInit = instance => {
  vueFlowInstance.value = instance;

  // 初次适配节点范围
  instance.fitView({ padding: 0.4, includeHiddenNodes: true });
};
// 监听窗口变化 这个死代码主要解决了窗口变化后，全局的流程恢复居中显示
const handleResize = throttle(() => {
  vueFlowInstance.value.fitView({ padding: 0.3, includeHiddenNodes: true });
}, 400); // 每 200ms 执行一次

// 定义节点进度数据
const nodePrecents = ref(new Map());
// 定义数量统计数据
const quantityStatistics = ref(new Map());

const route = useRoute();

// 抽取数据加载逻辑为独立函数
const loadData = async () => {
  // 获取本地存储的节点值，如果没有则使用默认值
  const storedNodeId = nodeStore.sonPurchaseOrder.id;
  const storedNodeLabel = nodeStore.sonPurchaseOrder.label;
  const simulatedNode = { node: { id: storedNodeId, label: storedNodeLabel } };
  const orderNo = nodeStore.queryId;

  // 获取采购订单进度
  try {
    const progressResult = await getPurchaseOrderProgressAPI(orderNo);
    console.debug("采购订单进度:", progressResult.data);

    // 更新百分比数据
    nodePrecents.value.set("inbound", progressResult.data.inboundPercent);
    nodePrecents.value.set("order", progressResult.data.orderPercent);
    nodePrecents.value.set("deliver", progressResult.data.deliverPercent);
    nodePrecents.value.set("inspection", progressResult.data.inspectionPercent);

    // 更新数量统计数据
    quantityStatistics.value.set(
      "qualified",
      progressResult.data.totalQualifiedQuantity
    );
    quantityStatistics.value.set(
      "execute",
      progressResult.data.totalExecuteQuantity
    );
    quantityStatistics.value.set(
      "delivery",
      progressResult.data.totalDeliveryNumber
    );
    quantityStatistics.value.set(
      "inspection",
      progressResult.data.totalInspectionQuantity
    );
  } catch (error) {
    console.error("获取采购订单进度失败:", error);
  }

  nodeClickHandler(simulatedNode);
};

// 监听路由变化
watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    // 判断是否是从外部路由进入
    const currentRoutePrefix = "/preparation/purchase-order";
    const isEnteringFromOutside =
      !oldPath.includes(currentRoutePrefix) &&
      newPath.includes(currentRoutePrefix);

    if (isEnteringFromOutside) {
      loadData();
    }
  }
);

onMounted(async () => {
  await loadData();
  window.addEventListener("resize", handleResize);
});

// 计算节点样式
const getNodeStyle = (status, highlighted) => {
  // 对应类型的节点样式
  let backgroundColor;
  switch (status) {
    case STATUS.COMPLETED:
      backgroundColor = "#19CAAD";

      break;
    case STATUS.IN_PROGRESS:
      backgroundColor = "#19CAAD";
      break;
    default:
      backgroundColor = "#19CAAD";
      break;
  }
  // 每个节点的样式
  return {
    background: highlighted ? "#0099cc" : backgroundColor, // 高亮节点为蓝色黄色
    color: highlighted ? "#ccccccc" : "white",
    boxShadow: highlighted
      ? "rgba(244, 96, 108, 0.56) 0px 22px 70px 4px"
      : "none",
    width: 100,
    height: 50,
    borderRadius: "8px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boder: "none",
    "--vf-node-color": "1px solid #fff"
  };
};

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
// // 更新节点样式
const updateNodeStyles = () => {
  initialNodes.value.forEach(node => {
    node.style = getNodeStyle(node.status, node.highlighted);
  });
};

updateNodeStyles(); // 初始化节点样式

// 仓库备料子节点显示隐藏
const toggleNodes = state => {
  showAdditionalNodes.value = !!state;
  initialNodes.value = initialNodes.value.map(node => {
    if (node.id.startsWith("extra-")) {
      return { ...node, hidden: !showAdditionalNodes.value };
    }
    return node;
  });
};

// 获取节点拖拽后的位置坐标、位置偏移量等关键信息
const dragNode = props => {
  // console.log(props);
};

// 整体的位移变化
const flowMove = props => {
  // console.log(props);
};
</script>

<template>
  <div id="vue-flow-container" style="width: 100%">
    <VueFlow
      ref="vueFlowInstance"
      class="math-flow flowchat-container"
      :nodes="initialNodes"
      :edges="initialEdges"
      :nodes-draggable="false"
      fit-view-on-init
      :zoom-on-scroll="true"
      :zoom-on-pinshoot="true"
      :prevent-scrolling="true"
      max-zoom="1.1"
      min-zoom="0.5"
      @init="onFlowInit"
      @nodeDrag="dragNode"
      @nodeClick="nodeClickHandler"
      @move="flowMove"
    >
      <!-- 采购订单 -->
      <template #node-purchase-order>
        <CommonNode
          :title="t('procurementNodes.purePurchaseOrder')"
          :progress="20"
          :number="nodePrecents.get('order')"
          :quantity="quantityStatistics.get('qualified')"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 入库上架 -->
      <template #node-inbound-shelves>
        <CommonNode
          :title="t('procurementNodes.pureWarehousingShelves')"
          :progress="20"
          :number="nodePrecents.get('inbound')"
          :quantity="quantityStatistics.get('qualified')"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 来料检验 -->
      <template #node-material-inspection>
        <CommonNode
          :title="t('procurementNodes.pureIncomingInspection')"
          :progress="20"
          :number="nodePrecents.get('inspection')"
          :quantity="quantityStatistics.get('inspection')"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <!-- 采购送货 -->
      <template #node-purchase-delivery>
        <CommonNode
          :title="t('procurementNodes.pureProcurementDelivery')"
          :progress="20"
          :number="nodePrecents.get('deliver')"
          :quantity="quantityStatistics.get('delivery')"
          :handles="[
            { id: 'source', type: 'source', position: 'right' },
            { id: 'target', type: 'target', position: 'left' }
          ]"
        />
      </template>
      <Background />
      <Controls :showInteractive="false" />
      <!-- <MiniMap
        v-if="!showAdditionalNodes"
        pannable
        zoomable
        height="100"
        width="150"
      /> -->
    </VueFlow>
  </div>
</template>

<style>
@import url("@/style/flow/style.css");
@import url("@/style/flow/theme-default.css");
@import url("@/style/flow/minmap.css");
@import url("@/style/flow/controls.css");
@import url("@/style/flow/node-resizer.css");
</style>
<style scoped>
#vue-flow-container {
  width: 100%;
  position: relative;
}
.total {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  font-size: 1.2em;
  color: #406eeb;
}
.flowchat-container {
  height: 240px !important;
  border: #ccc 1px solid;
  border-radius: 5px;
}

.vue-flow__minimap {
  transform: scale(75%);
  transform-origin: bottom right;
}
</style>
<style scoped>
@media (width <= 1200px) {
  .flowchat-container {
    height: 280px !important;
  }

  .vue-flow__minimap {
    transform: scale(65%);
    transform-origin: bottom right;
  }
}

@media (width <= 992px) {
  .flowchat-container {
    height: 260px !important;
  }

  .vue-flow__minimap {
    transform: scale(55%);
    transform-origin: bottom right;
  }
}

@media (width <= 768px) {
  .flowchat-container {
    height: 220px !important;
  }

  .vue-flow__minimapfw {
    transform: scale(45%);
    transform-origin: bottom right;
  }
}

@media (width <= 576px) {
  .flowchat-container {
    height: 200px !important;
  }

  .vue-flow__minimap {
    transform: scale(35%);
    transform-origin: bottom right;
  }
}

.extra-e7-btn {
  position: absolute;
  width: 25px !important;
  height: 25px !important;
  font-size: 25px;
  line-height: 25px;
  color: #fff;
  background-color: #3498db !important;
  border-radius: 50% !important;
  transform: translate(270%, 0);
}

:deep(.vue-flow__viewport) {
  /* 去掉 点点 的背景 */
  background: #edf2f7;
}

.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(0, -60px);
}

:deep(.vue-flow__node-default, .vue-flow__node-input, .vue-flow__node-output) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 20px;
  color: #fff;
  background-color: #19caad;
  border-radius: 8px;
}

.math-flow {
  width: 100%;
  height: 100%;
  background-color: #edf2f7;
}

.vue-flow__handle {
  width: 10px;
  height: 14px;
  background: #aaa;
  border-radius: 4px;
}

.vue-flow__edges path {
  stroke-width: 3;
}

.vue-flow__node {
  background-color: #f3f4f6;
}

.vue-flow__node-value {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-value.selected {
  box-shadow: 0 0 0 2px #ec4899;
}

.vue-flow__node-value input {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 10px #0000001a;
}

.vue-flow__node-value input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #ec4899;
  transition: box-shadow 0.2s;
}

.vue-flow__node-value .vue-flow__handle {
  background-color: #ec4899;
}

.vue-flow__node-operator {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-operator.selected {
  box-shadow: 0 0 0 2px #2563eb;
}

.vue-flow__node-operator button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
  background-color: #4a5568;
  border: none;
  border-radius: 8px;
  box-shadow: 0 0 10px #0000004d;
}

.vue-flow__node-operator button svg {
  width: 100%;
  height: 100%;
}

.vue-flow__node-operator button:hover {
  background-color: #2563eb;
  transition: background-color 0.2s;
}

.vue-flow__node-operator button.selected {
  background-color: #2563eb;
}

/* 左上角句柄 */
.vue-flow__node-operator .vue-flow__handle[data-handleid="target-a"] {
  top: 25%;
}

/* 左下角句柄 */
.vue-flow__node-operator .vue-flow__handle[data-handleid="target-b"] {
  top: 50%;
}

.vue-flow__node-operator .vue-flow__handle {
  background-color: #2563eb;
}

.vue-flow__handle-top {
  transform: rotate(90deg) translate(-10px, 5px);
}

.vue-flow__handle-bottom {
  transform: rotate(90deg) translate(10px, 5px);
}

.vue-flow__node-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 0 10px #0003;
}

.vue-flow__node-result.selected {
  box-shadow: 0 0 0 2px #5ec697;
}

.vue-flow__node-result .result {
  display: flex;
  gap: 8px;
  font-size: 24px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  margin: 0;
  appearance: none;
}

input[type="number"] {
  appearance: textfield;
}

/* @import "https://cdn.jsdelivr.net/npm/@vue-flow/core@1.41.5/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/core@1.41.5/dist/theme-default.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/controls@latest/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/minimap@latest/dist/style.css";
@import "https://cdn.jsdelivr.net/npm/@vue-flow/node-resizer@latest/dist/style.css"; */
</style>
