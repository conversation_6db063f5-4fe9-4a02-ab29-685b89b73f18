<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import { Posi<PERSON>, <PERSON><PERSON> } from "@vue-flow/core";
import Progress from "../../../../ReProgress/index.vue";
// 接收传递的 `props`
const props = defineProps({
  title: {
    type: String,
    default: "销售订单"
  },
  progress: {
    type: Number,
    default: 0
  },
  number: {
    type: Number
  },
  quantity: {
    type: Number
  },
  handles: {
    type: Array,
    default: () => [{ id: "sales-order", type: "source", position: "right" }]
  }
});

function isNotNullOrZero(value) {
  if (value || value == 0) {
    return true;
  }
  return false;
}

const progressValue = computed(() => {
  console.debug(props.number, isNotNullOrZero(props.number), "11");

  return isNotNullOr<PERSON>ero(props.number) ? props.number : false;
});
</script>

<template>
  <div class="node-container">
    <!-- <div class="precents">{{ "数量:" + quantity + " " + number + "%" }}</div> -->
    <Progress
      v-if="!hiddenProgress"
      :percentage="isNotNullOrZero(progressValue) ? progressValue : null"
      :width="55"
      style="font-size: 12px"
      :stroke-width="4"
      class="progress-bar"
      :color="progressValue ? '' : '#e0e0e0'"
    />
    <div class="operator-node-li">{{ title }}</div>

    <Handle
      v-for="handle in handles"
      :key="handle.id"
      :type="handle.type"
      :position="
        handle.position == 'left'
          ? Position.Left
          : handle.position == 'right'
            ? Position.Right
            : handle.position == 'top'
              ? Position.Top
              : Position.Bottom
      "
      :connectable="false"
    />
  </div>

  <!-- <Progress
    :percentage="progress"
    :width="50"
    style="font-size: 15px"
    :stroke-width="4"
    class="progress-bar"
  /> -->
</template>

<style scoped>
.node-container {
  position: relative;
}

.precents {
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100%;
  min-width: 150px;
  overflow: hidden;
  font-size: 14px;
  color: #000;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.operator-node-li {
  display: flex;
  align-items: center;
  justify-content: center;

  /* background-color: #19caad; */
  height: 40px;
  padding: 0 20px;
  color: #fff;
  border-radius: 8px;
}

.progress-bar {
  position: absolute;
  transform: translate(28px, -60px);
}
</style>
