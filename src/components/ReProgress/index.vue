<template>
  <el-progress
    :type="props.type"
    :percentage="props.percentage"
    :stroke-width="props.strokeWidth"
    :text-inside="props.textInside"
    :status="computedStatus"
    :indeterminate="props.indeterminate"
    :duration="props.duration"
    :width="props.width"
    :show-text="props.showText"
    :stroke-linecap="props.strokeLinecap"
    :format="props.format"
    :striped="props.striped"
    :striped-flow="props.stripedFlow"
    :style="dynamicStyle"
  >
    <template #default="{ percentage }">
      <span style="display: block" :style="props.style">{{
        typeof percentage === "number" ? `${percentage}%` : ""
      }}</span>
    </template>
  </el-progress>
</template>

<script setup>
import { defineProps, computed } from "vue";

const props = defineProps({
  percentage: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    default: "circle"
  },
  strokeWidth: {
    type: Number,
    default: 6
  },
  textInside: {
    type: <PERSON>olean,
    default: false
  },
  status: {
    type: String,
    default: ""
  },
  indeterminate: {
    type: <PERSON>olean,
    default: false
  },
  duration: {
    type: Number,
    default: 3
  },
  color: {
    type: [String, Function, Array],
    default: ""
  },
  width: {
    type: Number,
    default: 126
  },
  showText: {
    type: Boolean,
    default: true
  },
  strokeLinecap: {
    type: String,
    default: "round"
  },
  format: {
    type: Function,
    default: percentage => `${percentage}%`
  },
  striped: {
    type: Boolean,
    default: false
  },
  stripedFlow: {
    type: Boolean,
    default: false
  },
  style: {
    type: Object,
    default: () => ({
      marginTop: "10px",
      fontSize: "28px"
    })
  }
});

const computedStatus = computed(() => {
  if (props.percentage >= 75) return "";
  if (props.percentage < 75 && props.percentage >= 50) return "warning";
  return "exception";
});

// 计算动态样式
const dynamicStyle = computed(() => {
  return {
    "--el-progress-circle-track-color": props.color // 使用CSS变量来设置背景颜色
  };
});
</script>

<style scoped>
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}

.demo-progress .el-progress--line {
  max-width: 600px;
  margin-bottom: 15px;
}

.demo-progress .el-progress--circle {
  margin-right: 15px;
}

:deep(.el-progress-circle__track) {
  stroke: var(--el-progress-circle-track-color); /* 使用 CSS 变量 */
}
</style>
