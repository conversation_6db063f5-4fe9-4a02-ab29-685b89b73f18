<script setup lang="ts">
import { ref } from "vue";

const emit = defineEmits<{
  (e: "send", message: string, files: File[]): void;
}>();

const message = ref("");
const fileInput = ref<HTMLInputElement | null>(null);
const selectedFiles = ref<File[]>([]);

const handleSend = () => {
  if (message.value.trim() || selectedFiles.value.length > 0) {
    emit("send", message.value, selectedFiles.value);
    message.value = "";
    selectedFiles.value = [];
    if (fileInput.value) {
      fileInput.value.value = "";
    }
  }
};

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    selectedFiles.value = Array.from(input.files);
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleSend();
  }
};
</script>

<template>
  <div class="border-t p-4">
    <div class="flex items-center gap-4 mb-4">
      <button
        class="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 border rounded-md hover:bg-gray-50"
        @click="() => fileInput?.click()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
          />
        </svg>
        Attach files
      </button>
      <input
        ref="fileInput"
        type="file"
        multiple
        class="hidden"
        @change="handleFileChange"
      />
      <div v-if="selectedFiles.length > 0" class="text-sm text-gray-600">
        {{ selectedFiles.length }} file(s) selected
      </div>
    </div>
    <div class="flex gap-4">
      <textarea
        v-model="message"
        rows="1"
        class="flex-1 p-2 border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="Type your message..."
        @keydown="handleKeydown"
      />
      <button
        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        @click="handleSend"
      >
        Send
      </button>
    </div>
  </div>
</template>
