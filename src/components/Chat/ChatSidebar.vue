<script setup lang="ts">
import { ref, computed } from "vue";
import type { Chat } from "../types/chat";
import dayjs from "dayjs";
import ContextMenu from "./ContextMenu.vue";

const props = defineProps<{
  chats: Chat[];
  currentChatId: string | null;
}>();

const emit = defineEmits<{
  (e: "select", chatId: string): void;
  (e: "new"): void;
  (e: "delete", chatId: string): void;
  (e: "rename", chatId: string, newTitle: string): void;
}>();

const isCollapsed = ref(false);

const contextMenu = ref({
  show: false,
  x: 0,
  y: 0,
  chatId: ""
});

const editingTitle = ref({
  chatId: "",
  title: ""
});

const sortedChats = computed(() => {
  return [...props.chats].sort((a, b) => b.createdAt - a.createdAt);
});

const formatDate = (timestamp: number) => {
  return dayjs(timestamp).format("MM/DD HH:mm");
};

const handleContextMenu = (event: MouseEvent, chat: Chat) => {
  event.preventDefault();
  contextMenu.value = {
    show: true,
    x: event.clientX,
    y: event.clientY,
    chatId: chat.id
  };
};

const handleRename = () => {
  const chat = props.chats.find(c => c.id === contextMenu.value.chatId);
  if (chat) {
    editingTitle.value = {
      chatId: chat.id,
      title: chat.title
    };
  }
  contextMenu.value.show = false;
};

const handleDelete = () => {
  emit("delete", contextMenu.value.chatId);
  contextMenu.value.show = false;
};

const handleTitleSubmit = (event: Event, chat: Chat) => {
  event.preventDefault();
  if (editingTitle.value.title.trim()) {
    emit("rename", chat.id, editingTitle.value.title);
    editingTitle.value.chatId = "";
  }
};

const closeContextMenu = () => {
  contextMenu.value.show = false;
};

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<template>
  <div
    :class="[
      'border-r bg-gray-50 flex flex-col transition-all duration-300 ease-in-out relative',
      isCollapsed ? 'w-16' : 'w-64'
    ]"
    style="height: 85vh"
  >
    <button
      class="absolute -right-3 top-1/2 transform -translate-y-1/2 bg-gray-200 rounded-full p-1 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 z-10"
      @click="toggleSidebar"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 transform transition-transform duration-300"
        :class="{ 'rotate-180': isCollapsed }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        />
      </svg>
    </button>

    <div class="p-4 border-b">
      <button
        v-if="!isCollapsed"
        class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        @click="emit('new')"
      >
        New Chat
      </button>
      <button
        v-else
        class="w-full p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        @click="emit('new')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mx-auto"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
      </button>
    </div>

    <div class="flex-1 overflow-y-auto">
      <div
        v-for="chat in sortedChats"
        :key="chat.id"
        :class="[
          'cursor-pointer hover:bg-gray-100 transition-colors',
          chat.id === currentChatId ? 'bg-blue-50' : '',
          isCollapsed ? 'p-2' : 'p-3'
        ]"
        @click="emit('select', chat.id)"
        @contextmenu="handleContextMenu($event, chat)"
      >
        <form
          v-if="editingTitle.chatId === chat.id && !isCollapsed"
          class="flex items-center gap-2"
          @submit="handleTitleSubmit($event, chat)"
          @click.stop
        >
          <input
            ref="titleInput"
            v-model="editingTitle.title"
            type="text"
            class="flex-1 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            @blur="handleTitleSubmit($event, chat)"
          />
        </form>
        <template v-else>
          <div v-if="!isCollapsed">
            <div class="font-medium truncate">{{ chat.title }}</div>
            <div class="text-xs text-gray-500">
              {{ formatDate(chat.createdAt) }}
            </div>
          </div>
          <div
            v-else
            class="w-8 h-8 mx-auto bg-gray-200 rounded-full flex items-center justify-center"
          >
            <span class="text-sm font-medium">{{ chat.title.charAt(0) }}</span>
          </div>
        </template>
      </div>
    </div>

    <ContextMenu
      :show="contextMenu.show"
      :x="contextMenu.x"
      :y="contextMenu.y"
      @rename="handleRename"
      @delete="handleDelete"
      @close="closeContextMenu"
    />
  </div>
</template>

<style scoped>
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175 / 50%) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175 / 50%);
  border-radius: 3px;
}
</style>
