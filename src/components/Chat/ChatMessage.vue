<script setup lang="ts">
import { computed } from "vue";
import type { Message } from "../types/chat";
import dayjs from "dayjs";

const props = defineProps<{
  message: Message;
}>();

const formattedTime = computed(() => {
  return dayjs(props.message.timestamp).format("HH:mm:ss");
});

const isAi = computed(() => props.message.sender === "ai");
</script>

<template>
  <div :class="['flex gap-4 p-4', isAi ? 'justify-start' : 'justify-end']">
    <div
      :class="[
        'max-w-[80%] flex gap-4',
        isAi ? 'flex-row' : 'flex-row-reverse'
      ]"
    >
      <div class="flex-shrink-0">
        <div
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center',
            isAi ? 'bg-blue-500' : 'bg-green-500'
          ]"
        >
          <span class="text-white text-sm">{{ isAi ? "AI" : "U" }}</span>
        </div>
      </div>
      <div :class="['flex flex-col', isAi ? 'items-start' : 'items-end']">
        <div
          :class="[
            'p-3 rounded-lg',
            isAi ? 'bg-gray-100' : 'bg-blue-500 text-white'
          ]"
        >
          <div>{{ message.content }}</div>
          <div
            v-if="message.files && message.files.length > 0"
            class="mt-2 space-y-2"
          >
            <div
              v-for="file in message.files"
              :key="file.name"
              class="flex items-center gap-2 text-sm"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                />
              </svg>
              <span>{{ file.name }}</span>
            </div>
          </div>
        </div>
        <span class="text-xs text-gray-500 mt-1">{{ formattedTime }}</span>
      </div>
    </div>
  </div>
</template>
