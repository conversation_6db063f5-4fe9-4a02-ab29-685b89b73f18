<template>
  <div
    class="flex gap-4 p-4 cursor-pointer"
    :class="[isAI ? 'justify-start' : 'justify-end']"
    @click="$emit('click')"
  >
    <template v-if="isAI">
      <div
        class="w-8 h-8 rounded-full bg-blue-500 flex-shrink-0 flex items-center justify-center text-white"
      >
        AI
      </div>
      <div class="flex-1 max-w-[80%]">
        <div
          class="prose bg-white rounded-lg p-3 shadow-sm whitespace-pre-wrap text-left"
          v-html="formattedContent"
        />
        <button
          class="text-sm text-blue-500 hover:text-blue-700 mt-2"
          @click.stop="copyContent"
        >
          复制
        </button>
      </div>
    </template>
    <template v-else>
      <div class="flex-1 max-w-[80%] flex justify-end">
        <div
          class="prose bg-blue-500 text-white rounded-lg p-3 shadow-sm whitespace-pre-wrap text-left"
          v-html="formattedContent"
        />
        <button
          class="text-sm text-blue-500 hover:text-blue-700 mt-2"
          @click.stop="copyContent"
        >
          复制
        </button>
        <!-- <div
        class="w-8 h-8 rounded-full bg-gray-500 flex-shrink-0 flex items-center justify-center text-white"
      >
        U
      </div> -->
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { marked } from "marked";

const props = defineProps<{
  content: string;
  isAI: boolean;
}>();

defineEmits<{
  (e: "click"): void;
}>();

const formattedContent = computed(() => {
  return marked(props.content, {
    breaks: true,
    gfm: true
  });
});

function copyContent() {
  navigator.clipboard.writeText(props.content).then(() => {
    alert("内容已复制到剪贴板");
  });
}
</script>
