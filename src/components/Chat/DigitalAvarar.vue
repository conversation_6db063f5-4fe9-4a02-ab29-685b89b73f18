<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import lottie from "lottie-web";
import { useStorage } from "@vueuse/core";

const props = defineProps<{
  isActive: boolean;
  isSpeaking: boolean;
}>();

const avatarContainer = ref<HTMLDivElement | null>(null);
const animation = ref<any>(null);
const isDragging = ref(false);
const position = useStorage("digital-avatar-position", {
  x: window.innerWidth - 300,
  y: 100
});

// Using a more realistic human character animation
const AVATAR_ANIMATIONS = {
  IDLE: "https://assets10.lottiefiles.com/packages/lf20_kk62um5v.json",
  TALKING: "https://assets2.lottiefiles.com/packages/lf20_myejiggj.json",
  THINKING: "https://assets8.lottiefiles.com/packages/lf20_wdqlqkhq.json"
};

let currentAnimation = ref("IDLE");

const loadAnimation = async (type: keyof typeof AVATAR_ANIMATIONS) => {
  if (avatarContainer.value) {
    if (animation.value) {
      animation.value.destroy();
    }

    animation.value = lottie.loadAnimation({
      container: avatarContainer.value,
      renderer: "svg",
      loop: true,
      autoplay: true,
      path: AVATAR_ANIMATIONS[type]
    });

    currentAnimation.value = type;
  }
};

onMounted(async () => {
  await loadAnimation("IDLE");
  startRandomAnimations();
});

const updateAnimationState = async () => {
  if (props.isSpeaking && currentAnimation.value !== "TALKING") {
    await loadAnimation("TALKING");
  } else if (!props.isSpeaking && currentAnimation.value !== "IDLE") {
    await loadAnimation("IDLE");
  }
};

// Dragging functionality
const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const target = avatarContainer.value?.parentElement;
  if (!target) return;

  const rect = target.getBoundingClientRect();
  const offsetX = e.clientX - rect.left;
  const offsetY = e.clientY - rect.top;

  const onMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return;
    position.value = {
      x: Math.max(0, Math.min(window.innerWidth - 200, e.clientX - offsetX)),
      y: Math.max(0, Math.min(window.innerHeight - 200, e.clientY - offsetY))
    };
  };

  const onMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener("mousemove", onMouseMove);
    document.removeEventListener("mouseup", onMouseUp);
  };

  document.addEventListener("mousemove", onMouseMove);
  document.addEventListener("mouseup", onMouseUp);
};

// Random idle animations and expressions
const startRandomAnimations = () => {
  setInterval(async () => {
    if (!props.isSpeaking && Math.random() > 0.7) {
      await loadAnimation("THINKING");
      setTimeout(async () => {
        if (!props.isSpeaking) {
          await loadAnimation("IDLE");
        }
      }, 3000);
    }
  }, 10000);
};

watch(() => props.isSpeaking, updateAnimationState);

// Handle window resize
const handleResize = () => {
  position.value = {
    x: Math.min(position.value.x, window.innerWidth - 200),
    y: Math.min(position.value.y, window.innerHeight - 200)
  };
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
});
</script>

<template>
  <div
    class="fixed transition-all duration-300 ease-in-out cursor-move z-50 select-none"
    :style="{
      left: `${position.x}px`,
      top: `${position.y}px`,
      transform: isDragging ? 'scale(1.02)' : 'scale(1)'
    }"
    @mousedown="startDrag"
  >
    <div
      ref="avatarContainer"
      class="w-64 h-64 hover:scale-105 transition-transform duration-300"
    />
    <div
      class="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm shadow-lg"
      :class="{ 'opacity-0': !isDragging }"
    >
      Drag me
    </div>
  </div>
</template>

<style scoped>
.select-none {
  user-select: none;
}
</style>
