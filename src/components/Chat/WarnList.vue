<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import { VxeTable, VxeColumn } from "vxe-table";
import "vxe-table/lib/style.css";

export interface Record {
  id: number | string;
  [key: string]: any;
}

interface Column {
  key: string;
  title: string;
  width?: number;
}

interface Props {
  data: Record[];
  columns: Column[];
  scrollStep?: number;
  interval?: number;
  height?: number;
  width?: number;
  theme?: "dark" | "light";
  autoplay?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  scrollStep: 1,
  interval: 3000,
  height: 250,
  theme: "dark",
  autoplay: true
});

const tableRef = ref<InstanceType<typeof VxeTable>>();
const displayRecords = ref<Record[]>([]);
const scrollInterval = ref<number | null>(null);
const isPaused = ref(false);

onMounted(() => {
  initializeList();
});

onUnmounted(() => {
  stopScroll();
});

watch(() => props.data, initializeList, { deep: true });
watch(
  () => props.autoplay,
  newValue => {
    if (newValue) startScroll();
    else stopScroll();
  }
);

function initializeList() {
  displayRecords.value = [...props.data];
  if (props.autoplay) startScroll();
}

function startScroll() {
  stopScroll();
  scrollInterval.value = setInterval(() => {
    if (isPaused.value) return;

    // 移动数据实现滚动效果
    const itemsToMove = displayRecords.value.slice(0, props.scrollStep);
    displayRecords.value = [
      ...displayRecords.value.slice(props.scrollStep),
      ...itemsToMove
    ];
  }, props.interval);
}

function stopScroll() {
  if (scrollInterval.value) {
    clearInterval(scrollInterval.value);
    scrollInterval.value = null;
  }
}

function pauseScroll() {
  isPaused.value = true;
}

function resumeScroll() {
  isPaused.value = false;
}
</script>

<template>
  <vxe-table
    ref="tableRef"
    :data="displayRecords"
    class="my-table-scrollbar"
    :height="props.height"
    :max-height="props.height"
    :column-config="{ drag: false, resizable: false }"
    :resizable-config="{ dragMode: 'fixed' }"
    :row-config="{
      isHover: true,
      height: 45,
      resizable: false
    }"
    :style="{ width: props.width + 'px' }"
    :class="[theme]"
    highlight-hover-row
    :resizable="true"
    row-config.useKey="true"
    :size="'medium'"
    :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
    :scroll-x="{
      enabled: true,
      gt: 0,
      mode: 'wheel',
      scrollToLeftOnChange: false
    }"
    :autoHeight="true"
    show-overflow
    :round="false"
    border="none"
    :auto-resize="false"
    :sync-resize="false"
    show-footer-overflow
    @mouseenter="pauseScroll"
    @mouseleave="resumeScroll"
  >
    <vxe-column
      v-for="col in columns"
      :key="col.key"
      :field="col.key"
      :title="col.title"
      :width="col.width"
    >
      <template #default="{ row }">
        <span v-if="col.key == 'type'" style="color: #4897ff">{{
          row[col.key]
        }}</span>
        <div v-else class="cell-content">{{ row[col.key] }}</div>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<style scoped>
.vxe-table {
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s;
}

.vxe-table.dark {
  --vxe-table-header-background-color: #002140;
  --vxe-table-body-background-color: #001529;
  --vxe-table-row-hover-background-color: rgb(24 144 255 / 10%);
  --vxe-table-row-striped-background-color: rgb(0 33 64 / 30%);
  --vxe-font-color: white;
}

.cell-content {
  padding: 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<style lang="scss" scoped>
.my-table-scrollbar {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
  }
}

/** 默认模式 */
[data-vxe-ui-theme="light"] {
  .my-table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #fff;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #bfbfbf;
    }

    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #787878;
    }
  }
}

/** 暗黑模式 */
[data-vxe-ui-theme="dark"] {
  .my-table-scrollbar {
    ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-corner {
      background-color: #151518;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #4d7cab;
    }

    .selected-info .pagination-container-fixed-selected {
      background-color: #151518;
    }

    ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:active {
      background-color: #376593;
    }
  }
}
</style>
