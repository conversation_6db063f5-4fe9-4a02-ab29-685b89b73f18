<template>
  <div class="arrow-divider">
    <el-tooltip
      :content="isVisible ? '列表全屏' : '退出列表全屏'"
      placement="top"
      effect="dark"
    >
      <span
        class="arrow"
        :class="{ flipped: !isVisible }"
        @click="toggleVisibility"
      >
        <el-icon>
          <DArrowLeft />
        </el-icon>
      </span>
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { ElIcon, ElTooltip } from "element-plus";
import { DArrowLeft } from "@element-plus/icons-vue";
import { useVisibilityStore } from "@/store/modules/showBox";

const visibilityStore = useVisibilityStore();

const toggleVisibility = () => {
  visibilityStore.toggleVisibility();
};

const isVisible = computed(() => visibilityStore.isVisible);
</script>

<style scoped>
.arrow-divider {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 10px;
  margin: 10px 10px 0 0;
  cursor: pointer; /* 改为点击 */
  background: transparent;

  /* border-top: 2px dashed #ccc; 使用虚线作为分割线 */
}

.arrow {
  position: absolute;

  /* top: -15px; 让箭头在分割线上方 */
  padding: 0 5px;
  font-size: 16px;
  color: #666; /* 箭头颜色 */
  transition: transform 0.3s ease; /* 添加过渡效果 */
  transform: rotate(90deg); /* 切换箭头方向 */
}

.arrow.flipped {
  transform: rotate(-90deg); /* 切换箭头方向 */
}
</style>
