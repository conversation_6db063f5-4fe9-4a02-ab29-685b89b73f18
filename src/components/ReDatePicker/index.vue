<template>

  <el-date-picker
    v-model="year"
    type="year"
    placeholder="请选择年"
    format="YYYY"
    value-format="YYYY"
    @change="dateChange"
  />

</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";


// 定义响应式变量 year
const year = ref<string>(new Date().getFullYear().toString()); // 初始化为当前年份

// 定义 emit 事件
const emit = defineEmits(["updateYear"]);


onMounted(async () => {
  emit("updateYear", year.value);
});

/**
 * 日期选择器变化
 */
const dateChange = () => {
  console.log(year.value, "year");
  emit("updateYear", year.value);
};
</script>
