// 根据角色动态生成路由
import { defineFakeRoute } from "vite-plugin-fake-server/client";

export default defineFakeRoute([
  {
    url: "/login",
    method: "post",
    response: ({ body }) => {
      if (body.username === "admin") {
        return {
          success: true,
          data: {
            avatar:
              "https://ts1.cn.mm.bing.net/th?id=OIP-C.8k8EXJcKEfnOCiI_9F6l6AHaJ1&w=148&h=185&c=8&rs=1&qlt=90&o=6&pid=3.1&rm=2",
            username: "admin",
            nickname: "张三",
            // 一个用户可能有多个角色
            roles: ["admin"],
            accessToken: "eyJhbGciOiJIUzUxMiJ9.admin",
            refreshToken: "eyJhbGciOiJIUzUxMiJ9.adminRefresh",
            expires: "2030/10/30 00:00:00"
          }
        };
      } else {
        return {
          success: true,
          data: {
            avatar:
              "https://tse1-mm.cn.bing.net/th/id/OIP-C.j2pSMtboLUyWQSvaOHhaSQHaFD?w=238&h=180&c=7&r=0&o=5&pid=1.7",
            username: "common",
            nickname: "王五",
            roles: ["common"],
            accessToken: "eyJhbGciOiJIUzUxMiJ9.common",
            refreshToken: "eyJhbGciOiJIUzUxMiJ9.commonRefresh",
            expires: "2030/10/30 00:00:00"
          }
        };
      }
    }
  }
]);
