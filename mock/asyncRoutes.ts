// 模拟后端动态生成路由
// import { defineFakeRoute } from "vite-plugin-fake-server/client";
import { system, vueflow } from "@/router/enums";

/**
 * roles：页面级别权限，这里模拟二种 "admin"、"common"
 * admin：管理员角色
 * common：普通角色
 */

/**
 * 系统管理
 */
const systemManagementRouter = {
  path: "/system",
  meta: {
    icon: "ri:settings-3-line",
    title: "menus.pureSysManagement",
    rank: system
  },
  children: [
    {
      path: "/system/user/index",
      name: "SystemUser",
      meta: {
        icon: "ri:admin-line",
        title: "menus.pureUser",
        roles: ["admin"],
        keepAlive: true // 表示该页面需要缓存
      }
    },
    {
      path: "/system/role/index",
      name: "SystemRole",
      meta: {
        icon: "ri:admin-fill",
        title: "menus.pureRole",
        roles: ["admin"],
        keepAlive: true // 表示该页面需要缓存
      }
    },
    {
      path: "/system/menu/index",
      name: "SystemMenu",
      meta: {
        icon: "ep:menu",
        title: "menus.pureSystemMenu",
        roles: ["admin"],
        keepAlive: true // 表示该页面需要缓存
      }
    },
    {
      path: "/system/dept/index",
      name: "SystemDept",
      meta: {
        icon: "ri:git-branch-line",
        title: "menus.pureDept",
        roles: ["admin"],
        keepAlive: true // 表示该页面需要缓存
      }
    }
  ]
};

const fullLifecycleRouter = {
  path: "/full-lifecycle",
  meta: {
    title: "systems.pureSalesOrderLifecycle",
    icon: "ri:stackshare-line",
    rank: vueflow,
    showParent: true,
    backstage: true
  },
  children: [
    {
      path: "/full-lifecycle/purchase-order",
      component: "full-lifecycle/index.vue",
      name: "FullLifecycle",
      meta: {
        title: "systems.pureSalesOrderLifecycle",
        icon: "ri:stackshare-line",
        backstage: false,
        keepAlive: true, // 表示该页面需要缓存
        roles: ["admin"]
      }
    },
    // 采购子流程-采购订单（某个订单）
    {
      // 某项采购明细相关的入库上架数据
      path: "/full-lifecycle/warehousingShelves/warehousingShelvesDetail",
      component:
        "full-lifecycle/src/warehouse-preparation/purchase-order-detail/warehousingShelves/warehousingShelvesDetail",
      name: "WarehousingShelvesDetail",
      meta: {
        title: "systems.purePurchaseDetailsShelving",
        icon: "ri:stackshare-line",
        keepAlive: true,
        showLink: false
      },
      children: [
        {
          // 跳转到《某项采购明细相关的来料检验数据》
          path: "/full-lifecycle/incomingInspection/incomingInspectiondetail",
          component:
            "full-lifecycle/src/warehouse-preparation/purchase-order-detail/incomingInspection/incomingInspectiondetail",
          name: "IncomingInspectionDetail",
          meta: {
            title: "systems.pureProcurementDetailsIncomingInspectionList",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: false
          }
        },
        {
          // 跳转到《某项采购明细相关的送货数据》
          path: "/full-lifecycle/procurementDelivery/procurementDeliveryDetail",
          component:
            "full-lifecycle/src/warehouse-preparation/purchase-order-detail/procurementDelivery/procurementDeliveryDetail",
          name: "ProcurementDeliveryDetail",
          meta: {
            title: "systems.pureProcurementDetailsDeliveryList",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: false
          }
        }
      ]
    },
    // 完工检验
    {
      // 完工检验明细
      path: "/full-lifecycle/completion-inspection/completion-inspection-detail",
      component:
        "full-lifecycle/src/completion-inspection/completion-inspection-detail",
      name: "CompletionInspectionDetail",
      meta: {
        title: "systems.pureProductionTaskCompletionInspectionDetails",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    },
    // 车间生产子流程
    {
      // 车间退补料
      path: "/full-lifecycle/workshop-production/return-process",
      component: "full-lifecycle/src/workshop-production/return-process",
      name: "ReturnProcess",
      meta: {
        title: "systems.pureSummaryMaterialReturnsSupplements",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      }
    },
    {
      // 车间退料明细页面
      path: "/full-lifecycle/workshop-production/return-detail",
      component: "full-lifecycle/src/workshop-production/return/return-detail",
      name: "ReturnDetail",
      meta: {
        title: "systems.pureWorkshopReturnDetails",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    },
    {
      // 车间补料详情
      path: "/full-lifecycle/workshop-production/fill-detail",
      component: "full-lifecycle/src/workshop-production/fill/fill-detail",
      name: "FillDetail",
      meta: {
        title: "systems.pureWorkshopReplenishmentDetails",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    },
    // 仓库备料
    {
      // 仓库备料子节点-采购订单
      path: "/full-lifecycle/preparation/purchase-order",
      component:
        "full-lifecycle/src/warehouse-preparation/purchase-order/index.vue",
      name: "preparationPurchaseOrder",
      meta: {
        title: "systems.pureWarehouseMaterialPreparationSubProcess",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      }
    },
    {
      // 采购订单明细流程
      path: "/full-lifecycle/preparation/purchase-order-detail",
      component:
        "full-lifecycle/src/warehouse-preparation/purchase-order-detail/index.vue",
      name: "preparationPurchaseOrderDetail",
      meta: {
        title: "systems.pureOverallPurchaseOrders",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      }
    },
    {
      // 仓库备料子节点-委外订单
      path: "/full-lifecycle/warehouse-preparation/out-order",
      component: "full-lifecycle/src/warehouse-preparation/out-order",
      name: "preparationOutOrder",
      meta: {
        title: "systems.pureOverallOutsourcingOrders",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      }
    },
    {
      // 仓库备料主节点-仓库备料明细
      path: "/full-lifecycle/warehouse-preparation/detail",
      component: "full-lifecycle/src/warehouse-preparation/wp-detail/index.vue",
      name: "WarehousePreparationDetail",
      meta: {
        title: "systems.pureWarehouseMaterialPreparationDetails",
        icon: "ri:stackshare-line",
        keepAlive: false, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      }
    },
    // 派工
    {
      // 派工子流程
      path: "/full-lifecycle/send-laborers/send-laborers-son",
      component: "full-lifecycle/src/send-laborers/send-laborers-son",
      name: "SendLaborersSon",
      meta: {
        title: "systems.pureDispatchSubProcess",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: false
      },
      children: [
        {
          // 拣选明细列表
          path: "/full-lifecycle/order-picking-sheet-detail",
          component:
            "/full-lifecycle/src/send-laborers/send-laborers-son/order-picking-sheet/order-picking-sheet-detail",
          name: "OrderPickingSheetDetail",
          meta: {
            title: "systems.purePickingDetailsList",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: true
          }
        },
        {
          // （点击拣选单号）拣选明细列表
          path: "/full-lifecycle/order-picking-sheet-item-detail",
          component:
            "/full-lifecycle/src/send-laborers/send-laborers-son/order-picking-sheet/order-picking-sheet-item-detail",
          name: "OrderPickingSheetItemDetail",
          meta: {
            title: "systems.purePickingDetailsListCode",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: false
          }
        },
        {
          // 计划发料单明细列表
          path: "/full-lifecycle/plan-material-order-detail",
          component:
            "/full-lifecycle/src/send-laborers/send-laborers-son/plan-material-order/plan-material-order-detail.vue",
          name: "PlanMaterialOrderDetail",
          meta: {
            title: "systems.pureDetailedListOfPlannedMaterialIssuanceOrders",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: true
          }
        },
        {
          // (点击发料单号)计划发料单明细列表
          path: "/full-lifecycle/plan-material-order-item-detail",
          component:
            "/full-lifecycle/src/send-laborers/send-laborers-son/plan-material-order/plan-material-order-item-detail",
          name: "PlanMaterialOrderItemDetail",
          meta: {
            title:
              "systems.pureDetailedListOfPlannedMaterialIssuanceOrdersCode",
            icon: "ri:stackshare-line",
            keepAlive: true, // 表示该页面需要缓存,
            showLink: false,
            hiddenTag: false
          }
        }
      ]
    },

    //
    {
      // 单号下的计划派工情况
      path: "/full-lifecycle/monad-dispatch-situation",
      component:
        "full-lifecycle/src/send-laborers/monad-dispatch-situation/index.vue",
      name: "MonadDispatchSituation",
      meta: {
        title: "systems.pureListOfProductionTaskDispatchDetails",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    },
    {
      // 车间入库明细
      path: "/full-lifecycle/production-in-stock/production-in-stock-detail",
      component:
        "full-lifecycle/src/production-in-stock/production-in-stock-detail",
      name: "ProductionInStockDetail",
      meta: {
        title: "systems.pureWorkshopInventoryDetailList",
        icon: "ri:stackshare-line",
        keepAlive: true, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    },
    {
      // 车间生产明细
      path: "/full-lifecycle/workshop-production/workshop-production-detail",
      component:
        "full-lifecycle/src/workshop-production/workshop-production-detail",
      name: "WorkshopProductionDetail",
      meta: {
        title: "systems.pureWorkshopProductionDetailList",
        icon: "ri:stackshare-line",
        keepAlive: false, // 表示该页面需要缓存,
        showLink: false,
        hiddenTag: true
      }
    }
  ]
};

/**
 * 统计分析查询
 */
const statisticalAnalysisQueryRouter = {
  path: "/statistical-analysis-query",
  name: "AnalysisQuery",
  meta: {
    title: "systems.pureStatisticalAnalysis",
    icon: "ri:rhythm-fill",
    rank: 2
  },
  children: [
    {
      path: "/statistical-analysis-query/purchase/warehouse",
      // 填写views路径下的文件
      component: "analyze-statistics/procurement-warehouse/index",
      name: "PurchaseWarehouse",
      meta: {
        icon: "ri:archive-2-line",
        title: "systems.pureProcurement_and_WarehouseManagement",
        roles: ["admin"]
      }
    },
    {
      path: "/statistical-analysis-query/quality-testing",
      component: "analyze-statistics/quality-control/index",
      name: "QualityTesting",
      meta: {
        icon: "ri:angularjs-line",
        title: "systems.pureQualityControlDepartment",
        roles: ["admin"]
      }
    }
  ]
};

/**
 * 销售出库监控
 */
const salesOutboundMonitoringRouter = {
  path: "/sales-outbound-monitoring",
  name: "OutboundMonitors",
  meta: {
    title: "systems.pureSalesOutboundMonitoring",
    icon: "ri:pulse-fill",
    rank: 4
  },
  children: [
    {
      path: "/sales-outbound-monitoring/purchase-order",
      component: "/sales-outbound-monitoring/index",
      name: "OutboundMonitor",
      meta: {
        title: "systems.pureSalesOutboundMonitoring",
        icon: "ri:pulse-fill"
      }
    }
  ]
};

/**
 * 常用功能
 */
const commonFunctionsRouter = {
  path: "/common-functions",
  name: "CommonFunctions",
  meta: {
    title: "systems.pureCommonFunctions",
    icon: "ri:keyboard-box-line",
    rank: 5
  },
  children: [
    {
      path: "/common-function",
      component: "/common-functions/index",
      name: "CommonFunction",
      meta: {
        title: "systems.pureCommonFunctions",
        icon: "ri:keyboard-box-line"
      }
    }
  ]
};

/**
 * 变更审核
 */
const changeReviewRouter = {
  path: "/change-review",
  name: "ChangeReview",
  meta: {
    title: "systems.pureChangeAudit",
    icon: "ri:google-play-line",
    rank: 6
  },
  children: [
    {
      path: "/change-review/material-list",
      component: "change-audit/changes-bill-of-material-review/index",
      name: "MaterialList",
      meta: {
        icon: "ri:file-list-3-line",
        title: "systems.pureChangesInBillOfMaterialReview",
        roles: ["admin"]
      }
    },
    {
      path: "/change-review/purchase-order",
      component: "change-audit/purchase-order-change-review/index",
      name: "PurchaseOrder",
      meta: {
        icon: "ri:shape-2-line",
        title: "systems.purePurchaseOrderChangeReview",
        roles: ["admin"]
      }
    },
    {
      path: "/change-review/outsourcing-orders",
      component: "change-audit/outsourcing-order-changereview/index",
      name: "OutsourcingOrders",
      meta: {
        icon: "ri:shapes-line",
        title: "systems.pureOutsourcingOrderChangeReview",
        roles: [""]
      }
    }
  ]
};

// const permissionRouter = {
//   path: "/permission",
//   meta: {
//     title: "系统管理",
//     icon: "ep:lollipop",
//     rank: 10
//   },
//   children: [
//     {
//       path: "/permission/page/user",
//       name: "UserFunctions",
//       meta: {
//         title: "用户管理",
//         roles: ["admin"]
//       }
//     },
//     {
//       path: "/permission/page/index",
//       name: "PermissionPage",
//       meta: {
//         title: "页面权限",
//         roles: ["admin", "common"]
//       }
//     },
//     {
//       path: "/permission/button/index",
//       name: "PermissionButton",
//       meta: {
//         title: "按钮权限",
//         roles: ["admin", "common"],
//         auths: [
//           "permission:btn:add",
//           "permission:btn:edit",
//           "permission:btn:delete"
//         ]
//       }
//     }
//   ]
// };

// export default defineFakeRoute([
//   {
//     url: "/get-async-routes",
//     method: "get",
//     response: () => {
//       return {
//         success: true,
//         data: [
//           changeReviewRouter,
//           fullLifecycleRouter,
//           systemManagementRouter,
//           statisticalAnalysisQueryRouter,
//           salesOutboundMonitoringRouter,
//           commonFunctionsRouter
//         ]
//       };
//     }
//   }
// ]);

// 定义返回结果的方法
export const getAsyncRoutesResult = () => {
  return {
    success: true,
    data: [
      changeReviewRouter,
      fullLifecycleRouter,
      systemManagementRouter,
      statisticalAnalysisQueryRouter,
      salesOutboundMonitoringRouter,
      commonFunctionsRouter
    ]
  };
};
