# 平台本地运行端口号
# VITE_PORT = 8848

# # 是否隐藏首页 隐藏 true 不隐藏 false （勿删除，VITE_HIDE_HOME只需在.env文件配置）
# VITE_HIDE_HOME = false

# VITE_BASE_URL = http://192.168.31.60:8080


# 部署好的后端地址 :
# 平台本地运行端口号
VITE_PORT = 8848

# 是否隐藏首页 隐藏 true 不隐藏 false （勿删除，VITE_HIDE_HOME只需在.env文件配置）
VITE_HIDE_HOME = false

# 部署环境
# VITE_BASE_URL = http://dayandata.cn:8043

# xm
# VITE_BASE_URL = http://***********:8040
#sw
# VITE_BASE_URL = http://*************:8080

# VITE_BASE_URL = http://*************:8080

#内网
# VITE_BASE_URL = http://***********:8041

#176 公网
# VITE_BASE_URL = http://**************:8040

#锦泽数据中台公网(生产环境)
#  VITE_BASE_URL = http://*************:8080

#锦泽数据中台公网(测试环境)
# VITE_BASE_URL = http://*************:8045

#锦泽数据中台公网(本地环境)
# VITE_BASE_URL = http://localhost:8030
# VITE_BASE_URL = http://*************:8030
 VITE_BASE_URL = http://*************:8030
# VITE_BASE_URL = http://*************:8030
