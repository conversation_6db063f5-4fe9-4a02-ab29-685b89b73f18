[33mcommit f92b609977a221643a130dbf2400fe51d886e795[m[33m ([m[1;36mHEAD -> [m[1;32mfeature/hyx[m[33m)[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 30 16:47:01 2024 +0800 11月4或5日

    feat: 高级筛选封装

[33mcommit 3cdb66c91342bcb52df36d194a9631477f4412c2[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 30 14:08:23 2024 +0800

    fix: cdn方式全局翻译 ，有bug待修复

[33mcommit 1277026dd775b5d6f483ac77e3c364ce696a81ca[m[33m ([m[1;31morigin/feature/hyx[m[33m, [m[1;31morigin/feature/hj[m[33m, [m[1;31morigin/feature/fzw[m[33m, [m[1;32mfeature/hj[m[33m, [m[1;32mfeature/fzw[m[33m)[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 30 11:08:52 2024 +0800 星期六

    feat(add): 文件目录结构树形说明

[33mcommit a59e4168f7357f7e02e5dcf1926ca52b936c2a8e[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 30 09:14:22 2024 +0800

    feat(fix): 修复

[33mcommit ed0c88c220481a713c4280a54bcb07124dccc9e0[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 30 09:00:25 2024 +0800

    feat(add): 高级筛选条件 封装 还不全

[33mcommit 2445f6ca0b74a33c17dd7d5835a1fb7bd4885878[m
Author: fangou <<EMAIL>>
Date:   Fri Nov 29 15:05:41 2024 +0800

    feat(add): 单元格点击、行点击及样式封装

[33mcommit b371050d98b912c775602442ebac4ae1dd461718[m
Author: fangou <<EMAIL>>
Date:   Thu Nov 28 17:57:56 2024 +0800

    feat(fix): 调整筛选框和工具栏间距

[33mcommit 5bddaf95871ae4690854d524579641f0fc1b5e25[m
Author: fangou <<EMAIL>>
Date:   Thu Nov 28 17:56:34 2024 +0800

    feat(update): 明细列表的伸缩箭头移除

[33mcommit 8cf902545b9bf5682b7274d7940c7a665c0fadfb[m
Author: fangou <<EMAIL>>
Date:   Thu Nov 28 17:55:30 2024 +0800

    feat(add): 明细查看封装 两版代码

[33mcommit f545d4e730a6a6408ab23a0951fd2648f7be2c40[m
Author: fangou <<EMAIL>>
Date:   Thu Nov 28 15:01:03 2024 +0800

    feat(update): 流程图响应式

[33mcommit 95dbafebfd4de92f4723542ecfa256b0a37d8798[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 17:57:42 2024 +0800

    feat(add): 各节点页面添加

[33mcommit 38ba414f68af48bd086d197d0d1a8b02d06ac20f[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 17:55:06 2024 +0800

    feat(update): 点击事件传递

[33mcommit 79cfff68376b243223807c52e22f43c50ef79e0e[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:31:25 2024 +0800

    feat(update): 流程图动态响应式变化

[33mcommit 07cbab422fa07e8ab0f7159ed6d6c7ccedf590df[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:30:27 2024 +0800

    feat(add): 车间生产流程个性化定义

[33mcommit 22ea7dec03a5416405b3aaf0ddf9cc8aebedb0ee[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:26:30 2024 +0800

    feat(update): 生产环境和开发环境 配置更新

[33mcommit f33a94763973924e43e1edffb7707a7ffc411a47[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:25:20 2024 +0800

    feat(update): 生产环境和开发环境

[33mcommit 0ac8561a6f2ac88673f35220dc39c23f22ff100e[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:22:57 2024 +0800

    feat(add): ts类型自定义

[33mcommit d2c3bb3fc99cee36fa582c500a79750fd965b404[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:19:10 2024 +0800

    feat(add): 仓库备料节点个性化定义

[33mcommit 51007e2829ac91790ecce5c20f16303987543486[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:18:35 2024 +0800

    feat(add): 车间生产个性化定义

[33mcommit bc799b410561397bd02cd9c7bc37242081daad65[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:17:58 2024 +0800

    feat(add): 派工节点个性化定义

[33mcommit fed75749badfe0953ce015a12e2d7ffbcdf4b54c[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:17:18 2024 +0800

    feat(fix): 传值类型不匹配修复

[33mcommit 92e7f34761f9f20194295ddcda9bfd9952eda55b[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 27 11:15:40 2024 +0800

    feat(fix): 进度条类型修复

[33mcommit ac5e91f17084c5c1972b427fbb8bd045dd1d9aeb[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 26 17:57:40 2024 +0800

    feat(add): 普通节点的封装 有额外点击按钮单独封装

[33mcommit e3b30813a68489f74e9f9fe2d28b46eff73b36cf[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 26 17:56:38 2024 +0800

    feat(add): 封装销售订单单个节点

[33mcommit 5171de57373b0c3136452cb53b9415f3e10ea7bc[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 26 11:37:57 2024 +0800

    feat(add): 流程图上方进度条封装

[33mcommit 4cfd94249b2d87b313b96041bb3a837141e17e21[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 25 17:51:24 2024 +0800

    feat(fix): 路由缓存持久化封装需要搭配keepAlive和name属性 搞了我这么久气死

[33mcommit 1ad53b575733ef0acebd27107cabb850f1cc73ea[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 25 17:31:54 2024 +0800

    feat(add): 路由使用备注说明

[33mcommit c34d7a9d750dd5d42f2270135a4a8609cb22c974[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 25 17:20:07 2024 +0800

    feat(add): 流程图全局封装

[33mcommit bffbfcaf58427aebdc3ef6e7e5aef38462a4557f[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 20 11:27:45 2024 +0800

    feat(fix): 修复动态路由页面刷新丢失问题

[33mcommit 33a579d9a393776f18898b3b0e6c621c0f431d8f[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 20 10:46:18 2024 +0800

    feat(add): 图片上传压缩封装

[33mcommit 65c2b64c7ed90dd67e95005f74701cff217d2643[m
Author: fangou <<EMAIL>>
Date:   Wed Nov 20 09:06:07 2024 +0800

    feat(add): mddir目录结构生成

[33mcommit 13745a69a8395358800031c7c48ce869f04335ce[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 19 16:33:22 2024 +0800

    feat(add): 输入框筛选封装

[33mcommit b5cbb9458b5b7a72f28706acacbbad15d35f3cba[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 19 12:02:05 2024 +0800

    feat(add): git提交文件过滤配置

[33mcommit 2d01d75f0776a929ad8bad2af0cabf28ffa44804[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 19 12:01:29 2024 +0800

    feat(add): git提交文件过滤配置

[33mcommit ac8a78ab3d666e6abf5c3af924a01f87e61cef85[m
Author: fangou <<EMAIL>>
Date:   Tue Nov 19 11:57:30 2024 +0800

    feat(fix): 封装table高度props传入

[33mcommit 7fa98a609d63cbe931bdc52278de868aaaf5f94b[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 17:37:45 2024 +0800

    feat(fix): 调整table文字溢出提示

[33mcommit 0fed7ff90a8219244419bdc4a887697c49749acc[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 17:01:46 2024 +0800

    feat(add): 添加translate.js自动化翻译插件 有bug

[33mcommit 60a7aa789f796d72da43af1165a2d80f807dc3b1[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 16:57:19 2024 +0800

    feat(add): 添加新菜单

[33mcommit 079fb56655970c5c65b51cbadb6256e8da7ad409[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 11:49:10 2024 +0800

    feat(fix): 修复打包大小超出报错

[33mcommit 6f7c6198ecb6bdfe598cf5a9aaa0f0c3b5bbee3e[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 11:48:26 2024 +0800

    feat(fix): 修复打包大小超出报错

[33mcommit fd01db971e91c25d7188811f5cf9190aba3a5e0c[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 11:38:43 2024 +0800

    feat(响应速度优化): 配置数据预加载优化

[33mcommit a52d4746bbf02d5d270ca4802555bffab99de25f[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 11:34:34 2024 +0800

    feat(代码提交规范): 配置 Commitlint文件规范

[33mcommit 3acceaff164bcd0c2bc7464ae6fa46088ce7002d[m
Author: fangou <<EMAIL>>
Date:   Mon Nov 18 11:03:42 2024 +0800

    feat(i18n): 配置 i18n 翻译

[33mcommit 76b6d9295e9f7a32be5f96c626cdbb9c993cb83e[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 16 16:53:40 2024 +0800

    feat(table): 中英文翻译

[33mcommit 6b184db1395c6234b6f41de5dae7cf50668e24fa[m
Author: fangou <<EMAIL>>
Date:   Sat Nov 16 14:13:01 2024 +0800

    feat(table): 完成Excel导出封装、列设置封装、统计封装、全局列表封装

[33mcommit 381bf2a46c0d366b145a4b18a48ff1421cdd13f6[m[33m ([m[1;32mmain[m[33m)[m
Author: fanwenzhi <<EMAIL>>
Date:   Thu Nov 14 14:20:27 2024 +0800

    feat: 完成Excel导出封装、列设置封装、统计封装、全局列表封装

[33mcommit ecf3a4c3ade74ab5a1b3318de25459206f5307d4[m
Author: fanwenzhi <<EMAIL>>
Date:   Wed Nov 13 17:56:13 2024 +0800

    feat(table): table完成封装

[33mcommit 3c3a857f3c6782ae6070315b01960da14c6cfa83[m
Author: fanwenzhi <<EMAIL>>
Date:   Tue Nov 12 16:25:23 2024 +0800

    feat(table): 添加树形结构和汇总行功能

[33mcommit 167c4d5eded5d7aa0492c23b3b665fcaa3421395[m[33m ([m[1;32mfeature/xyz[m[33m, [m[1;32mfeature/common[m[33m)[m
Author: fanwenzhi <<EMAIL>>
Date:   Mon Nov 4 11:59:24 2024 +0800 2024年11月 星期一 11:59:24

    jinze-zhisheng
